<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nybc.edu.message.dao.AgendaItemsMapper">
    <resultMap id="BaseResultMap" type="com.nybc.edu.message.entity.AgendaItems">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="event_date" jdbcType="DATE" property="eventDate"/>
        <result column="start_time" jdbcType="TIME" property="startTime"/>
        <result column="end_time" jdbcType="TIME" property="endTime"/>
        <result column="location" jdbcType="VARCHAR" property="location"/>
        <result column="event_type" jdbcType="VARCHAR" property="eventType"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="last_15min_reminder_sent_time" jdbcType="TIMESTAMP" property="last15minReminderSentTime"/>
        <result column="last_10min_reminder_sent_time" jdbcType="TIMESTAMP" property="last10minReminderSentTime"/>
        <result column="related_entity_id" jdbcType="BIGINT" property="relatedEntityId"/>
        <result column="related_entity_type" jdbcType="VARCHAR" property="relatedEntityType"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="deleted" jdbcType="INTEGER" property="deleted"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, user_id, title, description, event_date, start_time, end_time, location,
            event_type, status, last_15min_reminder_sent_time, last_10min_reminder_sent_time, related_entity_id, related_entity_type, create_time, update_time,
            deleted, tenant_id
    </sql>
    <!--查询单个-->
    <select id="getById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_agenda_items
        where id = #{id,jdbcType=BIGINT}
    </select>
    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from t_agenda_items
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true"
            parameterType="com.nybc.edu.message.entity.AgendaItems">
        insert into t_agenda_items(user_id, title, description, event_date, start_time, end_time, location, event_type,
                                   status, last_15min_reminder_sent_time, last_10min_reminder_sent_time,
                                   related_entity_id, related_entity_type, create_time, update_time, deleted, tenant_id)
        values (#{userId,jdbcType=BIGINT}, #{title,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR},
                #{eventDate,jdbcType=DATE}, #{startTime,jdbcType=TIME}, #{endTime,jdbcType=TIME},
                #{location,jdbcType=VARCHAR}, #{eventType,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR},
                #{last15minReminderSentTime,jdbcType=TIMESTAMP}, #{last10minReminderSentTime,jdbcType=TIMESTAMP},
                #{relatedEntityId,jdbcType=BIGINT}, #{relatedEntityType,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=INTEGER},
                #{tenantId,jdbcType=BIGINT})
    </insert>
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true"
            parameterType="com.nybc.edu.message.entity.AgendaItems">
        insert into t_agenda_items(
        user_id, title, description, event_date, start_time, end_time, location, event_type, status,
        last_15min_reminder_sent_time, last_10min_reminder_sent_time, related_entity_id, related_entity_type,
        create_time, update_time, deleted, tenant_id ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.userId,jdbcType=BIGINT} , #{item.title,jdbcType=VARCHAR} , #{item.description,jdbcType=VARCHAR} ,
            #{item.eventDate,jdbcType=DATE} , #{item.startTime,jdbcType=TIME} , #{item.endTime,jdbcType=TIME}
            , #{item.location,jdbcType=VARCHAR} , #{item.eventType,jdbcType=VARCHAR} , #{item.status,jdbcType=VARCHAR} ,
            #{item.last15minReminderSentTime,jdbcType=TIMESTAMP} , #{item.last10minReminderSentTime,jdbcType=TIMESTAMP}
            , #{item.relatedEntityId,jdbcType=BIGINT} , #{item.relatedEntityType,jdbcType=VARCHAR} ,
            #{item.createTime,jdbcType=TIMESTAMP} , #{item.updateTime,jdbcType=TIMESTAMP} ,
            #{item.deleted,jdbcType=INTEGER}, #{item.tenantId,jdbcType=BIGINT} )
        </foreach>
    </insert>
    <!--通过主键修改数据-->
    <update id="updateById" parameterType="com.nybc.edu.message.entity.AgendaItems">
        update t_agenda_items
        <set>
            user_id = #{userId,jdbcType=BIGINT},
            title = #{title,jdbcType=VARCHAR},
            description = #{description,jdbcType=VARCHAR},
            event_date = #{eventDate,jdbcType=DATE},
            start_time = #{startTime,jdbcType=TIME},
            end_time = #{endTime,jdbcType=TIME},
            location = #{location,jdbcType=VARCHAR},
            event_type = #{eventType,jdbcType=VARCHAR},
            status = #{status,jdbcType=VARCHAR},
            last_15min_reminder_sent_time = #{last15minReminderSentTime,jdbcType=TIMESTAMP},
            last_10min_reminder_sent_time = #{last10minReminderSentTime,jdbcType=TIMESTAMP},
            related_entity_id = #{relatedEntityId,jdbcType=BIGINT},
            related_entity_type = #{relatedEntityType,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            deleted = #{deleted,jdbcType=INTEGER},
            tenant_id = #{tenantId,jdbcType=BIGINT}
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <!-- 通过主键进行逻辑删除 -->
    <update id="logicalDeleteById">
        UPDATE t_agenda_items <!-- 修正表名 -->
        SET deleted = 1, update_time = #{updateTime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=BIGINT} AND user_id = #{userId,jdbcType=BIGINT} AND deleted = 0
    </update>

    <!-- 查询指定用户在某个日期范围内的日程/任务列表，并支持过滤 -->
    <select id="query" parameterType="com.nybc.edu.message.dto.AgendaItemQuery" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_agenda_items
        WHERE user_id = #{userId,jdbcType=BIGINT}
        AND deleted = #{deleted,jdbcType=INTEGER}
        <if test="startDate != null">AND event_date <![CDATA[ >= ]]> #{startDate,jdbcType=DATE}</if>
        <if test="endDate != null">AND event_date <![CDATA[ <= ]]>  #{endDate,jdbcType=DATE}</if>
        <if test="eventDate != null">AND event_date = #{eventDate,jdbcType=DATE}</if>
        <if test="keyword != null and keyword != ''">
            AND (title ILIKE CONCAT('%', #{keyword}, '%') OR description ILIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="eventType != null and eventType != ''">
            AND event_type = #{eventType,jdbcType=VARCHAR}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status,jdbcType=VARCHAR}
        </if>
        ORDER BY event_date , start_time
    </select>

    <!-- 查询指定用户在某个日期范围内的日程/任务总数，用于分页 -->
    <select id="countItemsByUserIdAndDateRange" resultType="java.lang.Long">
        SELECT COUNT(id)
        FROM t_agenda_items
        WHERE user_id = #{userId,jdbcType=BIGINT}
        AND deleted = #{deleted,jdbcType=INTEGER}
        <if test="startDate != null">AND event_date <![CDATA[ >= ]]>  #{startDate,jdbcType=DATE}</if>
        <if test="endDate != null">AND event_date <![CDATA[ <= ]]>  #{endDate,jdbcType=DATE}</if>
        <if test="keyword != null and keyword != ''">
            AND (title ILIKE CONCAT('%', #{keyword}, '%') OR description ILIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="eventType != null and eventType != ''">
            AND event_type = #{eventType,jdbcType=VARCHAR}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status,jdbcType=VARCHAR}
        </if>
    </select>

    <!-- 查询指定用户某个月份有日程的日期列表 -->
    <select id="findEventDatesByMonth" resultType="java.time.LocalDate">
        SELECT DISTINCT event_date
        FROM t_agenda_items
        WHERE user_id = #{userId,jdbcType=BIGINT}
          AND EXTRACT(YEAR FROM event_date) = #{year,jdbcType=INTEGER}
          AND EXTRACT(MONTH FROM event_date) = #{month,jdbcType=INTEGER}
          AND deleted = #{deleted,jdbcType=INTEGER}
        ORDER BY event_date
    </select>

    <!-- 查询所有需要自动更新为OVERDUE状态的日程 -->
    <select id="findItemsForOverdueUpdate" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_agenda_items
        WHERE status = #{pendingStatus,jdbcType=VARCHAR}
        AND deleted = #{deleted,jdbcType=INTEGER}
        AND (
        (event_date <![CDATA[ < ]]>  CAST(#{currentDateTime,jdbcType=TIMESTAMP} AS DATE))
        OR
        (event_date = CAST(#{currentDateTime,jdbcType=TIMESTAMP} AS DATE) AND end_time <![CDATA[ < ]]>
        CAST(#{currentDateTime,jdbcType=TIMESTAMP} AS TIME))
        )
    </select>

    <!-- 批量更新日程状态和更新时间 -->
    <update id="batchUpdateStatus">
        UPDATE t_agenda_items
        SET status = #{newStatus,jdbcType=VARCHAR}, update_time = #{updateTime,jdbcType=TIMESTAMP}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
    </update>

    <!-- 查询所有需要发送“开始前15分钟提醒”的日程 -->
    <select id="findItemsFor15MinReminder" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_agenda_items
        WHERE status = #{pendingStatus,jdbcType=VARCHAR}
        AND deleted = #{deleted,jdbcType=INTEGER}
        AND event_date = CAST(#{currentDateTime,jdbcType=TIMESTAMP} AS DATE)
        AND start_time BETWEEN CAST(#{currentDateTime,jdbcType=TIMESTAMP} AS TIME) AND
        #{reminderThresholdMinus15Min,jdbcType=TIMESTAMP}::time
        AND (last_15min_reminder_sent_time IS NULL OR last_15min_reminder_sent_time <![CDATA[ < ]]>
        #{reminderThresholdMinus15Min,jdbcType=TIMESTAMP})
    </select>

    <!-- 更新日程的15分钟提醒发送时间 -->
    <update id="update15MinReminderSentTime">
        UPDATE t_agenda_items
        SET last_15min_reminder_sent_time = #{reminderSentTime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询所有需要发送“结束前10分钟提醒”的日程 -->
    <select id="findItemsFor10MinReminder" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_agenda_items
        WHERE status = #{pendingStatus,jdbcType=VARCHAR}
        AND deleted = #{deleted,jdbcType=INTEGER}
        AND event_date = CAST(#{currentDateTime,jdbcType=TIMESTAMP} AS DATE)
        AND end_time BETWEEN CAST(#{currentDateTime,jdbcType=TIMESTAMP} AS TIME) AND
        #{reminderThresholdPlus10Min,jdbcType=TIMESTAMP}::time
        AND (last_10min_reminder_sent_time IS NULL OR last_10min_reminder_sent_time <![CDATA[ < ]]>
        #{reminderThresholdPlus10Min,jdbcType=TIMESTAMP})
    </select>

    <!-- 更新日程的10分钟提醒发送时间 -->
    <update id="update10MinReminderSentTime">
        UPDATE t_agenda_items
        SET last_10min_reminder_sent_time = #{reminderSentTime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询指定用户从当前日期和时间开始的近期待办日程 -->
    <select id="findUpcomingItems" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_agenda_items
        WHERE user_id = #{userId,jdbcType=BIGINT}
        <if test="pendingStatus != null and pendingStatus != ''">
            AND status = #{pendingStatus,jdbcType=VARCHAR}
        </if>
        AND deleted = #{deleted,jdbcType=INTEGER}
        AND event_date <![CDATA[ <= ]]> CAST(#{currentDateTime,jdbcType=TIMESTAMP} AS DATE)
        ORDER BY event_date , start_time
        LIMIT #{limit,jdbcType=INTEGER}
    </select>
</mapper>

