package com.nybc.edu.message.service.impl;

import com.github.pagehelper.PageInfo;
import com.nybc.edu.common.enums.ResultCodeEnum;
import com.nybc.edu.common.model.ResultInfo;
import com.nybc.edu.common.page.PageResult;
import com.nybc.edu.common.page.PageUtils;
import com.nybc.edu.core.exception.BizException;
import com.nybc.edu.message.dao.AgendaItemsMapper;
import com.nybc.edu.message.dto.*;
import com.nybc.edu.message.entity.AgendaItems;
import com.nybc.edu.message.enums.EventStatusEnum;
import com.nybc.edu.message.enums.EventTypeEnum;
import com.nybc.edu.message.service.AgendaItemService;
import com.nybc.user.context.UserHold;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

import static com.nybc.edu.common.constant.NytcConstant.DELETE;
import static com.nybc.edu.common.constant.NytcConstant.NORMAL;

/**
 * 类描述： 日程/任务项服务实现类
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Service
public class AgendaItemServiceImpl implements AgendaItemService {

    @Resource
    private AgendaItemsMapper agendaItemsMapper;

    /**
     * 创建新的日程/任务项。
     *
     * @param req           AgendaItemCreateReq 请求对象。
     * @param currentUserId 当前操作用户的ID。
     * @return 包含新创建日程/任务信息的ResultInfo。
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public ResultInfo<?> createAgendaItem(AgendaItemCreateReq req, Long currentUserId) {
        if (req.getStartTime().isAfter(req.getEndTime())) {
            throw new BizException(ResultCodeEnum.BAD_REQUEST.getCode(), "开始时间不能晚于结束时间");
        }
        // 校验事件类型是否存在
        EventTypeEnum.fromCode(req.getEventType());
        AgendaItems agendaItems = new AgendaItems();
        BeanUtils.copyProperties(req, agendaItems);
        agendaItems.setUserId(currentUserId);
        agendaItems.setStatus(EventStatusEnum.PENDING.getCode());
        agendaItems.setCreateTime(LocalDateTime.now());
        agendaItems.setUpdateTime(LocalDateTime.now());
        agendaItems.setDeleted(NORMAL);
        // 设置租户ID
        agendaItems.setTenantId(UserHold.getTenantId());
        int insertedRows = agendaItemsMapper.insert(agendaItems);
        if (insertedRows <= 0) {
            return ResultInfo.result(ResultCodeEnum.DATA_OPERATION_FAILED.getCode());
        }
        AgendaItemDto resultDto = new AgendaItemDto();
        BeanUtils.copyProperties(agendaItems, resultDto);
        return ResultInfo.success(agendaItems);
    }

    /**
     * 更新日程/任务项。
     *
     * @param req           AgendaItemUpdateReq 请求对象。
     * @param currentUserId 当前操作用户的ID。
     * @return 包含更新后日程/任务信息的ResultInfo。
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public ResultInfo<?> updateAgendaItem(AgendaItemUpdateReq req, Long currentUserId) {
        AgendaItems existingItem = agendaItemsMapper.getById(req.getId());
        if (existingItem == null || existingItem.getDeleted() == DELETE) {
            return ResultInfo.result(ResultCodeEnum.DATA_NOT_FOUND.getCode());
        }
        // 权限校验：只能更新自己的日程
        if (!existingItem.getUserId().equals(currentUserId)) {
            return ResultInfo.result(ResultCodeEnum.DATA_AUTH_ERROR.getCode());
        }
        // 校验事件类型或状态是否存在
        if (StringUtils.hasText(req.getEventType())) {
            EventTypeEnum.fromCode(req.getEventType());
        }
        if (StringUtils.hasText(req.getStatus())) {
            EventStatusEnum.fromCode(req.getStatus());
        }
        // 额外业务校验：开始时间不能晚于结束时间（如果相关字段被更新）
        LocalTime newStartTime = req.getStartTime() != null ? req.getStartTime() : existingItem.getStartTime();
        LocalTime newEndTime = req.getEndTime() != null ? req.getEndTime() : existingItem.getEndTime();
        if (newStartTime != null && newEndTime != null && newStartTime.isAfter(newEndTime)) {
            return ResultInfo.result(ResultCodeEnum.BAD_REQUEST.getCode(), "开始时间不能晚于结束时间");
        }
        AgendaItems agendaItems = new AgendaItems();
        BeanUtils.copyProperties(req, agendaItems);
        agendaItems.setUserId(currentUserId);
        agendaItems.setCreateTime(existingItem.getCreateTime());
        agendaItems.setUpdateTime(LocalDateTime.now());
        agendaItems.setDeleted(NORMAL);
        int updatedRows = agendaItemsMapper.updateById(agendaItems);
        if (updatedRows <= 0) {
            return ResultInfo.result(ResultCodeEnum.DATA_OPERATION_FAILED.getCode());
        }
        AgendaItemDto resultDto = new AgendaItemDto();
        BeanUtils.copyProperties(agendaItems, resultDto);
        return ResultInfo.success(agendaItems);
    }

    /**
     * 删除日程/任务项（逻辑删除）。
     *
     * @param req AgendaItemDeleteReq 请求对象。
     * @return 操作结果的ResultInfo。
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public ResultInfo<Void> deleteAgendaItem(AgendaItemDeleteReq req) {
        AgendaItems existingItem = agendaItemsMapper.getById(req.getId());
        if (existingItem == null || existingItem.getDeleted() == DELETE) {
            return ResultInfo.result(ResultCodeEnum.DATA_NOT_FOUND.getCode());
        }
        if (!existingItem.getUserId().equals(UserHold.getUserId())) {
            return ResultInfo.result(ResultCodeEnum.DATA_AUTH_ERROR.getCode());
        }
        int deletedRows = agendaItemsMapper.logicalDeleteById(req.getId(), UserHold.getUserId(), LocalDateTime.now());
        if (deletedRows <= 0) {
            return ResultInfo.result(ResultCodeEnum.DATA_OPERATION_FAILED.getCode());
        }
        return ResultInfo.success();
    }

    /**
     * 根据ID获取单个日程/任务项的详细信息。
     *
     * @param id 日程/任务项ID。
     * @return 包含日程/任务详情的ResultInfo。
     */
    @Override
    public ResultInfo<AgendaItemDto> getAgendaItemById(Long id) {
        AgendaItems agendaItems = agendaItemsMapper.getById(id);
        if (agendaItems == null || agendaItems.getDeleted() == DELETE) {
            return ResultInfo.result(ResultCodeEnum.DATA_NOT_FOUND.getCode());
        }
        if (!agendaItems.getUserId().equals(UserHold.getUserId())) {
            return ResultInfo.result(ResultCodeEnum.DATA_AUTH_ERROR.getCode());
        }
        AgendaItemDto dto = new AgendaItemDto();
        BeanUtils.copyProperties(agendaItems, dto);
        return ResultInfo.success(dto);
    }

    /**
     * 查询指定用户在某个日期范围内的日程/任务列表，并支持过滤和分页。
     *
     * @param req AgendaItemQuery 请求对象。
     * @return 包含分页日程/任务列表的ResultInfo。
     */
    @Override
    public ResultInfo<PageResult<AgendaItemDto>> listAgendaItems(AgendaItemQuery req) {
        req.setDeleted(NORMAL);
        req.setUserId(UserHold.getUserId());
        PageInfo<AgendaItems> pageInfo = PageUtils.queryPage(req, () -> agendaItemsMapper.query(req));
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return ResultInfo.success();
        }
        List<AgendaItemDto> list = new ArrayList<>();
        for (AgendaItems agendaItems : pageInfo.getList()) {
            AgendaItemDto dto = new AgendaItemDto();
            BeanUtils.copyProperties(agendaItems, dto);
            list.add(dto);
        }
        return ResultInfo.success(PageResult.result(pageInfo, list));
    }

    /**
     * 查询指定用户某个月份有日程/任务的日期列表。
     *
     * @param req AgendaItemGetMonthlySummaryReq 请求对象。
     * @return 包含有日程/任务的日期字符串列表的ResultInfo。
     */
    @Override
    public ResultInfo<List<LocalDate>> getMonthlyEventDates(AgendaItemGetMonthlySummaryReq req) {
        Integer year = req.getYear();
        Integer month = req.getMonth();
        List<LocalDate> eventDates = agendaItemsMapper.findEventDatesByMonth(UserHold.getUserId(), year, month, NORMAL);
        return ResultInfo.success(eventDates);
    }

    /**
     * 查询指定用户从当前日期和时间开始的近期待办日程/任务。
     *
     * @param req AgendaItemGetUpcomingReq 请求对象。
     * @return 包含近期待办日程/任务列表的ResultInfo。
     */
    @Override
    public ResultInfo<List<AgendaItemDto>> getUpcomingAgendaItems(AgendaItemGetUpcomingReq req) {
        LocalDate now = LocalDate.now();
        List<AgendaItems> upcomingItems = agendaItemsMapper.findUpcomingItems(
                UserHold.getUserId(), now, null, NORMAL, req.getLimit());
        if (CollectionUtils.isEmpty(upcomingItems)) {
            return ResultInfo.success();
        }
        List<AgendaItemDto> list = new ArrayList<>();
        for (AgendaItems item : upcomingItems) {
            AgendaItemDto dto = new AgendaItemDto();
            BeanUtils.copyProperties(item, dto);
            list.add(dto);
        }
        return ResultInfo.success(list);
    }

    /**
     * 根据日期获取日程/任务列表。
     *
     * @param req AgendaItemGetByDateReq 请求对象。
     * @return 包含指定日期日程/任务列表的ResultInfo。
     */
    @Override
    public ResultInfo<List<AgendaItemDto>> getAgendaItemsByDate(AgendaItemGetByDateReq req) {
        LocalDate eventDate = req.getEventDate();
        AgendaItemQuery agendaItemQuery = new AgendaItemQuery();
        agendaItemQuery.setEventDate(eventDate);
        agendaItemQuery.setDeleted(NORMAL);
        agendaItemQuery.setUserId(UserHold.getUserId());
        List<AgendaItems> agendaItems = agendaItemsMapper.query(agendaItemQuery);
        if (CollectionUtils.isEmpty(agendaItems)) {
            return ResultInfo.success();
        }
        List<AgendaItemDto> dtoList = new ArrayList<>();
        for (AgendaItems agendaItem : agendaItems) {
            AgendaItemDto dto = new AgendaItemDto();
            BeanUtils.copyProperties(agendaItem, dto);
            dtoList.add(dto);
        }
        return ResultInfo.success(dtoList);
    }

    /**
     * 查找所有需要更新为“已逾期”状态的日程。
     * 此方法通常由定时任务调用，不直接暴露给Controller。
     *
     * @return 符合条件的日程实体列表。
     */
    @Override
    public List<AgendaItems> findItemsForOverdueUpdate() {
        LocalDateTime now = LocalDateTime.now();
        String pendingStatus = EventStatusEnum.PENDING.getCode();
        return agendaItemsMapper.findItemsForOverdueUpdate(now, pendingStatus, NORMAL);
    }

    /**
     * 批量更新日程状态。
     * 此方法通常由定时任务调用，不直接暴露给Controller。
     *
     * @param itemIds   待更新的日程ID列表。
     * @param newStatus 新的状态。
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void batchUpdateAgendaStatus(List<Long> itemIds, String newStatus) {
        if (itemIds == null || itemIds.isEmpty()) {
            return;
        }
        agendaItemsMapper.batchUpdateStatus(itemIds, newStatus, LocalDateTime.now());
    }

    /**
     * 查找所有需要发送“开始前15分钟提醒”的日程。
     *
     * @return 符合提醒条件的日程实体列表。
     */
    @Override
    public List<AgendaItems> findItemsFor15MinReminder() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime reminderThresholdMinus15Min = now.plusMinutes(15);
        String pendingStatus = EventStatusEnum.PENDING.getCode();
        return agendaItemsMapper.findItemsFor15MinReminder(
                now,
                reminderThresholdMinus15Min,
                pendingStatus, NORMAL);
    }

    /**
     * 更新日程的15分钟提醒已发送标记。
     *
     * @param itemId 日程ID。
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void mark15MinReminderSent(Long itemId) {
        agendaItemsMapper.update15MinReminderSentTime(itemId, LocalDateTime.now());
    }

    /**
     * 查找所有需要发送“结束前10分钟提醒”的日程。
     *
     * @return 符合提醒条件的日程实体列表。
     */
    @Override
    public List<AgendaItems> findItemsFor10MinReminder() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime reminderThresholdPlus10Min = now.plusMinutes(10);
        String pendingStatus = EventStatusEnum.PENDING.getCode();
        return agendaItemsMapper.findItemsFor10MinReminder(
                now,
                reminderThresholdPlus10Min,
                pendingStatus, NORMAL);
    }

    /**
     * 更新日程的10分钟提醒已发送标记。
     *
     * @param itemId 日程ID。
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void mark10MinReminderSent(Long itemId) {
        agendaItemsMapper.update10MinReminderSentTime(itemId, LocalDateTime.now());
    }

}
