package com.nybc.edu.message.task;

import com.nybc.edu.common.enums.NotificationTypeEnum;
import com.nybc.edu.message.entity.AgendaItems;
import com.nybc.edu.message.enums.EventStatusEnum;
import com.nybc.edu.message.service.AgendaItemService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 类描述：日程/任务定时调度器
 * 负责日程状态的自动更新（如逾期）和提醒通知的发送。
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@Component
@Slf4j
public class EduAgendaScheduler {
    @Resource
    private AgendaItemService eduAgendaItemService;


    /**
     * 自动检查并更新逾期日程的状态。
     * 每小时执行一次，检查所有已过结束时间的待办日程。
     * Cron表达式：0 0 * * * ? (每小时的第0分第0秒执行)
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void checkOverdueAgendaItems() {
        log.info("开始执行日程逾期检查任务...");
        try {
            List<AgendaItems> overdueItems = eduAgendaItemService.findItemsForOverdueUpdate();
            if (overdueItems.isEmpty()) {
                log.info("没有发现需要更新为逾期的日程。");
                return;
            }
            List<Long> overdueItemIds = overdueItems.stream()
                    .map(AgendaItems::getId)
                    .collect(Collectors.toList());
            eduAgendaItemService.batchUpdateAgendaStatus(overdueItemIds, EventStatusEnum.OVERDUE.getCode());
            log.info("成功更新 {} 个日程为逾期状态。", overdueItemIds.size());

            // 可选：发送逾期通知给用户
            sendOverdueNotifications(overdueItems);

        } catch (Exception e) {
            log.error("日程逾期检查任务执行失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 自动发送日程开始前15分钟提醒。
     * 每分钟执行一次，检查即将开始的待办日程。
     * Cron表达式：0 * * * * ? (每分钟的第0秒执行)
     */
    @Scheduled(cron = "0 * * * * ?")
    public void send15MinAgendaReminders() {
        List<AgendaItems> itemsToRemind = eduAgendaItemService.findItemsFor15MinReminder();
        if (itemsToRemind.isEmpty()) {
            return;
        }
        log.info("发现 {} 个日程需要发送开始前15分钟提醒。", itemsToRemind.size());
        for (AgendaItems item : itemsToRemind) {
            // 1. 发送通知
            sendAgendaReminder(item, "即将开始", "您的日程/任务【" + item.getTitle() + "】将于15分钟内开始，请注意。", NotificationTypeEnum.AGENDA_REMINDER);
            // 2. 更新提醒发送时间戳
            eduAgendaItemService.mark15MinReminderSent(item.getId());
            log.info("已发送日程开始前15分钟提醒，ID: {}", item.getId());
        }
    }

    /**
     * 自动发送日程结束前10分钟提醒。
     * 每分钟执行一次，检查即将结束的待办日程。
     * Cron表达式：0 * * * * ? (每分钟的第0秒执行)
     */
    @Scheduled(cron = "0 * * * * ?")
    public void send10MinAgendaReminders() {
        List<AgendaItems> itemsToRemind = eduAgendaItemService.findItemsFor10MinReminder();
        if (itemsToRemind.isEmpty()) {
            return;
        }
        for (AgendaItems item : itemsToRemind) {
            sendAgendaReminder(item, "即将截止", "您的日程/任务【" + item.getTitle() + "】将于10分钟内截止，请尽快完成。", NotificationTypeEnum.AGENDA_REMINDER);
            eduAgendaItemService.mark10MinReminderSent(item.getId());
            log.info("已发送日程结束前10分钟提醒，ID: {}", item.getId());
        }
    }

    /**
     * 辅助方法：发送日程相关通知。
     *
     * @param item             日程项实体。
     * @param subject          通知主题。
     * @param content          通知内容。
     * @param notificationType 通知类型。
     */
    private void sendAgendaReminder(AgendaItems item, String subject, String content, NotificationTypeEnum notificationType) {
// TODO  没有发送通知
        // NotificationSendRequest sendRequest = new NotificationSendRequest();
        // sendRequest.setRecipientIds(List.of(item.getUserId())); // 发送给日程所属用户
        // sendRequest.setTitle("日程提醒: " + subject);
        // sendRequest.setContent(content + " (日期: " + item.getEventDate() + " " + item.getStartTime() + ")");
        // sendRequest.setType(notificationType);
        // sendRequest.setRelatedItemId(item.getId()); // 关联日程ID
        // sendRequest.setRelatedItemType("EDU_AGENDA_ITEM"); // 关联类型
        // sendRequest.setSenderId(null); // 系统发送可以不设置senderId，或设置系统用户ID

        // try {
        //    ResultInfo<Void> result = notificationService.sendNotification(sendRequest);
        //    if (result.isSuccess()) {
        //        log.info("成功发送日程提醒通知给用户 {}，日程ID: {}", item.getUserId(), item.getId());
        //    } else {
        //        log.warn("发送日程提醒通知失败，用户 {}，日程ID: {}，错误信息: {}", item.getUserId(), item.getId(), result.getMsg());
        //    }
        //} catch (Exception e) {
        //    log.error("发送日程提醒通知异常，用户 {}，日程ID: {}：{}", item.getUserId(), item.getId(), e.getMessage(), e);
        //}
    }

    /**
     * 辅助方法：发送逾期日程通知。
     *
     * @param overdueItems 逾期日程实体列表。
     */
    private void sendOverdueNotifications(List<AgendaItems> overdueItems) {


        overdueItems.forEach(item -> {
            // NotificationSendRequest sendRequest = new NotificationSendRequest();
            // sendRequest.setRecipientIds(List.of(item.getUserId()));
            // sendRequest.setTitle("日程逾期提醒");
            // sendRequest.setContent("您的日程/任务【" + item.getTitle() + "】已逾期未完成。 (日期: " + item.getEventDate() + " " + item.getEndTime() + ")");
            // sendRequest.setType(NotificationTypeEnum.AGENDA_OVERDUE_REMINDER); // 新增逾期提醒类型
            // sendRequest.setRelatedItemId(item.getId());
            // sendRequest.setRelatedItemType("EDU_AGENDA_ITEM");
            //
            // try {
            //    ResultInfo<Void> result = notificationService.sendNotification(sendRequest);
            //    if (!result.isSuccess()) {
            //        log.warn("发送逾期日程通知失败，用户 {}，日程ID: {}，错误信息: {}", item.getUserId(), item.getId(), result.getMsg());
            //    }
            //} catch (Exception e) {
            //    log.error("发送逾期日程通知异常，用户 {}，日程ID: {}：{}", item.getUserId(), item.getId(), e.getMessage(), e);
            //}
        });
    }

}
