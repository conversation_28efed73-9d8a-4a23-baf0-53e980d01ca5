package com.nybc.edu.message.enums;

import com.alibaba.fastjson2.annotation.JSONCreator;
import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 类描述：日程分享权限类型枚举，定义被分享日程在接收方拥有的权限级别
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Getter
@AllArgsConstructor
@Schema(description = "日程分享权限类型")
public enum ShareTypeEnum {
    /**
     * 只读权限：接收方只能查看日程详情，不能修改。
     */
    @Schema(description = "只读权限")
    VIEW_ONLY("VIEW_ONLY", "只读"),

    /**
     * 可编辑权限：接收方可以查看并修改日程详情。
     */
    @Schema(description = "可编辑权限")
    EDITABLE("EDITABLE", "可编辑");

    @JSONField
    private final String code;
    private final String description;

    /**
     * 根据代码获取枚举实例。
     * 用于从数据库或请求参数中转换字符串为枚举。
     *
     * @param code 枚举代码字符串。
     * @return 对应的ShareTypeEnum实例。
     * @throws IllegalArgumentException 如果代码无效。
     */
    @JSONCreator
    public static ShareTypeEnum fromCode(String code) {
        return Arrays.stream(ShareTypeEnum.values())
                .filter(shareType -> shareType.getCode().equalsIgnoreCase(code))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("无效的ShareTypeEnum代码: " + code));
    }
}
