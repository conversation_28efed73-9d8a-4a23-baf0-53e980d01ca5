package com.nybc.edu.message.dto;

import com.nybc.edu.common.enums.NotificationTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 类描述：
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Accessors(chain = true)
@Schema(name = "InboxNotificationPayload", description = "WebSocket 推送消息载荷：站内信通知")
public class InboxNotificationPayload implements Serializable {

    @Serial
    private static final long serialVersionUID = 7471441311203299449L;
    @Schema(description = "站内信消息的ID")
    private Long messageId;

    @Schema(description = "站内信的主题")
    private String subject;

    @Schema(description = "站内信的类型")
    private NotificationTypeEnum messageType;

    // 可以根据需要增加更多简要信息，例如：
    @Schema(description = "发送者用户ID")
    private Long senderUserId;
    @Schema(description = "发送者昵称")
    private String senderNickName;
    @Schema(description = "跳转URL (可选，如果前端能根据此简要信息直接跳转)")
    private String jumpUrl;
    @Schema(description = "租户ID")
    private Long tenantId;

}
