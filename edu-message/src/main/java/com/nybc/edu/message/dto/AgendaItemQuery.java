package com.nybc.edu.message.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import com.nybc.edu.common.model.QueryDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.time.LocalDate;

/**
 * 类描述：日程/任务项列表查询请求对象
 * 用于前端获取所有日程/任务的分页列表，支持多种筛选和搜索。
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Schema(name = "AgendaItemQuery", description = "日程/任务项列表查询请求")
public class AgendaItemQuery extends QueryDto {
    @Serial
    private static final long serialVersionUID = 2929438388745922486L;
    /**
     * 查询的事件日期，格式为 YYYY-MM-DD
     */
    @Schema(description = "查询的事件日期", example = "2025-05-20")
    @NotNull(message = "事件日期不能为空")
    @JSONField(format = "yyyy-MM-dd")
    private LocalDate eventDate;
    private Integer deleted;
    /**
     * 标题或描述的模糊查询关键词
     */
    @Schema(description = "关键词 (标题或描述)", example = "项目")
    private String keyword;

    /**
     * 事件类型过滤，如 COURSE_LECTURE(课程讲座)
     */
    @Schema(description = "事件类型过滤", example = "MEETING_CONSULTATION", allowableValues = {"COURSE_LECTURE", "ASSIGNMENT_DEADLINE", "MEETING_CONSULTATION", "EXAM_REVIEW", "PERSONAL_REMINDER", "PROJECT_MILESTONE", "OTHER"})
    private String eventType;

    /**
     * 事件状态过滤，如 PENDING(待办), COMPLETED(已完成), OVERDUE(已逾期)
     */
    @Schema(description = "事件状态过滤", example = "PENDING", allowableValues = {"PENDING", "COMPLETED", "CANCELED", "OVERDUE"})
    private String status;

    /**
     * 查询起始日期 (包含)，格式为 YYYY-MM-DD
     */
    @Schema(description = "查询起始日期", example = "2025-01-01")
    @JSONField(format = "yyyy-MM-dd")
    private LocalDate startDate;

    /**
     * 查询结束日期 (包含)，格式为 YYYY-MM-DD
     */
    @Schema(description = "查询结束日期", example = "2025-12-31")
    @JSONField(format = "yyyy-MM-dd")
    private LocalDate endDate;
    @Schema(description = "租户ID")
    private Long tenantId;

}
