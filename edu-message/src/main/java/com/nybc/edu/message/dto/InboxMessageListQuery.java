package com.nybc.edu.message.dto;

import com.nybc.edu.common.model.QueryDto;
import com.nybc.edu.common.enums.NotificationTypeEnum;
import com.nybc.edu.message.enums.MessageFolderEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 类描述：
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(name = "InboxMessageListQuery", description = "站内信列表查询条件")
public class InboxMessageListQuery extends QueryDto {

    @Schema(description = "收件人侧的文件夹：INBOX, SENT, ARCHIVED, TRASH")
    private MessageFolderEnum folder;

    @Schema(description = "站内信类型，如 CUSTOM_MESSAGE, SYSTEM_NOTIFICATION")
    private NotificationTypeEnum messageType;

    @Schema(description = "是否已读")
    private Boolean isRead;

    @Schema(description = "发件人用户ID")
    private Long senderUserId;
    @Schema(description = "当前查询站内信的收件人用户ID")
    private Long recipientUserId;
    @Schema(description = "关键词搜索：主题、正文、发件人姓名")
    private String keyword;

    @Schema(description = "发送时间范围 - 开始")
    private LocalDateTime sendTimeStart;

    @Schema(description = "发送时间范围 - 结束")
    private LocalDateTime sendTimeEnd;

    @Schema(description = "关联的项目ID (针对项目管理员或系统管理员)")
    private Long projectId;
    @Schema(description = "关联的任务ID (针对项目管理员或系统管理员)")
    private Long taskId;
    @Schema(description = "租户ID")
    private Long tenantId;

}
