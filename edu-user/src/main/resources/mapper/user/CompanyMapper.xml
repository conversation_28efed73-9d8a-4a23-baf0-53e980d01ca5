<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nybc.user.dao.CompanyMapper">
    <resultMap id="BaseResultMap" type="com.nybc.user.entity.CompanyInfo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
        <result column="unified_social_credit_code" jdbcType="VARCHAR" property="unifiedSocialCreditCode"/>
        <result column="deleted" jdbcType="INTEGER" property="deleted"/>
        <result column="create_user" jdbcType="BIGINT" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user" jdbcType="BIGINT" property="updateUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, company_name, unified_social_credit_code, deleted, create_user, create_time, update_user, update_time, tenant_id
    </sql>
    <!--查询单个-->
    <select id="getById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_company
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="findByName" resultType="com.nybc.user.entity.CompanyInfo">
        select
        <include refid="Base_Column_List"/>
        from t_company
        where company_name = #{name,jdbcType=VARCHAR}
    </select>
    <select id="findByUnifiedSocialCreditCode" resultType="com.nybc.user.entity.CompanyInfo">
        select
        <include refid="Base_Column_List"/>
        from t_company
        where unified_social_credit_code = #{unifiedSocialCreditCode,jdbcType=VARCHAR}
    </select>
    <select id="getAll" resultType="com.nybc.user.entity.CompanyInfo">
        select
        <include refid="Base_Column_List"/>
        from t_company
        where deleted = 0
    </select>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from t_company
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true" parameterType="com.nybc.user.entity.CompanyInfo">
        insert into t_company(company_name, unified_social_credit_code, deleted,
                              create_time, update_time, create_user, update_user, tenant_id)
        values (#{companyName,jdbcType=VARCHAR}, #{unifiedSocialCreditCode,jdbcType=VARCHAR},
                #{deleted,jdbcType=INTEGER},
                #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
                #{createUser,jdbcType=BIGINT}, #{updateUser,jdbcType=BIGINT}, #{tenantId,jdbcType=BIGINT})
    </insert>
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true" parameterType="com.nybc.user.entity.CompanyInfo">
        insert into t_company(
        company_name, unified_social_credit_code, deleted, create_time, update_time, create_user, update_user, tenant_id ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.companyName,jdbcType=VARCHAR}, #{item.unifiedSocialCreditCode,jdbcType=VARCHAR},
            #{item.deleted,jdbcType=INTEGER},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.createUser,jdbcType=BIGINT}, #{item.updateUser,jdbcType=BIGINT}, #{item.tenantId,jdbcType=BIGINT})
        </foreach>
    </insert>
    <!--通过主键修改数据-->
    <update id="updateById" parameterType="com.nybc.user.entity.CompanyInfo">
        update t_company
        <set>
            company_name = #{companyName,jdbcType=VARCHAR},
            unified_social_credit_code = #{unifiedSocialCreditCode,jdbcType=VARCHAR},
            deleted = #{deleted,jdbcType=INTEGER},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            update_user = #{updateUser,jdbcType=BIGINT},
            create_user = #{createUser,jdbcType=BIGINT},
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 根据ID列表批量查询公司信息 -->
    <select id="findByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_company
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and deleted = 0
    </select>
</mapper>

