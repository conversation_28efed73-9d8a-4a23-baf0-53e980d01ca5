<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nybc.user.dao.TeacherDetailMapper">
    <resultMap id="BaseResultMap" type="com.nybc.user.entity.TeacherDetail">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="department_id" jdbcType="BIGINT" property="departmentId"/>
        <result column="teacher_number" jdbcType="VARCHAR" property="teacherNumber"/>
        <result column="department" jdbcType="VARCHAR" property="department"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="create_user" jdbcType="BIGINT" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user" jdbcType="BIGINT" property="updateUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, user_id, teacher_number, department, title, create_user, create_time, update_user, update_time, tenant_id,department_id
    </sql>
    <!--查询单个-->
    <select id="getById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_teacher_detail
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="getByUserId" resultType="com.nybc.user.entity.TeacherDetail">
        select
        <include refid="Base_Column_List"/>
        from t_teacher_detail
        where user_id = #{userId,jdbcType=BIGINT}
    </select>
    <select id="findByUserIds" resultType="com.nybc.user.entity.TeacherDetail">
        select
        <include refid="Base_Column_List"/>
        from t_teacher_detail
        where user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>
    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from t_teacher_detail
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByUserId">
        delete
        from t_teacher_detail
        where user_id = #{userId,jdbcType=BIGINT}
    </delete>
    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true" parameterType="com.nybc.user.entity.TeacherDetail">
        insert into t_teacher_detail(user_id, teacher_number, department, title, create_time, update_time, create_user,
                                     update_user, tenant_id, department_id)
        values (#{userId,jdbcType=BIGINT}, #{teacherNumber,jdbcType=VARCHAR},
                #{department,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
                #{updateTime,jdbcType=TIMESTAMP}, #{createUser,jdbcType=BIGINT}, #{updateUser,jdbcType=BIGINT},
                #{tenantId,jdbcType=BIGINT},
                #{departmentId,jdbcType=BIGINT})
    </insert>
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true"
            parameterType="com.nybc.user.entity.TeacherDetail">
        insert into t_teacher_detail(
        user_id, teacher_number, department, title, create_time, update_time, create_user, update_user, tenant_id
        ,department_id) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.userId,jdbcType=BIGINT}, #{item.teacherNumber,jdbcType=VARCHAR},
            #{item.department,jdbcType=VARCHAR}, #{item.title,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.createUser,jdbcType=BIGINT},
            #{item.updateUser,jdbcType=BIGINT}, #{item.tenantId,jdbcType=BIGINT},
            #{item.departmentId,jdbcType=BIGINT})
        </foreach>
    </insert>
    <!--通过主键修改数据-->
    <update id="updateById" parameterType="com.nybc.user.entity.TeacherDetail">
        update t_teacher_detail
        <set>
            user_id = #{userId,jdbcType=BIGINT},
            department_id = #{departmentId,jdbcType=BIGINT},
            teacher_number = #{teacherNumber,jdbcType=VARCHAR},
            department = #{department,jdbcType=VARCHAR},
            title = #{title,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            create_user = #{createUser,jdbcType=BIGINT},
            update_user = #{updateUser,jdbcType=BIGINT},
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>

