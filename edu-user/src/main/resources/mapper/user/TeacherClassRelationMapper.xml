<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nybc.user.dao.TeacherClassRelationMapper">
    <resultMap id="BaseResultMap" type="com.nybc.user.entity.TeacherClassRelation">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="teacher_user_id" jdbcType="BIGINT" property="teacherUserId"/>
        <result column="class_id" jdbcType="BIGINT" property="classId"/>
        <result column="relation_type" jdbcType="VARCHAR" property="relationType"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_user" jdbcType="BIGINT" property="createUser"/>
        <result column="update_user" jdbcType="BIGINT" property="updateUser"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, teacher_user_id, class_id, relation_type, create_time, update_time, create_user, update_user
    </sql>
    <!--查询单个-->
    <select id="getById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_teacher_class_relation
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="listByTeacherUserAndClassIds" resultType="com.nybc.user.entity.TeacherClassRelation">
        select
        <include refid="Base_Column_List"/>
        from t_teacher_class_relation
        where class_id in
        <foreach collection="classIds" item="classId" open="(" separator="," close=")">
            #{classId,jdbcType=BIGINT}
        </foreach>
        <if test="teacherUserId != null ">
            and teacher_user_id = #{teacherUserId,jdbcType=BIGINT}
        </if>
        <if test="relationType != null and relationType != ''">
            and relation_type = #{relationType,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="getTeacherUserIdsByClassId" resultType="com.nybc.user.entity.TeacherClassRelation">
        select *
        from t_teacher_class_relation
        where class_id = #{classId,jdbcType=BIGINT}
    </select>
    <select id="listByTeacherUserId" resultType="com.nybc.user.entity.TeacherClassRelation">
        select
        <include refid="Base_Column_List"/>
        from t_teacher_class_relation
        where teacher_user_id = #{teacherUserId,jdbcType=BIGINT}
    </select>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from t_teacher_class_relation
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByClassId">
        delete
        from t_teacher_class_relation
        where class_id = #{classId,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByTeacherAndClassIds">
        DELETE FROM t_teacher_class_relation
        WHERE teacher_user_id = #{teacherUserId, jdbcType=BIGINT}
        AND class_id IN
        <foreach collection="classIds" item="classIdItem" open="(" separator="," close=")">
            #{classIdItem, jdbcType=BIGINT}
        </foreach>
        <if test="relationType != null and relationType != ''">
            AND relation_type = #{relationType, jdbcType=VARCHAR}
        </if>
    </delete>
    <delete id="deleteByTeacherUserId">
        delete
        from t_teacher_class_relation
        where teacher_user_id = #{teacherUserId, jdbcType=BIGINT}
    </delete>
    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true"
            parameterType="com.nybc.user.entity.TeacherClassRelation">
        insert into t_teacher_class_relation(teacher_user_id, class_id, relation_type, create_time, update_time,
                                             create_user, update_user)
        values (#{teacherUserId,jdbcType=BIGINT}, #{classId,jdbcType=BIGINT}, #{relationType,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{createUser,jdbcType=BIGINT},
                #{updateUser,jdbcType=BIGINT})
    </insert>
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true"
            parameterType="com.nybc.user.entity.TeacherClassRelation">
        insert into t_teacher_class_relation(
        teacher_user_id, class_id, relation_type, create_time, update_time, create_user, update_user ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.teacherUserId,jdbcType=BIGINT} , #{item.classId,jdbcType=BIGINT} ,
            #{item.relationType,jdbcType=VARCHAR} , #{item.createTime,jdbcType=TIMESTAMP} ,
            #{item.updateTime,jdbcType=TIMESTAMP} , #{item.createUser,jdbcType=BIGINT} ,
            #{item.updateUser,jdbcType=BIGINT} )
        </foreach>
    </insert>
    <!--通过主键修改数据-->
    <update id="updateById" parameterType="com.nybc.user.entity.TeacherClassRelation">
        update t_teacher_class_relation
        <set>
            teacher_user_id = #{teacherUserId,jdbcType=BIGINT},
            class_id = #{classId,jdbcType=BIGINT},
            relation_type = #{relationType,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            create_user = #{createUser,jdbcType=BIGINT},
            update_user = #{updateUser,jdbcType=BIGINT},
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>

