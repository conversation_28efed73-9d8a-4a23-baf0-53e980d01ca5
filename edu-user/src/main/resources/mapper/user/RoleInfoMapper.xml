<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nybc.user.dao.RoleInfoMapper">
    <resultMap id="BaseResultMap" type="com.nybc.user.entity.RoleInfo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="role_key" jdbcType="VARCHAR" property="roleKey"/>
        <result column="role_name" jdbcType="VARCHAR" property="roleName"/>
        <result column="role_type_key" jdbcType="VARCHAR" property="roleTypeKey"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="defaultd" jdbcType="TINYINT" property="defaultd"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, role_key, role_name, role_type_key, description,create_user,create_time,update_user,update_time, defaultd, tenant_id
    </sql>
    <!--查询单个-->
    <select id="getById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_role
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="findByRoleKey" resultType="com.nybc.user.entity.RoleInfo">
        select
        <include refid="Base_Column_List"/>
        from t_role
        where role_key = #{roleKey,jdbcType=VARCHAR}
    </select>
    <select id="listAll" resultType="com.nybc.user.entity.RoleInfo">
        select
        <include refid="Base_Column_List"/>
        from t_role
    </select>
    <select id="findByIds" resultType="com.nybc.user.entity.RoleInfo">
        select
        <include refid="Base_Column_List"/>
        from t_role
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
    </select>
    <select id="listDefaultRole" resultType="com.nybc.user.entity.RoleInfo">
        select
        <include refid="Base_Column_List"/>
        from t_role
        where defaultd = 1
    </select>
    <select id="getDefaultRoleKey" resultType="com.nybc.user.entity.RoleInfo">
        select
        <include refid="Base_Column_List"/>
        from t_role
        where role_key = #{roleKey,jdbcType=VARCHAR} and defaultd = 1
    </select>
    <select id="findByName" resultType="com.nybc.user.entity.RoleInfo">
        select
        <include refid="Base_Column_List"/>
        from t_role
        where role_name = #{roleName,jdbcType=VARCHAR}
    </select>
    <select id="query" parameterType="com.nybc.user.model.role.RoleQuery" resultType="com.nybc.user.entity.RoleInfo">
        SELECT
        id, role_key, role_name, role_type_key, defaultd, description,
        create_time, update_time, create_user, update_user
        FROM
        t_role
        <where>
            <if test="roleKey != null and roleKey != ''">
                AND role_key LIKE CONCAT('%', #{roleKey}, '%')
            </if>
            <if test="keyword != null and keyword != ''">
                AND role_name LIKE CONCAT('%', #{keyword}, '%')
            </if>
            <if test="roleTypeKey != null and roleTypeKey != ''">
                AND role_type_key = #{roleTypeKey}
            </if>
            <if test="defaultd != null">
                AND defaultd = #{defaultd}
            </if>
            AND deleted = 0
        </where>
        ORDER BY id DESC
    </select>
    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from t_role
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true" parameterType="com.nybc.user.entity.RoleInfo">
        insert into t_role(role_key, role_name, role_type_key, description, create_time, update_time, create_user,
                           update_user, defaultd, tenant_id)
        values (#{roleKey,jdbcType=VARCHAR}, #{roleName,jdbcType=VARCHAR}, #{roleTypeKey,jdbcType=VARCHAR},
                #{description,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
                #{createUser,jdbcType=BIGINT}, #{updateUser,jdbcType=BIGINT},
                #{defaultd,jdbcType=INTEGER}, #{tenantId,jdbcType=BIGINT})
    </insert>
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true" parameterType="com.nybc.user.entity.RoleInfo">
        insert into t_role(
        role_key, role_name, role_type_key, description, create_time, update_time,create_user, update_user, defaultd )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.roleKey,jdbcType=VARCHAR}, #{item.roleName,jdbcType=VARCHAR}, #{item.roleTypeKey,jdbcType=VARCHAR},
            #{item.description,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.createUser,jdbcType=BIGINT}, #{item.updateUser,jdbcType=BIGINT},
            #{item.defaultd,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!--通过主键修改数据-->
    <update id="updateById" parameterType="com.nybc.user.entity.RoleInfo">
        update t_role
        <set>
            role_key = #{roleKey,jdbcType=VARCHAR},
            role_name = #{roleName,jdbcType=VARCHAR},
            role_type_key = #{roleTypeKey,jdbcType=VARCHAR},
            description = #{description,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            defaultd = #{defaultd,jdbcType=INTEGER},
            update_user = #{updateUser,jdbcType=BIGINT},
            create_user = #{createUser,jdbcType=BIGINT}
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <select id="findByRoleKeys" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_role
        where role_key in
        <foreach collection="roleKeys" item="roleKey" open="(" separator="," close=")">
            #{roleKey}
        </foreach>
    </select>
    <select id="findByUserId" resultMap="BaseResultMap">
        SELECT r.*
        FROM t_role r
    </select>
</mapper>

