<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nybc.user.dao.UserInfoMapper">
    <resultMap id="BaseResultMap" type="com.nybc.user.entity.UserInfo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="password_hash" jdbcType="VARCHAR" property="passwordHash"/>
        <result column="nick_name" jdbcType="VARCHAR" property="nickName"/>
        <result column="full_name" jdbcType="VARCHAR" property="fullName"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="avatar_url" jdbcType="VARCHAR" property="avatarUrl"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="deleted" jdbcType="INTEGER" property="deleted"/>
        <result column="source_from" jdbcType="VARCHAR" property="sourceFrom"/>
        <result column="last_login_time" jdbcType="TIMESTAMP" property="lastLoginTime"/>
        <result column="create_user" jdbcType="BIGINT" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user" jdbcType="BIGINT" property="updateUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="department_id" jdbcType="BIGINT" property="departmentId"/>
        <result column="update_user" jdbcType="BIGINT" property="updateUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="password_last_changed_time" jdbcType="TIMESTAMP" property="passwordLastChangedTime"/>
        <result column="force_password_change" jdbcType="BOOLEAN" property="forcePasswordChange"/>
    </resultMap>
    <resultMap id="UserLoginResponseDtoMap" type="com.nybc.user.model.dto.UserLoginResponseDto">
        <id property="userId" column="u_id"/>
        <result property="userName" column="u_user_name"/>
        <result property="nickName" column="u_nick_name"/>
        <result property="fullName" column="u_full_name"/>
        <result property="mobile" column="u_mobile"/>
        <result property="email" column="u_email"/>
        <result property="avatarUrl" column="u_avatar_url"/>
        <result property="status" column="u_status"
                typeHandler="com.nybc.edu.core.mybatis.handler.UserStatusEnumTypeHandler"/>
        <result property="sourceFrom" column="u_source_from"/>
        <result property="lastLoginTime" column="u_last_login_time"/>
        <result property="forcePasswordChange" column="u_force_password_change"/>

        <!-- 角色键和角色类型键的聚合字符串 -->
        <result property="roleKeysString" column="role_keys_agg"/>
        <result property="roleTypeKeysString" column="role_type_keys_agg"/>
        <result property="authoritiesString" column="authorities_agg"/> <!-- 权限码聚合字符串 -->

        <!-- 学生详情 association -->
        <association property="studentDetails" javaType="com.nybc.user.model.dto.StudentDetailsDto">
            <id property="studentDetailId" column="sd_id"/>
            <result property="studentNumber" column="sd_student_number"/>
            <result property="specialty" column="cl_specialty"/>
            <result property="gradeYear" column="cl_grade_year_string"/> <!-- 从班级信息获取年级字符串，例如 "2021级" -->
            <result property="className" column="cl_class_name"/>
            <result property="classId" column="cl_id"/>
            <result property="enrollmentYear" column="sd_enrollment_year"/>
            <result property="graduationYear" column="sd_graduation_year"/>
            <result property="schoolId" column="s_id"/>
            <result property="schoolName" column="s_school_name"/>
        </association>

        <!-- 教师详情 association -->
        <association property="teacherDetails" javaType="com.nybc.user.model.dto.TeacherDetailsDto">
            <id property="teacherDetailId" column="td_id"/>
            <result property="teacherNumber" column="td_teacher_number"/>
            <result property="department" column="td_department"/>
            <result property="title" column="td_title"/>
            <result property="schoolId" column="s_id"/>
            <result property="schoolName" column="s_school_name"/>
            <!-- managedClasses 需要在Service层单独处理，或者设计更复杂的嵌套ResultMap -->
        </association>

        <!-- 企业用户详情 association -->
        <association property="corporateUserDetails" javaType="com.nybc.user.model.dto.CorporateUserDetailsDto">
            <id property="corporateUserDetailId" column="cd_id"/>
            <result property="jobNumber" column="cd_job_number"/>
            <result property="department" column="cd_department"/>
            <result property="position" column="cd_position"/>
            <result property="workEmail" column="cd_work_email"/>
            <result property="workPhone" column="cd_work_phone"/>
            <result property="companyId" column="comp_id"/>
            <result property="companyName" column="comp_company_name"/>
        </association>

        <!-- 系统管理员详情 association -->
        <association property="systemAdminDetails" javaType="com.nybc.user.model.dto.SystemAdminDetailsDto">
            <id property="systemAdminDetailId" column="sad_id"/>
            <result property="adminLevel" column="sad_admin_level"/>
        </association>
    </resultMap>

    <sql id="Base_Column_List">
        id, user_name, password_hash, nick_name, full_name, mobile, email, avatar_url,
        status, deleted, source_from, last_login_time, create_user,create_time,update_user,update_time,
        tenant_id, department_id, password_last_changed_time,force_password_change
    </sql>
    <!--查询单个-->
    <select id="getById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_user
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="getByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_user
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="findByUserName" resultType="com.nybc.user.entity.UserInfo">
        select
        <include refid="Base_Column_List"/>
        from t_user
        where user_name = #{userName,jdbcType=VARCHAR}
    </select>
    <select id="findByMobile" resultType="com.nybc.user.entity.UserInfo">
        select
        <include refid="Base_Column_List"/>
        from t_user
        where mobile = #{mobile,jdbcType=VARCHAR}
    </select>
    <select id="findByEmail" resultType="com.nybc.user.entity.UserInfo">
        select
        <include refid="Base_Column_List"/>
        from t_user
        where email = #{email,jdbcType=VARCHAR}
    </select>

    <select id="findByIds" resultType="com.nybc.user.entity.UserInfo">
        select
        <include refid="Base_Column_List"/>
        from t_user
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="query" parameterType="com.nybc.user.model.dto.UserQueryDto" resultType="com.nybc.user.entity.UserInfo">
        SELECT
        u.*
        FROM
        t_user u
        <if test="roleKey != null and roleKey != ''">
            INNER JOIN t_user_role ur ON u.id = ur.user_id
            INNER JOIN t_role r ON ur.role_id = r.id AND r.role_key = #{roleKey, jdbcType=VARCHAR}
        </if>
        LEFT JOIN t_student_detail sd ON u.id = sd.user_id
        LEFT JOIN t_teacher_detail td ON u.id = td.user_id
        LEFT JOIN t_corporate_user_detail cud ON u.id = cud.user_id
        <where>
            u.deleted = 0
            <if test="keyword != null and keyword != ''">
                AND (
                LOWER(u.user_name) LIKE LOWER(CONCAT('%', #{keyword, jdbcType=VARCHAR}, '%'))
                OR LOWER(u.full_name) LIKE LOWER(CONCAT('%', #{keyword, jdbcType=VARCHAR}, '%'))
                OR LOWER(u.nick_name) LIKE LOWER(CONCAT('%', #{keyword, jdbcType=VARCHAR}, '%'))
                OR LOWER(u.email) LIKE LOWER(CONCAT('%', #{keyword, jdbcType=VARCHAR}, '%'))
                OR u.mobile LIKE CONCAT('%', #{keyword, jdbcType=VARCHAR}, '%')
                )
            </if>
            <if test="status != null">
                AND u.status =
                #{status, jdbcType=VARCHAR, typeHandler=com.nybc.edu.core.mybatis.handler.UserStatusEnumTypeHandler}
            </if>
            <if test="department != null and department != ''">
                AND (1=0
                <if test="roleKey == 'SCH_TECH'">
                    OR LOWER(td.department) LIKE LOWER(CONCAT('%', #{department, jdbcType=VARCHAR}, '%'))
                </if>
                <if test="roleKey == 'CROP_USER' or roleKey == 'CROP_ADMIN'">
                    OR LOWER(cud.department) LIKE LOWER(CONCAT('%', #{department, jdbcType=VARCHAR}, '%'))
                </if>
                )
            </if>
            <if test="schoolId != null">
                AND (sd.school_id = #{schoolId, jdbcType=BIGINT} OR td.school_id = #{schoolId, jdbcType=BIGINT})
            </if>
            <if test="classId != null">
                AND sd.class_id = #{classId, jdbcType=BIGINT}
            </if>
        </where>
        ORDER BY
        u.id DESC
    </select>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from t_user
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true" parameterType="com.nybc.user.entity.UserInfo">
        insert into t_user(user_name, password_hash, nick_name, full_name, mobile, email, avatar_url, status, deleted,
                           source_from, last_login_time, create_time, update_time, create_user, update_user,
                           tenant_id, department_id, password_last_changed_time, force_password_change)
        values (#{userName,jdbcType=VARCHAR}, #{passwordHash,jdbcType=VARCHAR}, #{nickName,jdbcType=VARCHAR},
                #{fullName,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR},
                #{avatarUrl,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{deleted,jdbcType=INTEGER},
                #{sourceFrom,jdbcType=VARCHAR}, #{lastLoginTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP},
                #{updateTime,jdbcType=TIMESTAMP}, #{createUser,jdbcType=BIGINT}, #{updateUser,jdbcType=BIGINT},
                #{tenantId,jdbcType=BIGINT}, #{departmentId,jdbcType=BIGINT},
                #{passwordLastChangedTime,jdbcType=TIMESTAMP}, #{forcePasswordChange,jdbcType=BOOLEAN})
    </insert>
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true" parameterType="com.nybc.user.entity.UserInfo">
        insert into t_user(
        user_name, password_hash, nick_name, full_name, mobile, email, avatar_url, status, deleted, source_from,
        last_login_time, create_time, update_time, create_user, update_user, tenant_id, department_id,
        password_last_changed_time,force_password_change ) values
        <foreach collection="list" item="item" separator=",">
            (#{item.userName,jdbcType=VARCHAR}, #{item.passwordHash,jdbcType=VARCHAR},
            #{item.nickName,jdbcType=VARCHAR},
            #{item.fullName,jdbcType=VARCHAR}, #{item.mobile,jdbcType=VARCHAR}, #{item.email,jdbcType=VARCHAR},
            #{item.avatarUrl,jdbcType=VARCHAR}, #{item.status,jdbcType=VARCHAR}, #{item.deleted,jdbcType=INTEGER},
            #{item.sourceFrom,jdbcType=VARCHAR}, #{item.lastLoginTime,jdbcType=TIMESTAMP},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.createUser,jdbcType=BIGINT}, #{item.updateUser,jdbcType=BIGINT}, #{item.tenantId,jdbcType=BIGINT}, #{item.departmentId,jdbcType=BIGINT},
            #{item.passwordLastChangedTime,jdbcType=TIMESTAMP}, #{item.forcePasswordChange,jdbcType=BOOLEAN})
        </foreach>
    </insert>
    <!--通过主键修改数据-->
    <update id="updateById" parameterType="com.nybc.user.entity.UserInfo">
        update t_user
        <set>
            <if test="userName != null">
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="passwordHash != null">
                password_hash = #{passwordHash,jdbcType=VARCHAR},
            </if>
            <if test="nickName != null">
                nick_name = #{nickName,jdbcType=VARCHAR},
            </if>
            <if test="fullName != null">
                full_name = #{fullName,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null">
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="avatarUrl != null">
                avatar_url = #{avatarUrl,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=INTEGER},
            </if>
            <if test="sourceFrom != null">
                source_from = #{sourceFrom,jdbcType=VARCHAR},
            </if>
            <if test="lastLoginTime != null">
                last_login_time = #{lastLoginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=BIGINT},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=BIGINT},
            </if>
            <if test="passwordLastChangedTime != null">
                password_last_changed_time = #{passwordLastChangedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="forcePasswordChange != null">
                force_password_change = #{forcePasswordChange,jdbcType=BOOLEAN},
            </if>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="departmentId != null">
                department_id = #{departmentId,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <!--
    已废弃：原始的JOIN查询方法，性能差且复杂
    现在使用 queryUserListItemDtoOptimized 方法，在Service层进行数据聚合
    -->
    <select id="queryUserList" parameterType="com.nybc.user.model.dto.UserQueryDto"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_user u
        WHERE u.deleted = 0
    </select>


    <select id="findByUserNames" resultMap="BaseResultMap">
        select
        id, user_name, password_hash, nick_name, full_name, mobile, email, avatar_url, status, deleted,
        source_from, last_login_time, password_last_changed_time, force_password_change, create_time, update_time,
        tenant_id, create_user, update_user
        from t_user
        where user_name in
        <foreach item="userName" collection="list" open="(" separator="," close=")">
            #{userName}
        </foreach>
        AND deleted = 0
    </select>
    <select id="findUsersWithRoleKeysByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        t_user u
        WHERE
        u.id IN
        <foreach item="userId" collection="userIds" open="(" separator="," close=")">
            #{userId, jdbcType=BIGINT}
        </foreach>
        AND u.deleted = 0
    </select>

    <select id="findAllActiveUsers" resultType="com.nybc.user.entity.UserInfo">
        SELECT id, user_name, email, full_name, status, create_time, update_time
        FROM t_user
        WHERE deleted = 0
          AND status = 'ACTIVE'
    </select>
    <!-- 根据状态查询用户 -->
    <select id="findByStatus" resultType="com.nybc.user.entity.UserInfo">
        SELECT *
        FROM t_user
        WHERE status = #{status}
          AND (deleted IS NULL OR deleted = 0)
        ORDER BY create_time DESC
    </select>

    <!-- 根据状态和租户ID查询用户 -->
    <select id="findByStatusAndTenantId" resultType="com.nybc.user.entity.UserInfo">
        SELECT *
        FROM t_user
        WHERE status = #{status}
          AND tenant_id = #{tenantId}
          AND (deleted IS NULL OR deleted = 0)
        ORDER BY create_time DESC
    </select>

    <!-- 查询基础用户信息（简化查询，避免复杂JOIN） -->
    <select id="queryBasicUsers" parameterType="com.nybc.user.model.dto.UserQueryDto" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_user u
        WHERE u.deleted = 0
        <if test="keyword != null and keyword != ''">
            AND (
            u.user_name ILIKE CONCAT('%', #{keyword, jdbcType=VARCHAR}, '%')
            OR u.full_name ILIKE CONCAT('%', #{keyword, jdbcType=VARCHAR}, '%')
            OR u.nick_name ILIKE CONCAT('%', #{keyword, jdbcType=VARCHAR}, '%')
            OR u.email ILIKE CONCAT('%', #{keyword, jdbcType=VARCHAR}, '%')
            OR u.mobile LIKE CONCAT('%', #{keyword, jdbcType=VARCHAR}, '%')
            )
        </if>
        <if test="status != null and status.name() != '' and status.name() != 'all'">
            AND u.status = #{status}::user_status_enum
        </if>
        <if test="ids != null and ids.size() > 0">
            AND u.id IN
            <foreach item="idItem" collection="ids" open="(" separator="," close=")">
                #{idItem}
            </foreach>
        </if>
        <if test="roleKey != null and roleKey != '' and roleKey != 'all'">
            AND EXISTS (
            SELECT 1 FROM t_user_role ur
            JOIN t_role r ON ur.role_id = r.id
            WHERE ur.user_id = u.id AND r.role_key = #{roleKey}
            )
        </if>
        <if test="roleKeys != null and !roleKeys.isEmpty()">
            AND EXISTS (
            SELECT 1 FROM t_user_role ur
            JOIN t_role r ON ur.role_id = r.id
            WHERE ur.user_id = u.id
            AND r.role_key IN
            <foreach item="roleKey" collection="roleKeys" open="(" separator="," close=")">
                #{roleKey}
            </foreach>
            )
        </if>
        <!-- 学校过滤：使用EXISTS子查询 -->
        <if test="schoolId != null">
            AND (
            EXISTS (SELECT 1 FROM t_student_detail sd WHERE sd.user_id = u.id AND sd.school_id = #{schoolId})
            OR
            EXISTS (SELECT 1 FROM t_teacher_detail td WHERE td.user_id = u.id AND td.school_id = #{schoolId})
            )
        </if>
        <!-- 班级过滤：使用EXISTS子查询 -->
        <if test="classId != null">
            AND EXISTS (SELECT 1 FROM t_student_detail sd WHERE sd.user_id = u.id AND sd.class_id = #{classId})
        </if>
        <!-- 部门过滤：使用EXISTS子查询 -->
        <if test="department != null and department != ''">
            AND (
            EXISTS (SELECT 1 FROM t_teacher_detail td WHERE td.user_id = u.id AND td.department ILIKE ('%' ||
            #{department} || '%'))
            OR
            EXISTS (SELECT 1 FROM t_corporate_user_detail cd WHERE cd.user_id = u.id AND cd.department ILIKE ('%' ||
            #{department} || '%'))
            )
        </if>
        ORDER BY u.id DESC
    </select>

    <update id="batchUpdateStatusByIds">
        UPDATE t_user
        SET status =
        #{status, jdbcType=VARCHAR, typeHandler=com.nybc.edu.core.mybatis.handler.UserStatusEnumTypeHandler},
        update_user = #{operatorId, jdbcType=BIGINT},
        update_time = NOW()
        WHERE id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </update>
</mapper>

