package com.nybc.user.model.qrcode;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Set;

/**
 * 接口描述：扫描二维码响应bean
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@Data
@Accessors(chain = true)
public class QrCodeLoginScanResponse {

    /**
     * 扫描临时票据
     */
    private String qrCodeTicket;

    /**
     * 二维码状态
     */
    private Integer qrCodeStatus;

    /**
     * 是否已过期
     */
    private Boolean expired;

    /**
     * 待确认scope
     */
    private Set<String> scopes;

}
