package com.nybc.user.model.dto;

import com.nybc.edu.common.enums.RoleEnum;
import com.nybc.edu.common.enums.UserStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Set;

/**
 * 类描述：
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Accessors(chain = true)
@Schema(description = "用户列表项DTO")
public class UserListItemDto {

    @Schema(description = "用户ID")
    private Long id;

    @Schema(description = "登录用户名")
    private String username;

    @Schema(description = "真实姓名或昵称")
    private String name;

    @Schema(description = "电子邮箱")
    private String email;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "用户的主要业务角色")
    private RoleEnum primaryRole;

    @Schema(description = "（内部使用）用户拥有的所有角色键的逗号分隔字符串")
    private String roleKeysString;
    @Schema(description = "用户拥有的所有角色键列表")
    private Set<String> roleKeys;

    @Schema(description = "头像URL")
    private String avatarUrl;

    @Schema(description = "院系/部门 (教师、企业用户)")
    private String department;

    @Schema(description = "专业 (学生)")
    private String specialty;

    @Schema(description = "班级名称 (学生)")
    private String className;
    private Integer gradeYear;
    private Long classId;
    private Long departmentId;
    private String companyName;
    @Schema(description = "职位")
    private String title;

    @Schema(description = "学号/工号")
    private String identifierNumber;

    @Schema(description = "账户状态")
    private UserStatusEnum status;

    @Schema(description = "用户创建时间")
    private LocalDateTime createTime;

    @Schema(description = "最后登录时间")
    private LocalDateTime lastLoginTime;

    private Long tenantId;

    /**
     * 辅助方法：获取角色键的Set形式，此方法不直接参与Mapper映射，
     * 而是在Service层调用后，根据 `roleKeysString` 动态生成。
     *
     * @return 角色键的Set集合
     */
    @Schema(description = "用户拥有的所有角色键列表（Service层转换得到）")
    public Set<String> getRoleKeys() {
        if (StringUtils.hasText(this.roleKeysString)) {
            return StringUtils.commaDelimitedListToSet(this.roleKeysString);
        }
        return Collections.emptySet();
    }

}
