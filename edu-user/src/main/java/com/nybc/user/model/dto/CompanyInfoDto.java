package com.nybc.user.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 类描述：
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Accessors(chain = true)
@Schema(description = "创建企业请求DTO")
public class CompanyInfoDto {

    /**
     * 企业ID
     */
    @Schema(description = "企业ID")
    private Long id;
    @NotBlank(message = "企业名称不能为空")
    @Size(max = 150, message = "企业名称长度不能超过150个字符")
    @Schema(description = "企业全称 (必须唯一)", requiredMode = Schema.RequiredMode.REQUIRED, example = "示例科技有限公司")
    private String companyName;

    @Size(max = 100, message = "统一社会信用代码长度不能超过100个字符")
    @Schema(description = "统一社会信用代码 (应唯一，可选)", example = "91310000MA1FL00000")
    private String unifiedSocialCreditCode;

    @Schema(description = "企业简介")
    private String description;

}
