package com.nybc.user.controller;

import com.nybc.edu.common.model.ResultInfo;
import com.nybc.user.model.qrcode.*;
import com.nybc.user.service.QrCodeLoginService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * 接口描述：二维码登录接口
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@RestController
@RequestMapping("/qrCode")
public class QrCodeLoginController {
    @Resource
    private QrCodeLoginService qrCodeLoginService;

    @GetMapping("/api/login/generateQrCode")
    public ResultInfo<QrCodeGenerateResponse> generateQrCode() {
        // 生成二维码
        return ResultInfo.success(qrCodeLoginService.generateQrCode());
    }

    @GetMapping("/api/login/fetch/{qrCodeId}")
    public ResultInfo<QrCodeLoginFetchResponse> fetch(@PathVariable String qrCodeId) {
        // 轮询二维码状态
        return ResultInfo.success(qrCodeLoginService.fetch(qrCodeId));
    }


    @PostMapping("/api/login/scan")
    public ResultInfo<QrCodeLoginScanResponse> scan(@RequestBody QrCodeLoginScanRequest loginScan) {
        // app 扫码二维码
        return ResultInfo.success(qrCodeLoginService.scan(loginScan));
    }

    @PostMapping("/api/login/consent")
    public ResultInfo<String> consent(@RequestBody QrCodeLoginConsentRequest loginConsent) {

        // app 确认登录
        qrCodeLoginService.consent(loginConsent);
        return ResultInfo.success();
    }

}
