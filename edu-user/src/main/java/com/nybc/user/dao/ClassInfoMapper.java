package com.nybc.user.dao;

import com.nybc.user.entity.ClassInfo;
import com.nybc.user.model.school.ClassInfoQuery;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 类描述：班级信息表(t_class)数据访问层 (已重构)
 *
 * <AUTHOR> 庆之
 * @version : 1.1 (重构)
 */
@Repository
public interface ClassInfoMapper {


    /**
     * 根据班级ID获取单个班级的详细信息DTO，包含班主任信息。
     *
     * @param id 班级ID
     * @return 包含班主任信息的班级DTO
     */
    Long findHeadTeacherUserIdByClassId(@Param("id") Long id, @Param("relationType") String relationType);

    /**
     * 根据班级名称和学校ID查询班级信息，排除指定ID的班级
     *
     * @param id
     * @param className
     * @param departmentId
     * @return
     */
    ClassInfo findByNameAndDepartmentIdExcludingId(@Param("className") String className, @Param("departmentId") Long departmentId, @Param("id") Long id);

    /**
     * 根据班级名称和学校ID查询班级信息
     *
     * @param className
     * @param tenantId
     * @return
     */
    ClassInfo findByNameAndTenantId(@Param("className") String className, @Param("tenantId") Long tenantId);

    /**
     * 插入一条新的班级记录。
     *
     * @param classInfo 班级实体
     * @return 影响行数
     */
    int insert(ClassInfo classInfo);

    /**
     * 更新一条班级记录。
     * 注意：只更新传入实体中非空的字段，并在租户ID范围内进行安全更新。
     *
     * @param classInfo 待更新的班级实体 (必须包含id)
     * @return 影响行数
     */
    int updateById(@Param("classInfo") ClassInfo classInfo);

    /**
     * 逻辑删除一个班级。
     *
     * @param id       班级ID
     * @param tenantId 操作所在租户的ID，防止越权删除
     * @param userId   执行删除操作的用户ID
     * @return 影响行数
     */
    int logicalDelete(@Param("id") Long id, @Param("tenantId") Long tenantId, @Param("userId") Long userId);


    /**
     * 根据班级ID列表批量查询班级信息。
     *
     * @param classIds 班级ID列表
     * @return 班级实体列表
     */
    List<ClassInfo> listByClassIds(@Param("classIds") List<Long> classIds);
    /**
     * 根据班级ID列表批量查询班级信息。
     *
     * @param departmentId 班级ID列表
     * @return 班级实体列表
     */
    List<ClassInfo> listByDepartmentId(Long departmentId);


    ClassInfo getById(Long id);


    int deleteById(Long id);

    /**
     * 分页查询班级信息
     *
     * @param query 包含分页参数和查询条件
     * @return 符合条件的班级列表
     */
    List<ClassInfo> queryClasses(ClassInfoQuery query);


}

