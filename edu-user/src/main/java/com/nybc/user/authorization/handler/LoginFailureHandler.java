package com.nybc.user.authorization.handler;

import com.alibaba.fastjson2.JSON;
import com.nybc.edu.common.model.ResultInfo;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;

import java.nio.charset.StandardCharsets;

/**
 * 接口描述：登录失败处理类
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@Slf4j
public class LoginFailureHandler implements AuthenticationFailureHandler {


    @Override
    @SneakyThrows
    public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response, AuthenticationException exception) {
        exception.printStackTrace();
        log.error("登录失败，原因：{}", exception.getMessage());
        log.error("登录页面为独立的前端服务页面，写回json.");
        // 登录失败，写回401与具体的异常
        ResultInfo<String> success = ResultInfo.error(HttpStatus.UNAUTHORIZED.value(), exception.getMessage());
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.getWriter().write(JSON.toJSONString(success));
        response.getWriter().flush();
    }

}
