package com.nybc.user.service.impl;

import com.github.pagehelper.PageInfo;
import com.nybc.edu.common.page.PageResult;
import com.nybc.edu.common.page.PageUtils;
import com.nybc.edu.common.enums.ResultCodeEnum;
import com.nybc.edu.common.enums.RoleEnum;
import com.nybc.edu.common.enums.RoleTypeEnum;
import com.nybc.edu.core.exception.BizException;
import com.nybc.user.context.UserHold;
import com.nybc.user.dao.*;
import com.nybc.user.entity.*;
import com.nybc.user.model.dto.CreateRoleRequest;
import com.nybc.user.model.dto.UpdateRoleRequest;
import com.nybc.user.model.dto.UserRoleAssignRequestDto;
import com.nybc.user.model.role.PermissionDto;
import com.nybc.user.model.role.PermissionGroupDto;
import com.nybc.user.model.role.RoleInfoDto;
import com.nybc.user.model.role.RoleQuery;
import com.nybc.user.service.RoleInfoService;
import com.nybc.user.service.UserCacheService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.nybc.edu.common.constant.NytcConstant.DEFAULT;
import static com.nybc.edu.common.constant.NytcConstant.UN_DEFAULT;

/**
 * 类描述：角色信息服务实现类
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@Service
public class RoleInfoServiceImpl implements RoleInfoService {
    @Resource
    private TeacherClassRelationMapper teacherClassRelationMapper;
    @Resource
    private RoleInfoMapper roleInfoMapper;
    @Resource
    private UserInfoMapper userInfoMapper;
    @Resource
    private UserRoleMapper userRoleMapper;
    @Resource
    private StudentDetailMapper studentDetailMapper;
    @Resource
    private TeacherDetailMapper teacherDetailMapper;
    @Resource
    private CorporateUserDetailMapper corporateUserDetailMapper;
    @Resource
    private RolePermissionMapper rolePermissionMapper;
    @Resource
    private PermissionInfoMapper permissionInfoMapper;
    @Resource
    private UserCacheService userCacheService;

    /**
     * 根据ID查询角色详情，包含权限组信息。
     *
     * @param roleId 角色ID
     * @return 角色详情DTO
     * @throws BizException 如果角色不存在
     */
    @Override
    public RoleInfoDto getById(Long roleId) {
        RoleInfo roleInfo = roleInfoMapper.getById(roleId);
        if (roleInfo == null) {
            log.warn("角色ID '{}' 未找到。", roleId);
            throw new BizException(ResultCodeEnum.ROLE_NOT_EXIST);
        }
        RoleInfoDto roleInfoDto = new RoleInfoDto();
        roleInfoDto.setId(roleInfo.getId());
        roleInfoDto.setCode(roleInfo.getRoleKey());
        roleInfoDto.setName(roleInfo.getRoleName());
        roleInfoDto.setRoleType(RoleTypeEnum.fromValue(roleInfo.getRoleTypeKey()));
        roleInfoDto.setDescription(roleInfo.getDescription());
        roleInfoDto.setIsSystemRole(roleInfo.getDefaultd() != null && roleInfo.getDefaultd() == DEFAULT);
        roleInfoDto.setCreateTime(roleInfo.getCreateTime());

        Map<Long, Integer> roleUserCounts = new HashMap<>();
        Map<Long, Map<String, Object>> rawCounts = userRoleMapper.countUsersByRoleIds(Collections.singletonList(roleId));
        if (rawCounts != null) {
            rawCounts.forEach((key, rowMap) -> {
                Object countValue = rowMap.get("user_count");
                if (countValue instanceof Number) {
                    roleUserCounts.put(key, ((Number) countValue).intValue());
                } else if (countValue != null) {
                    try {
                        roleUserCounts.put(key, Integer.parseInt(String.valueOf(countValue)));
                    } catch (NumberFormatException e) {
                        log.warn("无法将角色ID {} 的用户计数值 '{}' 转换为整数", roleId, countValue);
                    }
                }
            });
        }
        roleInfoDto.setUserCount(roleUserCounts.getOrDefault(roleId, 0));
        // 构建权限组
        List<PermissionGroupDto> permissionGroups = new ArrayList<>();
        List<RolePermission> rolePermissions = rolePermissionMapper.findByRoleId(roleId);
        List<Long> grantedPermissionIds = CollectionUtils.isNotEmpty(rolePermissions) ?
                                          rolePermissions.stream().map(RolePermission::getPermissionId).collect(Collectors.toList()) :
                                          Collections.emptyList();
        List<PermissionInfo> allPermissions = permissionInfoMapper.getAll();
        // 按分组名和排序值对权限进行排序，以确保前端树的顺序
        allPermissions.sort(Comparator.comparing(PermissionInfo::getPermissionGroupName, Comparator.nullsLast(String::compareTo))
                                    .thenComparingInt(p -> p.getSortOrder() == null ? Integer.MAX_VALUE : p.getSortOrder()));
        Map<String, List<PermissionDto>> groupedPermissions = allPermissions.stream()
                .collect(Collectors.groupingBy(
                        PermissionInfo::getPermissionGroupName,
                        LinkedHashMap::new,
                        Collectors.mapping(permissionInfo -> convertToPermissionDto(grantedPermissionIds, permissionInfo), Collectors.toList())));
        groupedPermissions.forEach((groupName, items) -> {
            PermissionGroupDto groupDto = new PermissionGroupDto();
            groupDto.setGroupName(groupName);
            groupDto.setPermissions(items);
            permissionGroups.add(groupDto);
        });
        roleInfoDto.setPermissionGroups(permissionGroups.isEmpty() ? Collections.emptyList() : permissionGroups);
        return roleInfoDto;
    }

    /**
     * 将 PermissionInfo 转换为 PermissionDto，并判断是否已授权。
     * (此方法与之前一致，保持不变)
     */
    private PermissionDto convertToPermissionDto(List<Long> grantedPermissionIds, PermissionInfo permissionInfo) {
        PermissionDto dto = new PermissionDto();
        dto.setId(permissionInfo.getId());
        dto.setCode(permissionInfo.getPermissionCode());
        dto.setName(permissionInfo.getPermissionName());
        dto.setDescription(permissionInfo.getDescription());
        dto.setIsGranted(grantedPermissionIds.contains(permissionInfo.getId()));
        return dto;
    }

    /**
     * 分页查询角色列表。
     *
     * @param queryDto 查询条件及分页参数
     * @return 分页的角色列表结果
     */
    @Override
    public PageResult<RoleInfoDto> getPage(RoleQuery queryDto) {
        log.info("分页查询角色，参数: {}", queryDto);
        PageInfo<RoleInfo> pageInfo = PageUtils.queryPage(queryDto, () -> roleInfoMapper.query(queryDto));
        if (pageInfo.getList().isEmpty()) {
            return new PageResult<>();
        }
        List<Long> roleIdsOnPage = pageInfo.getList().stream().map(RoleInfo::getId).collect(Collectors.toList());
        Map<Long, Integer> roleUserCounts = new HashMap<>();
        if (!roleIdsOnPage.isEmpty()) {
            Map<Long, Map<String, Object>> rawCounts = userRoleMapper.countUsersByRoleIds(roleIdsOnPage);
            if (rawCounts != null) {
                rawCounts.forEach((roleId, rowMap) -> {
                    Object countValue = rowMap.get("user_count");
                    if (countValue instanceof Number) {
                        roleUserCounts.put(roleId, ((Number) countValue).intValue());
                    } else if (countValue != null) {
                        try {
                            roleUserCounts.put(roleId, Integer.parseInt(String.valueOf(countValue)));
                        } catch (NumberFormatException e) {
                            log.warn("无法将角色ID {} 的用户计数值 '{}' 转换为整数", roleId, countValue);
                        }
                    }
                });
            }
        }
        List<RoleInfoDto> roleInfoDtos = pageInfo.getList().stream()
                .map(roleInfo -> {
                    RoleInfoDto dto = new RoleInfoDto();
                    dto.setId(roleInfo.getId());
                    dto.setCode(roleInfo.getRoleKey());
                    dto.setName(roleInfo.getRoleName());
                    dto.setRoleType(RoleTypeEnum.fromValue(roleInfo.getRoleTypeKey()));
                    dto.setDescription(roleInfo.getDescription());
                    dto.setIsSystemRole(roleInfo.getDefaultd() != null && roleInfo.getDefaultd() == DEFAULT);
                    dto.setUserCount(roleUserCounts.getOrDefault(roleInfo.getId(), 0));
                    dto.setCreateTime(roleInfo.getCreateTime());
                    dto.setPermissionGroups(Collections.emptyList());
                    return dto;
                })
                .collect(Collectors.toList());

        log.info("成功查询到 {} 条角色信息，总条数: {}", roleInfoDtos.size(), pageInfo.getTotal());
        // 方法可能需要调整以适应新的 PageResult 构造函数
        // 或者直接使用 PageResult 的构造函数
        return PageUtils.covertToDtoPageResult(pageInfo, roleInfoDtos);
    }

    /**
     * 初始化系统默认角色。
     * (此方法与之前一致，保持不变)
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void init() {
        log.info("开始检查并初始化系统默认角色...");
        for (RoleEnum roleEnum : RoleEnum.values()) {
            RoleInfo existingRole = roleInfoMapper.findByRoleKey(roleEnum.getKey());
            if (existingRole == null) {
                RoleInfo newRole = new RoleInfo()
                        .setRoleKey(roleEnum.getKey())
                        .setRoleName(roleEnum.getName())
                        .setRoleTypeKey(roleEnum.getType().name())
                        .setDefaultd(DEFAULT)
                        .setCreateTime(LocalDateTime.now())
                        .setUpdateTime(LocalDateTime.now())
                        .setCreateUser(1L)
                        .setUpdateUser(1L);
                roleInfoMapper.insert(newRole);
                log.info("已初始化默认角色: {} (Key: {})", newRole.getRoleName(), newRole.getRoleKey());
            } else {
                // (可选) 检查现有默认角色的 roleTypeKey 是否与 RoleEnum 中的一致，不一致则更新
                if (!Objects.equals(existingRole.getRoleTypeKey(), roleEnum.getType().name())) {
                    log.warn("默认角色 {} (Key: {}) 的 RoleTypeKey 与枚举定义不一致 (DB: {}, Enum: {}), 将尝试更新。",
                             existingRole.getRoleName(), existingRole.getRoleKey(), existingRole.getRoleTypeKey(), roleEnum.getType().name());
                    existingRole.setRoleTypeKey(roleEnum.getType().name());
                    existingRole.setUpdateTime(LocalDateTime.now());
                    existingRole.setUpdateUser(1L);
                    roleInfoMapper.updateById(existingRole);
                }
                log.debug("默认角色: {} (Key: {}) 已存在。", existingRole.getRoleName(), existingRole.getRoleKey());
            }
        }
        log.info("系统默认角色检查与初始化完成。");
    }


    /**
     * 获取所有角色列表 (不分页)。
     */
    @Override
    public List<RoleInfoDto> getAll() {
        List<RoleInfo> roleInfos = roleInfoMapper.listAll();
        if (CollectionUtils.isEmpty(roleInfos)) {
            return Collections.emptyList();
        }
        return roleInfos.stream().map(roleInfo -> {
            RoleInfoDto dto = new RoleInfoDto();
            dto.setId(roleInfo.getId());
            dto.setCode(roleInfo.getRoleKey());
            dto.setName(roleInfo.getRoleName());
            dto.setRoleType(RoleTypeEnum.fromValue(roleInfo.getRoleTypeKey()));
            dto.setDescription(roleInfo.getDescription());
            dto.setIsSystemRole(roleInfo.getDefaultd() != null && roleInfo.getDefaultd() == DEFAULT);
            dto.setCreateTime(roleInfo.getCreateTime());
            // 同样，列表查询通常不包含权限组
            dto.setPermissionGroups(Collections.emptyList());
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 为用户分配角色。
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public int assignUserRole(UserRoleAssignRequestDto assignRequest) {
        Long userId = assignRequest.getUserId();
        String targetRoleKey = assignRequest.getTargetRoleKey();
        Assert.notNull(userId, "用户ID不能为空");
        Assert.hasText(targetRoleKey, "目标角色键不能为空");
        UserInfo userInfo = userInfoMapper.getById(userId);
        if (userInfo == null) {
            throw new BizException("用户ID: " + userId + " 不存在。");
        }
        RoleEnum targetRoleEnum = RoleEnum.matchByKey(targetRoleKey);
        if (targetRoleEnum == null) {
            throw new BizException("目标角色键: " + targetRoleKey + " 无效或不存在。");
        }
        if (targetRoleEnum != RoleEnum.SCH_STU &&
                targetRoleEnum != RoleEnum.SCH_TECH &&
                targetRoleEnum != RoleEnum.CROP_USER &&
                targetRoleEnum != RoleEnum.CROP_ADMIN) {
            throw new BizException("目标角色键: " + targetRoleKey + " 不是可直接分配的业务角色。");
        }
        RoleInfo targetRoleInfo = roleInfoMapper.findByRoleKey(targetRoleKey);
        if (targetRoleInfo == null || targetRoleInfo.getId() == null) {
            throw new BizException("无法在数据库中找到角色键为: " + targetRoleKey + " 的角色信息。");
        }
        log.info("开始为用户ID: {} (用户名: {}) 分配新角色: {}", userId, userInfo.getUserName(), targetRoleKey);
        // 1. 清理该用户所有旧的 *业务* 角色及其关联详情
        // 首先获取用户当前所有角色
        List<UserRole> currentUserRoles = userRoleMapper.findByUserId(userId);
        List<Long> currentRoleIds = currentUserRoles.stream().map(UserRole::getRoleId).collect(Collectors.toList());
        List<RoleInfo> currentRoleInfoList = CollectionUtils.isNotEmpty(currentRoleIds) ?
                                             roleInfoMapper.findByIds(currentRoleIds) : Collections.emptyList();

        Set<String> businessRoleKeysToClean = Set.of(
                RoleEnum.SCH_STU.getKey(), RoleEnum.SCH_TECH.getKey(),
                RoleEnum.CROP_USER.getKey(), RoleEnum.CROP_ADMIN.getKey());
        for (RoleInfo currentRole : currentRoleInfoList) {
            if (businessRoleKeysToClean.contains(currentRole.getRoleKey()) &&
                    !currentRole.getRoleKey().equals(targetRoleKey)) {
                userRoleMapper.deleteByUserIdAndRoleId(userId, currentRole.getId());
                log.info("移除了用户ID: {} 的旧业务角色: {}", userId, currentRole.getRoleKey());
                deleteRoleSpecificDetails(userId, currentRole.getRoleKey(), UserHold.getUserId());
            }
        }
        // 2. 添加新的目标业务角色 (如果用户还没有这个角色)
        boolean alreadyHasTargetRole = currentRoleInfoList.stream()
                .anyMatch(r -> r.getRoleKey().equals(targetRoleKey));
        if (!alreadyHasTargetRole) {
            UserRole newUserRole = new UserRole();
            newUserRole.setUserId(userId);
            newUserRole.setRoleId(targetRoleInfo.getId());
            newUserRole.setCreateTime(LocalDateTime.now());
            newUserRole.setCreateUser(UserHold.getUserId());
            userRoleMapper.insert(newUserRole);
            log.info("为用户ID: {} 添加了新角色: {}", userId, targetRoleKey);
        }

        // 4. 根据新的目标角色，创建或更新对应的详情数据
        switch (targetRoleEnum) {
            case SCH_STU:
                if (assignRequest.getStudentDetail() == null) {
                    throw new BizException("分配学生角色时，学生详细信息不能为空。");
                }
                StudentDetail studentDetail = studentDetailMapper.getByUserId(userId);
                if (studentDetail == null) {
                    studentDetail = new StudentDetail().setUserId(userId).setCreateTime(LocalDateTime.now()).setCreateUser(UserHold.getUserId());
                }
                BeanUtils.copyProperties(assignRequest.getStudentDetail(), studentDetail, "id", "userId", "createTime", "createUser");
                studentDetail.setUpdateTime(LocalDateTime.now());
                studentDetail.setUpdateUser(UserHold.getUserId());
                if (studentDetail.getId() == null) {
                    studentDetailMapper.insert(studentDetail);
                } else {
                    studentDetailMapper.updateById(studentDetail);
                }
                log.info("为用户ID: {} 创建/更新了学生详情数据。", userId);
                break;
            case SCH_TECH:
                if (assignRequest.getTeacherDetail() == null) {
                    throw new BizException("分配教师角色时，教师详细信息不能为空。");
                }
                TeacherDetail teacherDetail = teacherDetailMapper.getByUserId(userId);
                if (teacherDetail == null) {
                    teacherDetail = new TeacherDetail().setUserId(userId).setCreateTime(LocalDateTime.now()).setCreateUser(UserHold.getUserId());
                }
                BeanUtils.copyProperties(assignRequest.getTeacherDetail(), teacherDetail, "id", "userId", "createTime", "createUser");
                teacherDetail.setUpdateTime(LocalDateTime.now());
                teacherDetail.setUpdateUser(UserHold.getUserId());
                if (teacherDetail.getId() == null) {
                    teacherDetailMapper.insert(teacherDetail);
                } else {
                    teacherDetailMapper.updateById(teacherDetail);
                }
                log.info("为用户ID: {} 创建/更新了教师详情数据。", userId);
                break;
            case CROP_USER:
            case CROP_ADMIN:
                if (assignRequest.getCorporateUserDetail() == null) {
                    throw new BizException("分配企业用户角色时，企业用户详细信息不能为空。");
                }
                CorporateUserDetail corporateDetail = corporateUserDetailMapper.getByUserId(userId);
                if (corporateDetail == null) {
                    corporateDetail = new CorporateUserDetail().setUserId(userId).setCreateTime(LocalDateTime.now()).setCreateUser(UserHold.getUserId());
                }
                BeanUtils.copyProperties(assignRequest.getCorporateUserDetail(), corporateDetail, "id", "userId", "createTime", "createUser");
                corporateDetail.setUpdateTime(LocalDateTime.now());
                corporateDetail.setUpdateUser(UserHold.getUserId());
                if (corporateDetail.getId() == null) {
                    corporateUserDetailMapper.insert(corporateDetail);
                } else {
                    corporateUserDetailMapper.updateById(corporateDetail);
                }
                log.info("为用户ID: {} 创建/更新了企业用户详情数据。", userId);
                break;
            default:
                break;
        }
        userCacheService.evictFromCache(userId);
        return ResultCodeEnum.SUCCESS.getCode();
    }


    // 删除用户时，清理其特定角色的详情数据
    private void deleteRoleSpecificDetails(Long userId, String roleKey, Long operatorId) {
        RoleEnum roleEnum = RoleEnum.matchByKey(roleKey);
        if (roleEnum == null) {
            log.warn("尝试删除用户ID: {} 的角色详情时，未知的角色Key: {}", userId, roleKey);
            return;
        }
        switch (roleEnum) {
            case SCH_STU:
                studentDetailMapper.deleteByUserId(userId);
                log.info("删除了用户ID: {} 的学生详情 (操作者ID: {})", userId, operatorId);
                break;
            case SCH_TECH:
                teacherDetailMapper.deleteByUserId(userId);
                log.info("删除了用户ID: {} 的教师详情 (操作者ID: {})", userId, operatorId);
                // 也可能需要删除教师与班级的关联
                teacherClassRelationMapper.deleteByTeacherUserId(userId);
                log.info("删除了用户ID: {} (教师) 的班级关联记录 (操作者ID: {})", userId, operatorId);
                break;
            case CROP_USER:
            case CROP_ADMIN:
                corporateUserDetailMapper.deleteByUserId(userId);
                log.info("删除了用户ID: {} 的企业用户详情 (操作者ID: {})", userId, operatorId);
                break;
            // SUPER_ADMIN 或 SYSTEM_USER 通常没有独立的详情表，或者不应在此处删除
            default:
                log.debug("角色Key: {} 没有对应的特定详情表删除逻辑。", roleKey);
                break;
        }
        userCacheService.evictFromCache(userId);
    }


    /**
     * 创建新角色。
     * (此方法与之前基本一致，确保日志和异常处理符合新规范)
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public int createRole(CreateRoleRequest request) {
        log.info("尝试创建新角色，请求: {}", request);
        RoleInfo existingRoleByKey = roleInfoMapper.findByRoleKey(request.getCode());
        if (existingRoleByKey != null) {
            log.warn("角色键 '{}' 已被角色ID '{}' 占用。", request.getCode(), existingRoleByKey.getId());
            throw new BizException(ResultCodeEnum.ROLE_ALREADY_EXISTS_KEY);
        }
        // 校验角色类型
        RoleTypeEnum roleType = request.getRoleType();
        RoleInfo newRole = new RoleInfo();
        newRole.setRoleKey(request.getCode());
        newRole.setRoleName(request.getName());
        newRole.setDescription(request.getDescription());
        newRole.setRoleTypeKey(roleType.name());
        newRole.setDefaultd(UN_DEFAULT);
        newRole.setCreateTime(LocalDateTime.now());
        newRole.setCreateUser(UserHold.getUserId());
        newRole.setUpdateTime(LocalDateTime.now());
        newRole.setUpdateUser(UserHold.getUserId());
        int insertResult = roleInfoMapper.insert(newRole);
        if (insertResult <= 0 || newRole.getId() == null) {
            log.error("创建角色 '{}' 失败，数据库操作未返回有效ID。", request.getCode());
            throw new BizException(ResultCodeEnum.DATA_OPERATION_FAILED.getCode(), "创建角色信息失败。");
        }
        log.info("角色 '{}' (ID: {}) 已成功创建。", newRole.getRoleKey(), newRole.getId());
        if (CollectionUtils.isNotEmpty(request.getPermissionIds())) {
            List<RolePermission> rolePermissions = new ArrayList<>();
            for (Long permissionId : request.getPermissionIds()) {
                // 校验权限ID是否存在 (可选，但推荐)
                PermissionInfo perm = permissionInfoMapper.getById(permissionId);
                if (perm == null) {
                    log.warn("尝试为角色 {}关联不存在的权限ID: {}", newRole.getId(), permissionId);
                    continue;
                }
                RolePermission rolePermission = new RolePermission();
                rolePermission.setRoleId(newRole.getId());
                rolePermission.setPermissionId(permissionId);
                rolePermission.setCreateTime(LocalDateTime.now());
                rolePermission.setCreateUser(UserHold.getUserId());
                rolePermission.setUpdateTime(LocalDateTime.now());
                rolePermission.setUpdateUser(UserHold.getUserId());
                rolePermissions.add(rolePermission);
            }
            if (!rolePermissions.isEmpty()) {
                rolePermissionMapper.insertBatch(rolePermissions);
                log.info("为新角色ID {} 成功分配了 {} 条权限。", newRole.getId(), rolePermissions.size());
            }
        }
        return ResultCodeEnum.SUCCESS.getCode();
    }

    /**
     * 更新角色信息。
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public int updateRole(UpdateRoleRequest request) {
        Long currentUserId = UserHold.getUserId();
        log.info("管理员 {} 正在尝试更新角色ID: {}，请求参数: {}", currentUserId, request.getRoleId(), request);
        RoleInfo existingRole = roleInfoMapper.getById(request.getRoleId());
        if (existingRole == null) {
            log.warn("尝试更新的角色ID '{}' 不存在。", request.getRoleId());
            throw new BizException(ResultCodeEnum.ROLE_NOT_EXIST);
        }
        // 系统默认角色通常不允许修改名称、描述之外的关键属性
        if (existingRole.getDefaultd() != null && existingRole.getDefaultd() == DEFAULT) {
            // 允许修改描述和权限，但不允许修改名称和角色键 (角色键通常在创建后就不应修改)
            if (request.getName() != null && !StringUtils.equals(existingRole.getRoleName(), request.getName())) {
                log.warn("系统默认角色 '{}' (ID: {}) 的名称不允许修改。", existingRole.getRoleKey(), existingRole.getId());
                // 可以选择抛出异常或仅记录警告并忽略名称修改
                throw new BizException(ResultCodeEnum.OPERATION_NOT_ALLOWED.getCode(), "系统默认角色的名称不能修改。");
            }
        } else { // 非系统默认角色
            if (request.getName() != null && !StringUtils.equals(existingRole.getRoleName(), request.getName())) {
                RoleInfo roleWithSameName = roleInfoMapper.findByName(request.getName());
                if (roleWithSameName != null && !Objects.equals(roleWithSameName.getId(), existingRole.getId())) {
                    log.warn("尝试将角色ID '{}' 的名称更新为 '{}'，但该名称已被角色ID '{}' 使用。",
                             existingRole.getId(), request.getName(), roleWithSameName.getId());
                    throw new BizException(ResultCodeEnum.ROLE_ALREADY_EXISTS_NAME);
                }
                existingRole.setRoleName(request.getName());
            }
        }
        // 更新描述 (对所有角色都允许)
        if (request.getDescription() != null && !StringUtils.equals(existingRole.getDescription(), request.getDescription())) {
            existingRole.setDescription(request.getDescription());
        }
        existingRole.setUpdateTime(LocalDateTime.now());
        existingRole.setUpdateUser(currentUserId);
        int updateResult = roleInfoMapper.updateById(existingRole);
        if (updateResult <= 0) {
            log.warn("更新角色ID '{}' 的基本信息时，数据库未更新任何行 (可能数据未变化)。", existingRole.getId());
            // 不一定是错误，可能只是没有字段变化
        } else {
            log.info("角色ID '{}' 的基本信息已更新。", existingRole.getId());
        }
        // 更新权限关联：先删除旧的，再添加新的
        if (request.getPermissionIds() != null) {
            int deletedPermissions = rolePermissionMapper.deleteByRoleId(existingRole.getId());
            log.info("已为角色ID {} 清理了 {} 条旧的权限关联。", existingRole.getId(), deletedPermissions);

            if (CollectionUtils.isNotEmpty(request.getPermissionIds())) {
                List<RolePermission> newRolePermissions = new ArrayList<>();
                for (Long permissionId : request.getPermissionIds()) {
                    PermissionInfo perm = permissionInfoMapper.getById(permissionId);
                    if (perm == null) {
                        log.warn("尝试为角色 {} 关联不存在的权限ID: {}", existingRole.getId(), permissionId);
                        continue;
                    }
                    RolePermission rolePermission = new RolePermission();
                    rolePermission.setRoleId(existingRole.getId());
                    rolePermission.setPermissionId(permissionId);
                    rolePermission.setCreateTime(LocalDateTime.now());
                    rolePermission.setCreateUser(currentUserId);
                    rolePermission.setUpdateTime(LocalDateTime.now());
                    rolePermission.setUpdateUser(currentUserId);
                    newRolePermissions.add(rolePermission);
                }
                if (!newRolePermissions.isEmpty()) {
                    int newPermissionInsertCount = rolePermissionMapper.insertBatch(newRolePermissions);
                    log.info("为角色ID {} 成功分配了 {} 条新权限。", existingRole.getId(), newPermissionInsertCount);
                }
            } else {
                log.info("角色ID {} 的权限被设置为空列表，所有权限已被移除（如果之前有）。", existingRole.getId());
            }
        }
        List<UserRole> usersWithThisRole = userRoleMapper.findByRoleId(existingRole.getId());
        usersWithThisRole.forEach(ur -> userCacheService.evictFromCache(ur.getUserId()));
        return ResultCodeEnum.SUCCESS.getCode();
    }

    /**
     * 删除角色。
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public int deleteRole(Long roleId) {
        Long currentUserId = UserHold.getUserId();
        log.info("管理员 {} 正在尝试删除角色ID: {}", currentUserId != null ? currentUserId : "SYSTEM", roleId);
        RoleInfo roleToDelete = roleInfoMapper.getById(roleId);
        if (roleToDelete == null) {
            log.warn("尝试删除的角色ID '{}' 不存在。", roleId);
            throw new BizException(ResultCodeEnum.ROLE_NOT_EXIST);
        }
        if (roleToDelete.getDefaultd() != null && roleToDelete.getDefaultd() == DEFAULT) {
            log.warn("尝试删除系统默认角色 '{}' (ID: {})，操作被禁止。", roleToDelete.getRoleKey(), roleId);
            throw new BizException(ResultCodeEnum.OPERATION_NOT_ALLOWED.getCode(), "系统默认角色不能删除。");
        }
        List<UserRole> userRoles = userRoleMapper.findByRoleId(roleId);
        if (CollectionUtils.isNotEmpty(userRoles)) {
            log.warn("角色ID '{}' (名称: '{}') 仍被 {} 个用户关联，无法删除。",
                     roleId, roleToDelete.getRoleName(), userRoles.size());
            throw new BizException(ResultCodeEnum.ROLE_IN_USE);
        }
        // 删除角色与权限的关联
        int deletedPermissions = rolePermissionMapper.deleteByRoleId(roleId);
        log.info("已为角色ID {} 清理了 {} 条权限关联。", roleId, deletedPermissions);
        // 删除角色本身
        int deleteResult = roleInfoMapper.deleteById(roleId);
        if (deleteResult <= 0) {
            log.error("删除角色ID '{}' 失败，数据库操作未影响任何行。", roleId);
            throw new BizException(ResultCodeEnum.DATA_OPERATION_FAILED.getCode(), "删除角色失败。");
        }
        log.info("角色ID {} (名称: '{}') 已成功删除。", roleId, roleToDelete.getRoleName());
        return ResultCodeEnum.SUCCESS.getCode();
    }


    /**
     * 获取所有权限列表，并按组名组织。
     */
    @Override
    public List<PermissionGroupDto> getPermissionList() {
        List<PermissionInfo> allPermissions = permissionInfoMapper.getAll();
        if (CollectionUtils.isEmpty(allPermissions)) {
            return Collections.emptyList();
        }
        // 按分组名和排序值排序
        allPermissions.sort(Comparator.comparing(PermissionInfo::getPermissionGroupName, Comparator.nullsLast(String::compareTo))
                                    .thenComparingInt(p -> p.getSortOrder() == null ? Integer.MAX_VALUE : p.getSortOrder()));
        Map<String, List<PermissionDto>> groupedPermissions = allPermissions.stream()
                .collect(Collectors.groupingBy(
                        PermissionInfo::getPermissionGroupName,
                        LinkedHashMap::new,
                        Collectors.mapping(permissionInfo -> {
                            PermissionDto dto = new PermissionDto();
                            dto.setId(permissionInfo.getId()); // 后端 Long
                            dto.setCode(permissionInfo.getPermissionCode());
                            dto.setName(permissionInfo.getPermissionName());
                            dto.setDescription(permissionInfo.getDescription());
                            dto.setIsGranted(false);
                            return dto;
                        }, Collectors.toList())));
        List<PermissionGroupDto> resultPermissionGroups = new ArrayList<>();
        groupedPermissions.forEach((groupName, permissionDtoList) -> {
            PermissionGroupDto groupDto = new PermissionGroupDto();
            groupDto.setGroupName(groupName); // groupName已经是中文
            groupDto.setPermissions(permissionDtoList);
            resultPermissionGroups.add(groupDto);
        });
        return resultPermissionGroups;
    }

    /**
     * 根据用户ID获取其拥有的所有角色键列表。
     * (此方法与之前一致，保持不变)
     */
    @Override
    public List<String> getRoleKeysByUserId(Long userId) {
        if (userId == null) {
            return Collections.emptyList();
        }
        List<UserRole> userRoles = userRoleMapper.findByUserId(userId);
        if (CollectionUtils.isEmpty(userRoles)) {
            return Collections.emptyList();
        }
        List<Long> roleIds = userRoles.stream()
                .map(UserRole::getRoleId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(roleIds)) {
            return Collections.emptyList();
        }
        List<RoleInfo> roles = roleInfoMapper.findByIds(roleIds);
        return roles.stream()
                .map(RoleInfo::getRoleKey)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

}