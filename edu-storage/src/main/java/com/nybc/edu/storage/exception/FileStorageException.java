package com.nybc.edu.storage.exception;

import lombok.Getter;

/**
 * 类描述：
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Getter
public class FileStorageException extends RuntimeException {

    private boolean isFileNotFound = false;

    public FileStorageException(String message) {
        super(message);
    }

    public FileStorageException(String message, Throwable cause) {
        super(message, cause);
    }

    public FileStorageException(String message, Throwable cause, boolean isFileNotFound) {
        super(message, cause);
        this.isFileNotFound = isFileNotFound;
    }

}
