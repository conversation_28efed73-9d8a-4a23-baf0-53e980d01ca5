package com.nybc.edu.storage.service;

import com.nybc.edu.common.page.PageResult;
import com.nybc.edu.common.enums.FileModuleType;
import com.nybc.edu.storage.dto.*;
import com.nybc.edu.storage.entity.FileMetadata;
import com.nybc.edu.storage.enums.FileAccessLevel;
import com.nybc.edu.storage.exception.FileStorageException;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * 接口描述：文件元数据管理服务接口。
 * 负责文件元数据的创建、查询、更新和删除操作。
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
public interface FileMetadataService {
    /**
     * 根据提供的文件ID列表，获取用户有权下载且为有效文件（非目录、未逻辑删除）的元数据列表。
     * 此方法用于准备打包下载的文件列表。
     *
     * @param fileIds       要检查的文件元数据ID列表。
     * @param currentUserId 当前操作用户的ID，用于权限校验。
     * @return 一个列表，包含用户有权下载且有效的 FileMetadata 对象。
     */
    List<FileMetadata> getValidFilesForPackaging(List<Long> fileIds, Long currentUserId);

    /**
     * 根据提供的文件元数据ID列表，批量查找，并进行权限过滤。
     *
     * @param fileIds       文件ID列表。
     * @param currentUserId 当前操作用户ID。
     * @return 用户可见的文件元数据列表。
     */
    List<FileMetadata> getFilesByIdsWithAccessCheck(List<Long> fileIds, Long currentUserId);

    /**
     * 重载方法：根据文件元数据DTO对象，构造其在前端UI展示用的逻辑全路径。
     *
     * @param metadataDto 文件元数据DTO对象。
     * @return 用于UI展示的逻辑全路径字符串。
     */
    String getLogicalFullPathForDto(FileMetadataDto metadataDto);

    /**
     * 根据文件元数据对象，构造其在前端UI展示用的逻辑全路径。
     * 例如：对于项目文件，可能是 "/项目名称/模块内路径/文件名"。
     *
     * @param metadata 文件元数据对象。
     * @return 用于UI展示的逻辑全路径字符串。
     */
    String getLogicalFullPathForDto(FileMetadata metadata);

    /**
     * 完成分片上传：合并物理分片文件，并创建最终的文件元数据记录。
     *
     * @param session                 分片上传会话信息 (从Redis等获取)。
     * @param finalFileHash           (可选) 完整文件的最终哈希值。
     * @param chunkContextsForStorage (对于MinIO) 已上传分片的上下文列表 (partNumber, ETag)。
     * @return 创建的最终文件元数据对象。
     * @throws FileStorageException 如果合并或元数据创建失败。
     */
    FileMetadata completeChunkUploadAndCreateMetadata(
            ChunkUploadSession session, String finalFileHash,
            List<Map<String, String>> chunkContextsForStorage) throws FileStorageException;

    // 秒传功能已移除

    /**
     * 初始化分片上传会话。
     *
     * @param initRequest         分片初始化请求DTO。
     * @param uploaderId          上传者ID。
     * @param moduleType          模块类型。
     * @param accessLevel         访问级别。
     * @param detectedContentType 检测到的文件MIME类型。
     * @return 初始化响应DTO，包含 uploadId 和可能的预签名URL。
     * @throws FileStorageException 如果初始化失败。
     */
    ChunkInitResponse initializeChunkUpload(
            ChunkInitRequest initRequest, Long uploaderId, String uploaderName, FileModuleType moduleType,
            FileAccessLevel accessLevel, String detectedContentType
                                           ) throws FileStorageException;

    /**
     * 根据文件在存储系统上的存储名称查找文件元数据。
     *
     * @param id
     * @return 文件元数据对象；如果未找到，则返回null。
     */
    FileMetadata findByIdIncludeDeleted(Long id);

    /**
     * 根据用户ID和查询条件查找已逻辑删除的文件元数据。
     *
     * @param userId
     * @param request
     * @return
     */
    PageResult<FileMetadataDto> findDeletedItemsByUserId(Long userId, RecycleBinQueryRequest request);

    /**
     * 恢复文件
     *
     * @param metadataId
     * @param operatorId
     * @return
     */
    int restoreFromRecycleBin(Long metadataId, Long operatorId);

    /**
     * 根据文件元数据的唯一ID查找所有版本的文件元数据记录。
     *
     * @param id
     * @return
     */
    List<FileMetadata> findAllVersionsById(Long id);

    /**
     * 保存新的或更新现有的文件元数据记录。
     * 如果 fileMetadata 的 id 为 null，则执行插入操作。
     * 如果 fileMetadata 的 id 非 null，则执行更新操作（通常只更新允许修改的字段）。
     *
     * @param fileMetadata 要保存或更新的文件元数据对象。
     * @return 操作成功后的文件元数据对象 (包含数据库生成的ID或更新后的状态)。
     * @throws FileStorageException 如果保存或更新过程中发生数据库错误或其他业务校验失败。
     */
    FileMetadata saveOrUpdateMetadata(FileMetadata fileMetadata) throws FileStorageException;

    /**
     * 根据文件哈希值查找文件元数据。
     */
    FileMetadata findByFileHash(String fileHash);

    /**
     * 根据文件元数据的唯一ID查找未被逻辑删除的记录。
     *
     * @param id 文件元数据的ID。
     * @return 文件元数据对象；如果不存在或已被逻辑删除，则返回null。
     */
    FileMetadata findById(Long id);

    /**
     * 根据文件在存储系统中的唯一名称 (storage_name) 查找最新的、未被逻辑删除的文件元数据。
     *
     * @param storageName 文件在存储系统中的唯一名称。
     * @return 文件元数据对象；如果不存在，则返回null。
     */
    FileMetadata findByStorageName(String storageName);

    /**
     * 逻辑删除指定ID的文件元数据记录。
     *
     * @param id         要逻辑删除的文件元数据的ID。
     * @param operatorId 执行删除操作的用户ID。
     * @return
     * @throws FileStorageException 如果文件元数据不存在、已被删除，或删除过程中发生数据库错误。
     */
    int logicalDeleteMetadata(Long id, Long operatorId) throws FileStorageException;

    /**
     * 物理删除指定ID的文件元数据记录。
     * **警告：** 此操作不可恢复，通常在物理文件也确认删除后执行。
     *
     * @param id 要物理删除的文件元数据的ID。
     * @return
     * @throws FileStorageException 如果删除过程中发生数据库错误。
     */
    int physicalDeleteMetadata(Long id) throws FileStorageException;

    /**
     * 查找所有已过期且标记为临时的、未被逻辑删除的文件元数据记录。
     * 用于定时清理任务。
     *
     * @return 过期的临时文件元数据列表；如果无此类文件，则返回空列表。
     */
    List<FileMetadata> findExpiredTemporaryFiles();

    /**
     * 原子性地增加指定文件ID的下载次数。
     *
     * @param fileId 要增加下载次数的文件元数据ID。
     * @return
     * @throws FileStorageException 如果文件不存在或更新下载次数失败。
     */
    int incrementDownloadCount(Long fileId) throws FileStorageException;

    /**
     * 根据提供的文件元数据ID列表，批量查找对应的、未被逻辑删除的文件元数据记录。
     *
     * @param ids 文件元数据ID的列表。
     * @return 包含找到的文件元数据记录的列表；如果列表为空或ID均无效/已删除，则返回空列表。
     */
    List<FileMetadata> findByIds(List<Long> ids);

    FileMetadata updateMetadata(Long fileId, FileMetadataUpdateRequestDto updateRequest, Long operatorId) throws FileStorageException;

    /**
     * 列出指定目录下，当前用户可见的内容（文件和/或直接子目录）的元数据。
     *
     * @param storagePath           要查询的目录路径 (例如 "courses/123/" 或 "" 代表根)。
     * @param includeFiles          是否在结果中包含文件。
     * @param includeSubDirectories 是否在结果中包含直接子目录。
     * @param currentUserId         当前操作用户ID，用于权限过滤。
     * @param courseIdContext       (可选) 课程上下文ID，用于权限判断。
     * @param projectIdContext      (可选) 项目上下文ID，用于权限判断。
     * @param taskIdContext         (可选) 任务上下文ID，用于权限判断。
     * @return 符合条件且用户可见的文件和目录元数据列表；如果无内容或无权限，返回空列表。
     * @throws FileStorageException 如果路径无效或基础访问被拒绝。
     */
    List<FileMetadata> listDirectoryContentsMetadata(
            String storagePath,
            boolean includeFiles,
            boolean includeSubDirectories,
            Long currentUserId,
            Long courseIdContext,
            Long projectIdContext,
            Long taskIdContext) throws FileStorageException;

    /**
     * 递归清理因子项删除而变为空的父目录。
     * 该方法检查被删除项的父目录，如果父目录变为空，则物理删除父目录，并向上递归。
     *
     * @param deletedItemMetadata 刚刚被物理删除的文件或目录的元数据。
     * @param operatorId          操作用户ID，用于权限校验和审计。
     */
    void cleanUpEmptyParentDirectory(FileMetadata deletedItemMetadata, Long operatorId);

    /**
     * 根据项目ID查询当前用户可见的文件列表。
     *
     * @param projectId     项目ID。
     * @param currentUserId 当前用户ID，用于权限过滤。
     * @return 该项目下用户可见的文件元数据列表；如果无匹配项或无权限，返回空列表。
     */
    List<FileMetadata> findFilesByProjectId(Long projectId, Long currentUserId);

    /**
     * 根据任务ID查询当前用户可见的文件列表。
     *
     * @param taskId        任务ID。
     * @param currentUserId 当前用户ID，用于权限过滤。
     * @return 该任务下用户可见的文件元数据列表；如果无匹配项或无权限，返回空列表。
     */
    List<FileMetadata> findFilesByTaskId(Long taskId, Long currentUserId);

    /**
     * 根据元数据获取其在存储服务中的完整Key (物理路径或对象键)。
     *
     * @param metadata 文件元数据对象。
     * @return 存储服务所需的完整访问键。
     */
    String getStorageKeyForFile(FileMetadata metadata);

    FileMetadata storeAndCreateMetadata(
            InputStream inputStream,
            String originalFilename,
            long fileSize,
            String mimeType,
            String fileExtension,
            String fileHash,
            Long uploaderId,
            String uploaderName,
            FileModuleType moduleType,
            Long relatedEntityId,
            FileAccessLevel accessLevel,
            Long projectId,
            Long taskId,
            String moduleRelativeTargetPath,
            boolean isTemporary) throws FileStorageException;

    /**
     * 根据多个筛选条件查询文件元数据列表（带分页和权限过滤）。
     *
     * @param query         DTO对象，包含各种筛选条件。 (具体类型 FileSearchQueryDto 待定义)
     * @param currentUserId 当前用户ID，用于权限过滤。
     * @return 分页的文件元数据结果 (例如 PageInfo<FileMetadata>，当前简化为List)。
     */
    PageResult<FileMetadataDto> searchFiles(FileSearchRequest query, Long currentUserId);

    /**
     * 创建目录的元数据记录，并处理物理目录的创建。
     * 此方法封装了重名检查、物理目录创建和元数据保存的逻辑。
     *
     * @param relativeParentPath 文件夹的父路径 (相对于其业务模块根)。
     * @param folderName         要创建的文件夹名称。
     * @param creatorId          创建者用户ID。
     * @param creatorName        创建者用户名。
     * @param courseId           (可选) 关联的课程ID。
     * @param projectId          (可选) 关联的项目ID。
     * @param taskId             (可选) 关联的任务ID。
     * @param moduleType         文件夹的模块类型。
     * @param accessLevel        文件夹的访问级别。
     * @param storageBackendCode 存储后端代码 (如 "LOCAL", "MINIO")。
     * @return 创建成功后的 FileMetadata 对象。
     * @throws FileStorageException 如果目录已存在、物理创建失败或元数据保存失败。
     */
    FileMetadata createDirectoryMetadata(
            String relativeParentPath,
            String folderName,
            Long creatorId,
            String creatorName,
            Long courseId,
            Long projectId,
            Long taskId,
            FileModuleType moduleType,
            FileAccessLevel accessLevel,
            String storageBackendCode) throws FileStorageException;

    /**
     * 根据业务上下文（项目ID、课程ID、任务ID）、模块类型以及在模块内的相对父路径，
     * 查找直接子目录的元数据记录。
     *
     * @param projectId          (可选) 项目ID。
     * @param courseId           (可选) 课程ID。
     * @param taskId             (可选) 任务ID。
     * @param moduleType         (可选) 模块类型，用于更精确地匹配。
     * @param relativeParentPath 模块内相对父路径 (例如 "folderA/" 或 "" 表示模块根)。
     * @param directoryName      要查找的目录名称。
     * @return 匹配的目录元数据对象；如果不存在，则返回null。
     */
    FileMetadata findDirectoryByContextAndPathAndName(
            Long projectId, Long courseId, Long taskId, FileModuleType moduleType,
            String relativeParentPath, String directoryName);

    /**
     * 删除文件或目录。
     *
     * @param request
     * @param operatorId
     * @throws FileStorageException
     */
    void deleteItems(FileDeleteRequest request, Long operatorId) throws FileStorageException;


    void deleteDirectoryByLogicPath(
            Long projectId, Long courseId, Long taskId, FileModuleType moduleType,
            String moduleRelativePath, Long operatorId, boolean logicalOnly, boolean recursive) throws FileStorageException;

}
