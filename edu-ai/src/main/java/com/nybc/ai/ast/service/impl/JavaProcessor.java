package com.nybc.ai.ast.service.impl;

import com.nybc.ai.ast.AnalysisUtil;
import com.nybc.ai.ast.model.CodeBlock;
import com.nybc.ai.ast.model.FileAnalysisResult;
import com.nybc.ai.ast.model.MethodInfo;
import com.nybc.ai.ast.service.LanguageProcessor;
import org.treesitter.*;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;

/**
 * 类描述: Java语言处理器。
 * 实现了 LanguageProcessor 接口，专门负责解析 .java 文件，
 * 能够提取出文件中的类以及每个类的所有方法的详细元数据。
 *
 * <AUTHOR> AI 助手
 * @version : 1.0
 */
public class JavaProcessor implements LanguageProcessor {
    private static final TSLanguage LANGUAGE = new TreeSitterJava();
    private static final String QUERY_STRING = """
        (class_declaration
          name: (identifier) @class.name
          body: (class_body
            (method_declaration) @method
          )
        )
        """;

    @Override
    public String getLanguageName() {
        return "Java";
    }

    @Override
    public FileAnalysisResult process(File file) throws IOException {
        byte[] sourceBytes = Files.readAllBytes(file.toPath());
        List<CodeBlock> extractedBlocks = new ArrayList<>();

        // 依据源码，TSParser等对象使用Cleaner自动管理资源，无需手动关闭。
        TSParser parser = new TSParser();
        parser.setLanguage(LANGUAGE);

        // TSTree 实现了 AutoCloseable，所以这里可以使用 try-with-resources
        try (TSTree tree = parser.parseStringEncoding(null, new String(sourceBytes, StandardCharsets.UTF_8), TSInputEncoding.TSInputEncodingUTF8)) {

            TSQuery query = new TSQuery(LANGUAGE, QUERY_STRING);
            TSQueryCursor cursor = new TSQueryCursor();

            cursor.exec(query, tree.getRootNode());

            TSQueryMatch match = new TSQueryMatch();
            String currentClassName = "Unknown";

            while (cursor.nextMatch(match)) {
                TSNode methodNode = null;

                for (TSQueryCapture capture : match.getCaptures()) {
                    // 使用 query 对象和 capture 的索引来获取捕获名称
                    String captureName = query.captureNameForId(capture.getIndex());
                    TSNode capturedNode = capture.getNode();

                    if ("class.name".equals(captureName)) {
                        currentClassName = AnalysisUtil.getNodeText(capturedNode, sourceBytes);
                    } else if ("method".equals(captureName)) {
                        methodNode = capturedNode;
                    }
                }

                if (methodNode != null) {
                    String methodName = AnalysisUtil.getNodeTextByFieldName(methodNode, "name", sourceBytes, "anonymous_method");
                    String returnType = AnalysisUtil.getNodeTextByFieldName(methodNode, "type", sourceBytes, "void");
                    String parameters = AnalysisUtil.getNodeTextByFieldName(methodNode, "parameters", sourceBytes, "()");
                    String comment = AnalysisUtil.getPrecedingComment(methodNode, sourceBytes);
                    String content = AnalysisUtil.getNodeText(methodNode, sourceBytes);
                    int startLine = methodNode.getStartPoint().getRow() + 1;
                    int endLine = methodNode.getEndPoint().getRow() + 1;

                    MethodInfo methodInfo = new MethodInfo(
                            currentClassName + "." + methodName,
                            returnType,
                            parameters,
                            comment,
                            content,
                            startLine,
                            endLine
                    );
                    extractedBlocks.add(methodInfo);
                }
            }
        }

        return new FileAnalysisResult(file.getPath(), getLanguageName(), extractedBlocks);
    }

}
