package com.nybc.ai.ast.model;

import java.util.List;
import java.util.Objects;

/**
 * 类描述: 项目分析结果顶层数据模型。
 * 这是一个不可变的常规Java类，作为整个静态分析任务的最终产出。
 * 它聚合了项目级别的元数据以及所有被分析文件的详细结果。
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
public final class ProjectAnalysisResult {

    /**
     * 被分析的项目根目录的路径。
     */
    private final String directoryPath;

    /**
     * 通过启发式分析得出的项目类型。
     * 例如："Java后端项目", "前端项目", "混合项目", "未知类型"。
     */
    private final String detectedProjectType;

    /**
     * 项目中所有被成功分析的文件的结果列表。
     */
    private final List<FileAnalysisResult> fileResults;

    /**
     * 全参构造函数，用于创建 ProjectAnalysisResult 的实例。
     *
     * @param directoryPath       项目根目录路径
     * @param detectedProjectType 检测到的项目类型
     * @param fileResults         所有文件的分析结果列表
     */
    public ProjectAnalysisResult(String directoryPath, String detectedProjectType, List<FileAnalysisResult> fileResults) {
        this.directoryPath = directoryPath;
        this.detectedProjectType = detectedProjectType;
        // 创建一个不可变的副本，增强安全性
        this.fileResults = List.copyOf(fileResults);
    }

    // --- 重写 Object 的基础方法 ---

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ProjectAnalysisResult that = (ProjectAnalysisResult) o;
        return Objects.equals(directoryPath, that.directoryPath) &&
                Objects.equals(detectedProjectType, that.detectedProjectType) &&
                Objects.equals(fileResults, that.fileResults);
    }

    @Override
    public int hashCode() {
        return Objects.hash(directoryPath, detectedProjectType, fileResults);
    }

    @Override
    public String toString() {
        return "ProjectAnalysisResult{" +
                "directoryPath='" + directoryPath + '\'' +
                ", detectedProjectType='" + detectedProjectType + '\'' +
                ", analyzedFilesCount=" + fileResults.size() +
                '}';
    }

}
