package com.nybc.ai.ast;

/**
 * 类描述：代码块通用接口。
 * 作为所有具体代码分析结果（如方法、函数、类等）的父接口，
 * 提供了通用的契约，使得不同类型的代码元素可以被统一处理。
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
public interface CodeBlock {
    /**
     * 获取代码块的名称。
     * <p>
     * 对于一个方法，这将是方法名；对于一个函数，是函数名；
     * 对于一个类，是类名。
     *
     * @return 代码块的名称字符串。
     */
    String getName();

    /**
     * 获取代码块在源文件中所占用的总行数。
     * <p>
     * 这个值通常通过 (结束行号 - 开始行号 + 1) 来计算。
     *
     * @return 代码块覆盖的总行数。
     */
    int getLineCount();

}
