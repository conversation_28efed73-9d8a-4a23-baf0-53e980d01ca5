package com.nybc.ai.ast.model;

import java.util.Objects;

/**
 * 类描述: 函数信息数据模型。
 * 这是一个不可变的常规Java类，用于存储从过程式或脚本语言（如C, Python, JavaScript）
 * 中分析出的关于独立函数的详细元数据。它实现了 CodeBlock 接口。
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
public final class FunctionInfo implements CodeBlock {

    /**
     * 函数的名称。
     */
    private final String name;

    /**
     * 函数的返回类型。
     * 对于动态类型语言，可能需要标记为 "dynamic" 或 "unknown"。
     */
    private final String returnType;

    /**
     * 函数的参数列表的字符串表示。
     * 例如："(int count, char* name)"。
     */
    private final String parameters;

    /**
     * 函数上方紧邻的注释，或函数体内的文档字符串 (docstring)。
     */
    private final String comment;

    /**
     * 该函数从开始到结束的完整源代码文本。
     */
    private final String content;

    /**
     * 函数在源文件中开始的行号（从1开始计数）。
     */
    private final int startLine;

    /**
     * 函数在源文件中结束的行号（从1开始计数）。
     */
    private final int endLine;

    /**
     * 全参构造函数，用于创建 FunctionInfo 的实例。
     *
     * @param name       函数名称
     * @param returnType 返回类型
     * @param parameters 参数列表
     * @param comment    注释或文档字符串
     * @param content    函数体完整源码
     * @param startLine  开始行号
     * @param endLine    结束行号
     */
    public FunctionInfo(String name, String returnType, String parameters, String comment, String content, int startLine, int endLine) {
        this.name = name;
        this.returnType = returnType;
        this.parameters = parameters;
        this.comment = comment;
        this.content = content;
        this.startLine = startLine;
        this.endLine = endLine;
    }


    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public int getLineCount() {
        return this.endLine - this.startLine + 1;
    }

    // --- 重写 Object 的基础方法 ---

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        FunctionInfo that = (FunctionInfo) o;
        return startLine == that.startLine &&
                endLine == that.endLine &&
                Objects.equals(name, that.name) &&
                Objects.equals(returnType, that.returnType) &&
                Objects.equals(parameters, that.parameters) &&
                Objects.equals(comment, that.comment) &&
                Objects.equals(content, that.content);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, returnType, parameters, comment, content, startLine, endLine);
    }

    @Override
    public String toString() {
        return "FunctionInfo{" +
                "name='" + name + '\'' +
                ", returnType='" + returnType + '\'' +
                ", parameters='" + parameters + '\'' +
                ", comment='" + comment.substring(0, Math.min(comment.length(), 20)) + "...'" +
                ", content='" + content.substring(0, Math.min(content.length(), 30)) + "...'" +
                ", startLine=" + startLine +
                ", endLine=" + endLine +
                '}';
    }

}
