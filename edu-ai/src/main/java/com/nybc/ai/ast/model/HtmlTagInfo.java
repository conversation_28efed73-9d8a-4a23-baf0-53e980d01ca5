package com.nybc.ai.ast.model;

import java.util.Map;
import java.util.Objects;

/**
 * 类描述: HTML标签信息数据模型。
 * 这是一个不可变的常规Java类，用于存储从HTML文件中分析出的
 * 关于单个HTML标签的详细信息。它实现了 CodeBlock 接口。
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
public final class HtmlTagInfo implements CodeBlock {

    /**
     * HTML标签的名称。
     * 例如: "div", "a", "span"。
     */
    private final String name;

    /**
     * 存储该标签所有属性的映射。
     * 键是属性名 (e.g., "id", "class", "href"), 值是属性值。
     */
    private final Map<String, String> attributes;

    /**
     * 标签体内的原始文本内容或子HTML结构。
     * 为简化分析，这里存储为原始字符串。
     */
    private final String content;

    /**
     * 标签在源文件中开始的行号（从1开始计数）。
     */
    private final int startLine;

    /**
     * 标签在源文件中结束的行号（从1开始计数）。
     */
    private final int endLine;

    /**
     * 全参构造函数，用于创建 HtmlTagInfo 的实例。
     *
     * @param name       标签名称
     * @param attributes 属性映射
     * @param content    标签内容
     * @param startLine  开始行号
     * @param endLine    结束行号
     */
    public HtmlTagInfo(String name, Map<String, String> attributes, String content, int startLine, int endLine) {
        this.name = name;
        this.attributes = attributes;
        this.content = content;
        this.startLine = startLine;
        this.endLine = endLine;
    }


    // --- 实现 CodeBlock 接口的方法 ---

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public int getLineCount() {
        return this.endLine - this.startLine + 1;
    }

    // --- 重写 Object 的基础方法 ---

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        HtmlTagInfo that = (HtmlTagInfo) o;
        return startLine == that.startLine &&
                endLine == that.endLine &&
                Objects.equals(name, that.name) &&
                Objects.equals(attributes, that.attributes) &&
                Objects.equals(content, that.content);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, attributes, content, startLine, endLine);
    }

    @Override
    public String toString() {
        return "HtmlTagInfo{" +
                "name='" + name + '\'' +
                ", attributes=" + attributes +
                ", content='" + content.substring(0, Math.min(content.length(), 30)) + "...'" +
                ", startLine=" + startLine +
                ", endLine=" + endLine +
                '}';
    }

}
