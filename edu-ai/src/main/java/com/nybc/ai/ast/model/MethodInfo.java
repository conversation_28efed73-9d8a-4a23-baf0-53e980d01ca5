package com.nybc.ai.ast.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Objects;

/**
 * 类描述: 方法信息数据模型。
 * 这是一个不可变的常规Java类，用于存储从面向对象语言（如Java, C++, C#）
 * 中分析出的关于方法的详细元数据。它实现了 CodeBlock 接口。
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@Data
@Accessors(chain = true)
public final class MethodInfo implements CodeBlock {

    /**
     * 方法的名称。
     * 为了提供唯一标识，通常建议采用 "所属类名.方法名" 的格式。
     */
    private final String name;

    /**
     * 方法的返回类型。
     * 例如："int", "String", "void"。
     */
    private final String returnType;

    /**
     * 方法的参数列表的字符串表示。
     * 例如："(int a, String b)"。
     */
    private final String parameters;

    /**
     * 方法上方紧邻的注释。
     * 这可以是 Javadoc/Doxygen 等格式的文档注释，或普通的块/行注释。
     */
    private final String comment;

    /**
     * 该方法从开始到结束的完整源代码文本。
     */
    private final String content;

    /**
     * 方法在源文件中开始的行号（从1开始计数）。
     */
    private final int startLine;

    /**
     * 方法在源文件中结束的行号（从1开始计数）。
     */
    private final int endLine;

    /**
     * 全参构造函数，用于创建 MethodInfo 的实例。
     *
     * @param name       方法名称
     * @param returnType 返回类型
     * @param parameters 参数列表
     * @param comment    注释
     * @param content    方法体完整源码
     * @param startLine  开始行号
     * @param endLine    结束行号
     */
    public MethodInfo(String name, String returnType, String parameters, String comment, String content, int startLine, int endLine) {
        this.name = name;
        this.returnType = returnType;
        this.parameters = parameters;
        this.comment = comment;
        this.content = content;
        this.startLine = startLine;
        this.endLine = endLine;
    }


    // --- 实现 CodeBlock 接口的方法 ---

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public int getLineCount() {
        return this.endLine - this.startLine + 1;
    }

    // --- 重写 Object 的基础方法 ---

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        MethodInfo that = (MethodInfo) o;
        return startLine == that.startLine &&
                endLine == that.endLine &&
                Objects.equals(name, that.name) &&
                Objects.equals(returnType, that.returnType) &&
                Objects.equals(parameters, that.parameters) &&
                Objects.equals(comment, that.comment) &&
                Objects.equals(content, that.content);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, returnType, parameters, comment, content, startLine, endLine);
    }

    @Override
    public String toString() {
        return "MethodInfo{" +
                "name='" + name + '\'' +
                ", returnType='" + returnType + '\'' +
                ", parameters='" + parameters + '\'' +
                ", comment='" + comment.substring(0, Math.min(comment.length(), 20)) + "...'" +
                ", content='" + content.substring(0, Math.min(content.length(), 30)) + "...'" +
                ", startLine=" + startLine +
                ", endLine=" + endLine +
                '}';
    }

}
