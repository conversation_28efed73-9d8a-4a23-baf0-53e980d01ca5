package com.nybc.ai.ast.service.impl;

import com.nybc.ai.ast.AnalysisUtil;
import com.nybc.ai.ast.model.CodeBlock;
import com.nybc.ai.ast.model.FileAnalysisResult;
import com.nybc.ai.ast.model.FunctionInfo;
import com.nybc.ai.ast.service.LanguageProcessor;
import org.treesitter.*;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;

/**
 * 类描述: Vue单文件组件(.vue)处理器。
 * 采用两阶段解析策略：首先使用Vue解析器提取<script>块内容，
 * 然后复用TypeScript解析逻辑对脚本内容进行二次深度解析，以提取方法和函数。
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
public class VueProcessor implements LanguageProcessor {
    private static final TSLanguage VUE_LANGUAGE = new TreeSitterVue();
    private static final TSLanguage TS_LANGUAGE = new TreeSitterTypescript();

    private static final String VUE_SCRIPT_QUERY = "(element (start_tag (tag_name) @_tagname) . (raw_text) @script.content (end_tag) (#eq? @_tagname \"script\"))";
    private static final String TS_FUNCTION_QUERY = """
        [
          (function_declaration) @function
          (method_definition) @function
          (arrow_function) @function
          (lexical_declaration (variable_declarator name: (_) value: [(arrow_function) (function)])) @function
        ]
        """;

    @Override
    public String getLanguageName() {
        return "Vue";
    }

    @Override
    public FileAnalysisResult process(File file) throws IOException {
        byte[] vueSourceBytes = Files.readAllBytes(file.toPath());
        List<CodeBlock> extractedBlocks = new ArrayList<>();

        TSParser vueParser = new TSParser();
        vueParser.setLanguage(VUE_LANGUAGE);

        TSTree tree = vueParser.parseStringEncoding(null, new String(vueSourceBytes, StandardCharsets.UTF_8), TSInputEncoding.TSInputEncodingUTF8);
        TSQuery vueQuery = new TSQuery(VUE_LANGUAGE, VUE_SCRIPT_QUERY);
        TSQueryCursor cursor = new TSQueryCursor();
        cursor.exec(vueQuery, tree.getRootNode());

        TSQueryMatch match = new TSQueryMatch();
        if (cursor.nextMatch(match)) {
            for (TSQueryCapture capture : match.getCaptures()) {
                if ("script.content".equals(vueQuery.getCaptureNameForId(capture.getIndex()))) {
                    TSNode scriptContentNode = capture.getNode();
                    String scriptText = AnalysisUtil.getNodeText(scriptContentNode, vueSourceBytes);

                    extractedBlocks.addAll(parseScriptContent(scriptText));
                    break;
                }
            }
        }

        return new FileAnalysisResult(file.getPath(), getLanguageName(), extractedBlocks);
    }

    /**
     * 一个私有的静态辅助方法，用于对<script>块内的纯文本进行深度解析。
     * @param scriptText <script>标签内的源代码字符串。
     * @return 从脚本中提取出的代码块列表。
     */
    private static List<CodeBlock> parseScriptContent(String scriptText) {
        if (scriptText == null || scriptText.isBlank()) {
            return new ArrayList<>();
        }
        byte[] scriptBytes = scriptText.getBytes(StandardCharsets.UTF_8);
        List<CodeBlock> blocks = new ArrayList<>();

        TSParser tsParser = new TSParser();
        tsParser.setLanguage(TS_LANGUAGE);

        TSTree tsTree = tsParser.parseStringEncoding(null, scriptText, TSInputEncoding.TSInputEncodingUTF8);
        TSQuery tsQuery = new TSQuery(TS_LANGUAGE, TS_FUNCTION_QUERY);
        TSQueryCursor tsCursor = new TSQueryCursor();
        tsCursor.exec(tsQuery, tsTree.getRootNode());

        TSQueryMatch tsMatch = new TSQueryMatch();
        while (tsCursor.nextMatch(tsMatch)) {
            for (TSQueryCapture tsCapture : tsMatch.getCaptures()) {
                TSNode node = tsCapture.getNode();
                String name = "anonymous";

                name = AnalysisUtil.getNodeTextByFieldName(node, "name", scriptBytes, "");
                if (name.isEmpty()) {
                    TSNode parent = node.getParent();
                    if (parent != null && "variable_declarator".equals(parent.getType())) {
                        name = AnalysisUtil.getNodeTextByFieldName(parent, "name", scriptBytes, "anonymous");
                    }
                }

                String returnType = AnalysisUtil.getNodeTextByFieldName(node, "return_type", scriptBytes, "inferred");
                String parameters = AnalysisUtil.getNodeTextByFieldName(node, "parameters", scriptBytes, "()");
                String content = AnalysisUtil.getNodeText(node, scriptBytes);
                String comment = AnalysisUtil.getPrecedingComment(node, scriptBytes);
                int startLine = node.getStartPoint().getRow() + 1;
                int endLine = node.getEndPoint().getRow() + 1;

                blocks.add(new FunctionInfo(name, returnType, parameters, comment, content, startLine, endLine));
            }
        }
        return blocks;
    }

}
