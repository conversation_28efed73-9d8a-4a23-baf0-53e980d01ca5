多语言静态代码分析框架 - 功能实现方案
1. 项目目标
   构建一个功能全面、易于扩展的静态代码分析框架。该框架能够：
   接收一个本地项目目录作为输入。
   自动识别项目的主要类型（前端、后端、混合项目等）。
   遍历目录下的所有文件，并识别出可分析的源代码文件。
   对多种语言（Java, Python, C, C++, TypeScript, TSX/React, Vue, HTML）的源代码文件进行深度解析。
   从代码中提取结构化的元数据，例如：
   Java/C++: 类名、方法名、方法参数、返回值、行数以及方法前的注释。
   Python/C: 函数名、参数、返回值、行数以及文档字符串/注释。
   TS/TSX/Vue: 组件、函数、方法、箭头函数的名称、参数、行数等。
   HTML: 标签、属性等。
   将所有分析结果聚合成一份结构清晰的报告。
2. 核心设计原则与架构
   为实现高内聚、低耦合、易扩展的目标，框架将基于以下设计原则：
   策略模式 (Strategy Pattern): 这是整个框架的基石。每一种语言的分析逻辑将被封装在一个独立的“策略”（LanguageProcessor）类中。主程序根据文件类型动态选择并应用相应的策略，从而将“如何分析”（策略）与“何时分析”（主逻辑）解耦。
   面向接口编程: 定义清晰的接口（如 LanguageProcessor），所有具体实现都遵循此接口，保证了组件的可替换性。
   数据模型驱动 (Model-Driven): 使用 Java record 定义一系列不可变的、强类型的数据传输对象（DTO），用于清晰地承载分析结果，确保数据在不同层级间传递的准确性。
   并发处理: 利用 JDK 21 的虚拟线程（Virtual Threads）对文件进行并发分析，极大地提升了处理大量文件时的 I/O 和 CPU 效率。
   注册表模式 (Registry Pattern): 通过一个中央注册表（LanguageRegistry）来管理文件扩展名与语言处理器之间的映射关系，使新增语言支持变得简单。
3. 组件划分 (文件交付蓝图)
   整个项目将由以下核心组件构成，这也将是我们后续 逐一交付文件 的顺序：
   pom.xml - 项目基石:
   职责: 定义所有项目依赖。这是第一步，也是最重要的一步，它将引入 tree-sitter 核心库以及所有我们需要的特定语言的解析器（Java, Python, C, C++, TS, Vue, HTML 等）。同时也会包含 Jackson 库用于最终结果的JSON格式化输出。
   数据模型 (Data Models) - analysis/results/:
   职责: 定义分析结果的结构。这些是纯数据类。
   CodeBlock.java: 一个接口，作为所有代码块（方法、函数）的通用抽象。
   MethodInfo.java: 用于存储面向对象语言（Java, C++）的方法详情。
   FunctionInfo.java: 用于存储过程式语言（C, Python）的函数详情。
   HtmlTagInfo.java: 用于存储HTML标签的详情。
   FileAnalysisResult.java: 存储单个文件的分析结果。
   ProjectAnalysisResult.java: 存储整个项目的最终分析报告。
   辅助工具类 (Utility Class) - analysis/util/:
   职责: 封装可复用的辅助函数。
   AnalysisUtil.java: 包含从 AST 节点安全提取文本、获取节点前的注释等静态方法，避免在每个处理器中重复编写相同逻辑。
   处理器接口 (Processor Interface) - analysis/processors/:
   职责: 定义策略模式的核心契约。
   LanguageProcessor.java: 声明 process(File file) 和 getLanguageName() 方法，所有具体的语言处理器都必须实现此接口。
   具体语言处理器 (Concrete Processors) - analysis/processors/:
   职责: 实现对特定语言的完整分析逻辑。
   JavaProcessor.java: 使用 tree-sitter-java 解析器，通过S-expression查询定位类和方法，并提取所有元数据。
   PythonProcessor.java: 使用 tree-sitter-python 解析器，查询函数定义并特别处理 docstring 的提取。
   CProcessor.java: 使用 tree-sitter-c 解析器，查询函数定义。
   CppProcessor.java: 使用 tree-sitter-cpp 解析器，查询类、方法和函数定义。
   TypeScriptProcessor.java: 使用 tree-sitter-typescript，这是一个功能强大的处理器，能够处理 .ts 和 .tsx (React) 文件，通过复杂的查询捕获常规函数、箭头函数、类方法等多种形式。
   VueProcessor.java: 这将是一个特殊的两阶段处理器。第一阶段使用 tree-sitter-vue 找到 <script> 块，第二阶段提取其内容，并调用 TypeScriptProcessor 的逻辑对这部分脚本进行深度分析。
   HtmlProcessor.java: 使用 tree-sitter-html 解析器，用于提取HTML文档结构，如标签和属性。
   语言注册表 (Language Registry) - analysis/:
   职责: 作为处理器工厂和分发中心。
   LanguageRegistry.java: 内部维护一个 Map，将文件扩展名（如 ".java"）映射到对应的 LanguageProcessor 实例。
   主分析器 (Main Analyzer) - analysis/:
   职责: 驱动整个分析流程的协调器。
   ProjectAnalyzer.java:
   接收目录路径。
   遍历文件，同时根据 pom.xml, package.json 等关键文件识别项目类型。
   对每个文件，通过 LanguageRegistry 请求相应的处理器。
   使用虚拟线程池将文件和处理器分发下去，进行并发处理。
   收集所有 Future 的结果并汇总。
   程序入口 (Application Entry Point) - analysis/:
   职责: 启动程序并展示结果。
   Main.java: 一个简单的启动类，负责解析命令行参数（项目路径），调用 ProjectAnalyzer，并使用 Jackson 将最终的 ProjectAnalysisResult 对象格式化为易于阅读的 JSON 格式打印到控制台。