package com.nybc.edu.core.config;

import com.dtflys.forest.exceptions.ForestRuntimeException;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.dtflys.forest.interceptor.Interceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 类描述：
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@Component
public class ForestErrorLogInterceptor implements Interceptor<Object> {
    @Override
    public void onError(ForestRuntimeException ex, ForestRequest request, ForestResponse response) {
        log.error("Forest HTTP请求出错: URL={}, Method={}, Status={}, ExceptionMessage={}",
                  request.getUrl(), request.getMethod().getMethodName(), response != null ? response.getStatusCode() : "N/A", ex.getMessage());
    }

}