<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.4.5</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.nybc</groupId>
    <artifactId>nybc-edu</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>nybc-edu</name>
    <modules>
        <module>edu-deploy</module>
        <module>edu-common</module>
        <module>edu-start</module>
        <module>edu-user</module>
        <module>edu-core</module>
        <module>edu-ops</module>
        <module>edu-log</module>
        <module>edu-biz</module>
        <module>edu-course</module>
        <module>edu-storage</module>
        <module>edu-message</module>
        <module>edu-tag</module>
        <module>edu-ai</module>
    </modules>

    <properties>
        <java.version>21</java.version>
        <revision>1.0.0-SNAPSHOT</revision>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring.boot.version>3.4.5</spring.boot.version>
        <nybc-common-tool.version>1.0.1.7-RELEASE</nybc-common-tool.version>
        <nybc-common-model.version>1.0.2.5-RELEASE</nybc-common-model.version>
        <guava.version>33.3.1-jre</guava.version>
        <mybatis-boot.version>3.0.4</mybatis-boot.version>
        <mybatis.version>3.5.19</mybatis.version>
        <pagehelper>2.1.0</pagehelper>
        <jsqlparser>4.9</jsqlparser>
        <jgit.version>7.1.0.202411261347-r</jgit.version>
        <lombok.version>1.18.36</lombok.version>
        <fastjson.version>2.0.57</fastjson.version>
        <redisson>3.39.0</redisson>
        <okhttp.version>4.12.0</okhttp.version>
        <minio.version>8.5.17</minio.version>
        <jjwt.version>0.12.6</jjwt.version>
        <transmittable-thread-local.version>2.14.5</transmittable-thread-local.version>
        <commons-collections4.version>4.4</commons-collections4.version>
        <commons-io.version>2.19.0</commons-io.version>
        <commons-net.version>3.11.1</commons-net.version>
        <commons-fileupload.version>1.5</commons-fileupload.version>
        <commons-exec.version>1.4.0</commons-exec.version>
        <commons-codec.version>1.17.1</commons-codec.version>
        <commons-lang3.version>3.17.0</commons-lang3.version>
        <commons-text.version>1.13.0</commons-text.version>
        <commons-dbutils.version>1.8.1</commons-dbutils.version>
        <commons-pool2.version>2.12.1</commons-pool2.version>
        <commons-configuration2.version>2.7</commons-configuration2.version>
        <commons-jexl3.version>3.4.0</commons-jexl3.version>
        <commons-compress.version>1.27.1</commons-compress.version>
        <swagger.version>2.2.29</swagger.version>
        <fastexcel.version>1.2.0</fastexcel.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.nybc</groupId>
                <artifactId>edu-core</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.nybc</groupId>
                <artifactId>edu-user</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.nybc</groupId>
                <artifactId>edu-log</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.nybc</groupId>
                <artifactId>edu-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.nybctech</groupId>
                <artifactId>nybc-common-model</artifactId>
                <version>${nybc-common-model.version}</version>
            </dependency>
            <dependency>
                <groupId>com.nybctech</groupId>
                <artifactId>nybc-common-tool</artifactId>
                <version>${nybc-common-tool.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.xmlunit</groupId>
                <artifactId>xmlunit-core</artifactId>
                <version>2.10.0</version>
            </dependency>
            <dependency>
                <groupId>org.xmlunit</groupId>
                <artifactId>xmlunit-legacy</artifactId>
                <version>2.10.0</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.properties</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                    <configuration>
                        <skip>true</skip>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>3.1.3</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.3.1</version>
                    <executions>
                        <execution>
                            <id>attach-sources</id>
                            <goals>
                                <goal>jar</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.3.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>3.3.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <source>21</source>
                        <target>21</target>
                        <encoding>UTF-8</encoding>
                        <compilerArgument>-XDignore.symbol.file</compilerArgument>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.springframework.boot</groupId>
                                <artifactId>spring-boot-configuration-processor</artifactId>
                                <version>${spring.boot.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                        </annotationProcessorPaths>
                        <!-- 编译参数写在 arg 内，解决 Spring Boot 3.2 的 Parameter Name Discovery 问题 -->
                        <debug>false</debug>
                        <compilerArgs>
                            <arg>-parameters</arg>
                        </compilerArgs>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
