CREATE EXTENSION pg_trgm;
--启用zhparser插件
CREATE EXTENSION zhparser;
--创建一个名为testzhcfg的文本搜索配置，该配置使用zhparser解析器。
CREATE TEXT SEARCH CONFIGURATION zh (PARSER = zhparser);
--添加令牌映射，在testzhcfg配置中为小写字母a-z添加映射到simple词典。
ALTER TEXT SEARCH CONFIGURATION zh ADD MAPPING FOR n,v,a,i,e,l WITH simple;

-- 任务评论表
-- 存储用户在任务下进行的讨论和评论内容。

CREATE TABLE t_task_comment (
    id BIGSERIAL PRIMARY KEY, -- 评论唯一标识ID，使用BIGSERIAL自动递增

    task_id BIGINT NOT NULL, -- 关联的任务ID
    project_id BIGINT NOT NULL, -- 关联的项目ID（冗余字段，方便查询和权限校验）
    comment_content TEXT NOT NULL, -- 评论内容，支持HTML格式
    parent_comment_id BIGINT, -- 父评论ID，用于支持评论回复（自关联），如果为顶层评论则为NULL

    -- 全文搜索向量字段，由触发器自动维护。初始化时允许为NULL
    -- 'zh' 是您配置的中文文本搜索配置名称
    tsv_comment_content tsvector,

    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 评论创建时间
    create_user BIGINT NOT NULL, -- 评论创建人ID
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 评论最后更新时间
    update_user BIGINT, -- 评论最后修改人ID

    -- 逻辑删除标记 (0:未删除, 1:已删除)。默认0表示未删除
    deleted INTEGER NOT NULL DEFAULT 0
);

-- 为表和列添加注释 (推荐的良好实践)
COMMENT ON TABLE t_task_comment IS '任务评论表，存储用户在特定任务下的讨论和评论内容。';
COMMENT ON COLUMN t_task_comment.id IS '评论唯一标识ID';
COMMENT ON COLUMN t_task_comment.task_id IS '关联的任务ID，外键指向t_project_task表';
COMMENT ON COLUMN t_task_comment.project_id IS '关联的项目ID，冗余字段，外键指向t_project表，方便查询和权限校验';
COMMENT ON COLUMN t_task_comment.comment_content IS '评论内容，支持HTML格式和富文本内容';
COMMENT ON COLUMN t_task_comment.parent_comment_id IS '父评论ID，用于支持评论回复（自关联），如果为顶层评论则为NULL';
COMMENT ON COLUMN t_task_comment.tsv_comment_content IS '评论内容经过全文搜索配置向量化后的表示，由触发器自动维护，用于全文搜索';
COMMENT ON COLUMN t_task_comment.create_time IS '评论创建时间';
COMMENT ON COLUMN t_task_comment.create_user IS '评论创建人ID，外键指向t_user表';
COMMENT ON COLUMN t_task_comment.update_time IS '评论最后更新时间';
COMMENT ON COLUMN t_task_comment.update_user IS '评论最后修改人ID，外键指向t_user表';
COMMENT ON COLUMN t_task_comment.deleted IS '逻辑删除标记 (0:未删除, 1:已删除)';


-- 创建索引 (在 CREATE TABLE 之后单独执行)

-- 全文搜索索引 (使用 GIN 索引加速 tsvector 字段的查询)
-- 索引名称通常以 idx_表名_字段名 命名
CREATE INDEX idx_t_task_comment_tsv ON t_task_comment USING GIN (tsv_comment_content);

-- 普通索引 (加速常见查询和外键关联)
CREATE INDEX idx_t_task_comment_task_id ON t_task_comment (task_id);
CREATE INDEX idx_t_task_comment_project_id ON t_task_comment (project_id);
CREATE INDEX idx_t_task_comment_create_time ON t_task_comment (create_time DESC); -- 按时间倒序查询常用
CREATE INDEX idx_t_task_comment_parent_id ON t_task_comment (parent_comment_id);
-- 组合索引（如果按任务ID和创建时间查询评论树很频繁）
CREATE INDEX idx_t_task_comment_task_create_parent ON t_task_comment (task_id, create_time DESC, parent_comment_id);


-- 定义全文搜索触发器函数 (在创建表和索引之后单独执行)
-- 'zh' 是您配置的中文文本搜索配置名称 (已假设您已执行：CREATE EXTENSION zhparser; CREATE TEXT SEARCH CONFIGURATION zh ...;)
CREATE OR REPLACE FUNCTION update_task_comment_tsv_trigger_func() RETURNS trigger AS $$
BEGIN
    -- 使用 'zh' 配置将评论内容转换为tsvector
    NEW.tsv_comment_content := to_tsvector('zh', NEW.comment_content);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器 (在 CREATE TRIGGER FUNCTION 之后单独执行)
-- 在 t_task_comment 表的 BEFORE INSERT OR UPDATE 事件上触发，确保在数据写入前更新 tsvector 字段
CREATE TRIGGER t_task_comment_tsv_update_trigger
    BEFORE INSERT OR UPDATE ON t_task_comment
    FOR EACH ROW EXECUTE PROCEDURE update_task_comment_tsv_trigger_func();




create table nybc_edu.t_user
(id              bigint primary key          not null default nextval('t_user_id_seq'::regclass), -- 主键ID，平台所有用户的唯一标识
 user_name       character varying(100)      not null,                                            -- 登录账号，全局唯一，可以是学号、工号、邮箱、手机号或自定义用户名
 password_hash   character varying(255)      not null,                                            -- 密码的哈希值，严禁存储明文
 nick_name       character varying(255),                                                          -- 用户昵称或显示名
 full_name       character varying(100)      not null,                                            -- 用户真实姓名
 mobile          character varying(20),                                                           -- 手机号码
 email           character varying(100),                                                          -- 邮箱
 avatar_url      character varying(255),                                                          -- 头像地址
 status          user_status_enum            not null default 'active'::user_status_enum,         -- 账户状态：active-活跃, inactive-非活跃, suspended-暂停, pending_approval-待审批
 source_from     character varying(255),                                                          -- 用户来源，如 school_import, enterprise_register, admin_create
 last_login_time timestamp without time zone,                                                     -- 最后登录时间 (不含时区信息)
 create_time     timestamp without time zone not null default now(),                              -- 记录创建时间 (不含时区信息)
 update_time     timestamp without time zone,                                                     -- 记录最后更新时间 (不含时区信息)
 deleted         integer,
 create_user     bigint,
 update_user     bigint
);
create unique index t_user_email_key on t_user using btree (email);
create unique index t_user_mobile_key on t_user using btree (mobile);
comment on table nybc_edu.t_user is '基础用户信息表，存储平台所有用户的核心身份与认证信息';
comment on column nybc_edu.t_user.id is '主键ID，平台所有用户的唯一标识';
comment on column nybc_edu.t_user.user_name is '登录账号，全局唯一，可以是学号、工号、邮箱、手机号或自定义用户名';
comment on column nybc_edu.t_user.password_hash is '密码的哈希值，严禁存储明文';
comment on column nybc_edu.t_user.nick_name is '用户昵称或显示名';
comment on column nybc_edu.t_user.full_name is '用户真实姓名';
comment on column nybc_edu.t_user.mobile is '手机号码';
comment on column nybc_edu.t_user.email is '邮箱';
comment on column nybc_edu.t_user.avatar_url is '头像地址';
comment on column nybc_edu.t_user.status is '账户状态：active-活跃, inactive-非活跃, suspended-暂停, pending_approval-待审批';
comment on column nybc_edu.t_user.source_from is '用户来源，如 school_import, enterprise_register, admin_create';
comment on column nybc_edu.t_user.last_login_time is '最后登录时间 (不含时区信息)';
comment on column nybc_edu.t_user.create_time is '记录创建时间 (不含时区信息)';
comment on column nybc_edu.t_user.update_time is '记录最后更新时间 (不含时区信息)';


-- 2. t_role (平台角色表)
CREATE TABLE t_role
(id            SERIAL PRIMARY KEY,                        -- 角色ID
 role_key      VARCHAR(50) UNIQUE NOT NULL,               -- 角色键，系统内部标识，对应 RoleEnum.key
 role_name     VARCHAR(50)        NOT NULL,               -- 角色名称，用于界面显示，对应 RoleEnum.name
 role_type_key VARCHAR(50)        NOT NULL,               -- 角色类型键，对应 RoleTypeEnum.value
 description   TEXT               NULL,                   -- 角色描述
 create_time   TIMESTAMP          NOT NULL DEFAULT NOW(), -- 创建时间
 update_time   TIMESTAMP          NULL                    -- 修改时间，由应用或触发器更新
);
COMMENT ON TABLE t_role IS '平台角色表，存储平台中所有角色的定义信息';
COMMENT ON COLUMN t_role.id IS '角色ID';
COMMENT ON COLUMN t_role.role_key IS '角色键，系统内部标识，对应 RoleEnum.key，全局唯一';
COMMENT ON COLUMN t_role.role_name IS '角色名称，用于界面显示，对应 RoleEnum.name';
COMMENT ON COLUMN t_role.role_type_key IS '角色类型键，对应 RoleTypeEnum.value，如 TENANT, CROP, SCH, PROJECT';
COMMENT ON COLUMN t_role.description IS '角色描述';
COMMENT ON COLUMN t_role.create_time IS '记录创建时间 (不含时区信息)';
COMMENT ON COLUMN t_role.update_time IS '记录最后更新时间 (不含时区信息)';


-- 3. t_user_role (用户角色关联表)
CREATE TABLE t_user_role
(id          BIGSERIAL PRIMARY KEY,            -- 主键ID
 user_id     BIGINT    NOT NULL,               -- 关联到 t_user 表的 ID (无外键)
 role_id     INTEGER   NOT NULL,               -- 关联到 t_role 表的 ID (无外键)
 create_time TIMESTAMP NOT NULL DEFAULT NOW(), -- 关联创建时间

    -- Constraints
 UNIQUE (user_id, role_id)                     -- 用户和角色的联合唯一，确保一个用户不会被赋予同一个角色两次
);
COMMENT ON TABLE t_user_role IS '用户角色关联表，记录用户与角色之间的多对多关系';
COMMENT ON COLUMN t_user_role.id IS '主键ID';
COMMENT ON COLUMN t_user_role.user_id IS '关联到 t_user 表的 ID';
COMMENT ON COLUMN t_user_role.role_id IS '关联到 t_role 表的 ID';
COMMENT ON COLUMN t_user_role.create_time IS '关联创建时间 (不含时区信息)';


-- 4. 组织结构表 (t_school, t_company)
CREATE TABLE t_school
(id          BIGSERIAL PRIMARY KEY,                      -- 学校ID
 school_name VARCHAR(100) UNIQUE NOT NULL,               -- 学校名称
 school_code VARCHAR(50) UNIQUE  NULL,                   -- 学校代码
 deleted     BOOLEAN             NOT NULL DEFAULT FALSE, -- 是否已逻辑删除
 create_time TIMESTAMP           NOT NULL DEFAULT NOW(), -- 创建时间
 update_time TIMESTAMP           NULL                    -- 修改时间，由应用或触发器更新
);
COMMENT ON TABLE t_tenant_school IS '学校信息表';
COMMENT ON COLUMN t_tenant_school.id IS '学校ID';
COMMENT ON COLUMN t_tenant_school.school_name IS '学校名称，全局唯一';
COMMENT ON COLUMN t_tenant_school.school_code IS '学校代码，全局唯一，可选';
COMMENT ON COLUMN t_tenant_school.deleted IS '是否已逻辑删除';
COMMENT ON COLUMN t_tenant_school.create_time IS '记录创建时间 (不含时区信息)';
COMMENT ON COLUMN t_tenant_school.update_time IS '记录最后更新时间 (不含时区信息)';


CREATE TABLE t_company
(id                         BIGSERIAL PRIMARY KEY,                      -- 企业ID
 company_name               VARCHAR(100) UNIQUE NOT NULL,               -- 企业名称
 unified_social_credit_code VARCHAR(50) UNIQUE  NULL,                   -- 统一社会信用代码
 deleted                    BOOLEAN             NOT NULL DEFAULT FALSE, -- 是否已逻辑删除
 create_time                TIMESTAMP           NOT NULL DEFAULT NOW(), -- 创建时间
 update_time                TIMESTAMP           NULL                    -- 修改时间，由应用或触发器更新
);
COMMENT ON TABLE t_company IS '企业信息表';
COMMENT ON COLUMN t_company.id IS '企业ID';
COMMENT ON COLUMN t_company.company_name IS '企业名称，全局唯一';
COMMENT ON COLUMN t_company.unified_social_credit_code IS '统一社会信用代码，可选，全局唯一';
COMMENT ON COLUMN t_company.deleted IS '是否已逻辑删除';
COMMENT ON COLUMN t_company.create_time IS '记录创建时间 (不含时区信息)';
COMMENT ON COLUMN t_company.update_time IS '记录最后更新时间 (不含时区信息)';


-- 5. 角色特定的详细信息表

-- t_student_detail (学生详细信息表)
CREATE TABLE t_student_detail
(id              BIGSERIAL PRIMARY KEY,                     -- 主键ID
 user_id         BIGINT UNIQUE      NOT NULL,               -- 关联到 t_user 表的 ID，学生的唯一用户关联 (无外键)
 school_id       BIGINT             NOT NULL,               -- 关联到 t_school 表的 ID，学生所属学校 (无外键)
 student_number  VARCHAR(50) UNIQUE NOT NULL,               -- 学号，学生在学校的唯一标识，全局唯一
 specialty           VARCHAR(100)       NULL,                   -- 专业名称
 grade           VARCHAR(50)        NULL,                   -- 年级，如 2021级
 class_name      VARCHAR(100)       NULL,                   -- 班级名称
 enrollment_year SMALLINT           NULL,                   -- 入学年份
 graduation_year SMALLINT           NULL,                   -- 预计毕业年份
    -- 学生其他详细信息：如 政治面貌、籍贯、教育背景（非本校）、荣誉奖项、技能标签等
 create_time     TIMESTAMP          NOT NULL DEFAULT NOW(), -- 创建时间
 update_time     TIMESTAMP          NULL                    -- 修改时间，由应用或触发器更新
);
COMMENT ON TABLE t_student_detail IS '学生详细信息表，存储学生角色的特有属性';
COMMENT ON COLUMN t_student_detail.id IS '主键ID';
COMMENT ON COLUMN t_student_detail.user_id IS '关联到 t_user 表的 ID，学生的唯一用户关联';
COMMENT ON COLUMN t_student_detail.school_id IS '关联到 t_school 表的 ID，学生所属学校';
COMMENT ON COLUMN t_student_detail.student_number IS '学号，学生在学校的唯一标识，全局唯一';
COMMENT ON COLUMN t_student_detail.specialty IS '专业名称';
COMMENT ON COLUMN t_student_detail.grade IS '年级，如 2021级';
COMMENT ON COLUMN t_student_detail.class_name IS '班级名称';
COMMENT ON COLUMN t_student_detail.enrollment_year IS '入学年份';
COMMENT ON COLUMN t_student_detail.graduation_year IS '预计毕业年份';
COMMENT ON COLUMN t_student_detail.create_time IS '记录创建时间 (不含时区信息)';
COMMENT ON COLUMN t_student_detail.update_time IS '记录最后更新时间 (不含时区信息)';


-- t_teacher_detail (教师详细信息表)
CREATE TABLE t_teacher_detail
(id             BIGSERIAL PRIMARY KEY,                     -- 主键ID
 user_id        BIGINT UNIQUE      NOT NULL,               -- 关联到 t_user 表的 ID，教师的唯一用户关联 (无外键)
 school_id      BIGINT             NOT NULL,               -- 关联到 t_school 表的 ID，教师所属学校 (无外键)
 teacher_number VARCHAR(50) UNIQUE NULL,                   -- 工号，可能非强制唯一
 department     VARCHAR(100)       NULL,                   -- 所属院系/部门
 title          VARCHAR(100)       NULL,                   -- 职称/职务，如 教授, 讲师, 辅导员
    -- 教师其他详细信息
 create_time    TIMESTAMP          NOT NULL DEFAULT NOW(), -- 创建时间
 update_time    TIMESTAMP          NULL                    -- 修改时间，由应用或触发器更新
);
COMMENT ON TABLE t_teacher_detail IS '教师详细信息表，存储教师角色的特有属性';
COMMENT ON COLUMN t_teacher_detail.id IS '主键ID';
COMMENT ON COLUMN t_teacher_detail.user_id IS '关联到 t_user 表的 ID，教师的唯一用户关联';
COMMENT ON COLUMN t_teacher_detail.school_id IS '关联到 t_school 表的 ID，教师所属学校';
COMMENT ON COLUMN t_teacher_detail.teacher_number IS '工号，教师在学校的标识，可能非强制唯一';
COMMENT ON COLUMN t_teacher_detail.department IS '所属院系或部门';
COMMENT ON COLUMN t_teacher_detail.title IS '职称或职务，如 教授, 副教授, 讲师, 辅导员等';
COMMENT ON COLUMN t_teacher_detail.create_time IS '记录创建时间 (不含时区信息)';
COMMENT ON COLUMN t_teacher_detail.update_time IS '记录最后更新时间 (不含时区信息)';


-- t_corporate_user_detail (企业成员详细信息表)
CREATE TABLE t_corporate_user_detail
(id          BIGSERIAL PRIMARY KEY,                      -- 主键ID
 user_id     BIGINT UNIQUE       NOT NULL,               -- 关联到 t_user 表的 ID，企业成员的唯一用户关联 (无外键)
 company_id  BIGINT              NOT NULL,               -- 关联到 t_company 表的 ID，企业成员所属企业 (无外键)
 job_number  VARCHAR(50) UNIQUE  NULL,                   -- 工号 (企业内部)
 department  VARCHAR(100)        NULL,                   -- 所属部门
 position    VARCHAR(100)        NULL,                   -- 职位
 work_email  VARCHAR(100) UNIQUE NULL,                   -- 工作邮箱
 work_phone  VARCHAR(20) UNIQUE  NULL,                   -- 工作电话
    -- 企业成员其他详细信息
 create_time TIMESTAMP           NOT NULL DEFAULT NOW(), -- 创建时间
 update_time TIMESTAMP           NULL                    -- 修改时间，由应用或触发器更新
);
COMMENT ON TABLE t_corporate_user_detail IS '企业成员详细信息表，存储企业成员角色的特有属性';
COMMENT ON COLUMN t_corporate_user_detail.id IS '主键ID';
COMMENT ON COLUMN t_corporate_user_detail.user_id IS '关联到 t_user 表的 ID，企业成员的唯一用户关联';
COMMENT ON COLUMN t_corporate_user_detail.company_id IS '关联到 t_company 表的 ID，企业成员所属企业';
COMMENT ON COLUMN t_corporate_user_detail.job_number IS '企业内部工号，可选，可能唯一';
COMMENT ON COLUMN t_corporate_user_detail.department IS '所属部门';
COMMENT ON COLUMN t_corporate_user_detail.position IS '职位';
COMMENT ON COLUMN t_corporate_user_detail.work_email IS '工作邮箱，可选，可能唯一';
COMMENT ON COLUMN t_corporate_user_detail.work_phone IS '工作电话，可选，可能唯一';
COMMENT ON COLUMN t_corporate_user_detail.create_time IS '记录创建时间 (不含时区信息)';
COMMENT ON COLUMN t_corporate_user_detail.update_time IS '记录最后更新时间 (不含时区信息)';


-- t_system_admin_detail (系统管理员详细信息表)
CREATE TABLE t_system_admin_detail
(id          BIGSERIAL PRIMARY KEY,                            -- 主键ID
 user_id     BIGINT UNIQUE    NOT NULL,                        -- 关联到 t_user 表的 ID，管理员的唯一用户关联 (无外键)
 admin_level admin_level_enum NOT NULL DEFAULT 'normal_admin', -- 管理员级别
    -- 其他管理员特定信息
 create_time TIMESTAMP        NOT NULL DEFAULT NOW(),          -- 创建时间
 update_time TIMESTAMP        NULL                             -- 修改时间，由应用或触发器更新
);
COMMENT ON TABLE t_system_admin_detail IS '系统管理员详细信息表，存储系统管理员角色的特有属性';
COMMENT ON COLUMN t_system_admin_detail.id IS '主键ID';
COMMENT ON COLUMN t_system_admin_detail.user_id IS '关联到 t_user 表的 ID，管理员的唯一用户关联';
COMMENT ON COLUMN t_system_admin_detail.admin_level IS '管理员级别：super_admin-超级管理员, normal_admin-普通管理员, data_admin-数据管理员等';
COMMENT ON COLUMN t_system_admin_detail.create_time IS '记录创建时间 (不含时区信息)';
COMMENT ON COLUMN t_system_admin_detail.update_time IS '记录最后更新时间 (不含时区信息)';


-- ----------------------------
-- Initial Data (Populate t_role based on RoleEnum)
-- ----------------------------
INSERT INTO t_role (role_key, role_name, role_type_key, description, create_time)
VALUES ('SUPER_ADMIN', '超级管理员', 'TENANT', '超级管理员 super-admin', NOW()),
       ('SYSTEM_USER', '平台用户', 'TENANT', '平台用户 SYSTEM_USER', NOW()),
       ('CROP_ADMIN', '企业负责人', 'CROP', '企业负责人', NOW()),
       ('CROP_USER', '企业成员', 'CROP', '企业成员', NOW()),
       ('SCH_TECH', '老师', 'SCH', '学校老师', NOW()),
       ('SCH_STU', '学生', 'SCH', '学校学生', NOW()),
       ('PROJECT_ADMIN', '项目经理', 'PROJECT', '负责管理项目的用户', NOW()),
       ('PROJECT_TEST', '测试', 'PROJECT', '项目测试角色', NOW()),
       ('PROJECT_DEV', '开发', 'PROJECT', '项目开发角色', NOW()),
       ('PROJECT_PM', '产品经理', 'PROJECT', '项目产品经理角色', NOW())


CREATE TABLE t_user_oauth2
(id                      BIGSERIAL PRIMARY KEY,               -- 主键ID
 user_id                 BIGINT       NOT NULL,               -- 关联到 t_user 表的 ID (无外键)
 provider_type           VARCHAR(50)  NOT NULL,               -- 三方登录类型，对应三方服务商，如 'wechat', 'qq', 'github'
 provider_unique_id      VARCHAR(255) NOT NULL,               -- 三方服务商提供的用户唯一ID
 provider_username       VARCHAR(255) NULL,                   -- 三方用户的账号/昵称 (服务商提供)
 credentials             VARCHAR(255) NOT NULL,               -- 三方登录获取的认证信息 (如 access_token)，**敏感信息，考虑加密存储**
 credentials_expire_time TIMESTAMP    NULL,                   -- 三方登录认证信息过期时间 (不含时区信息)
 location                VARCHAR(255) NULL,                   -- 三方用户关联的地理位置信息 (可选)
 create_time             TIMESTAMP    NOT NULL DEFAULT NOW(), -- 绑定时间 (不含时区信息)
 update_time             TIMESTAMP    NULL                    -- 修改时间，由应用或触发器更新 (不含时区信息)
);

-- Comments
COMMENT ON TABLE t_user_oauth2 IS '三方登录账户信息表，记录平台用户与外部OAuth2/OpenID Connect提供者账户的绑定关系';
COMMENT ON COLUMN t_user_oauth2.id IS '主键ID';
COMMENT ON COLUMN t_user_oauth2.user_id IS '关联到 t_user 表的 ID，平台用户的主键';
COMMENT ON COLUMN t_user_oauth2.provider_type IS '三方登录提供者类型，如 ''wechat'', ''qq'', ''github''';
COMMENT ON COLUMN t_user_oauth2.provider_unique_id IS '三方服务商为该用户提供的唯一ID，在特定提供者类型下唯一';
COMMENT ON COLUMN t_user_oauth2.provider_username IS '三方服务商提供的用户账号或昵称 (可选)';
COMMENT ON COLUMN t_user_oauth2.credentials IS '三方登录获取的认证信息 (如 access_token)，应妥善保护或加密存储';
COMMENT ON COLUMN t_user_oauth2.credentials_expire_time IS '三方登录认证信息过期时间 (不含时区信息)';
COMMENT ON COLUMN t_user_oauth2.location IS '三方用户关联的地理位置信息 (可选)';
COMMENT ON COLUMN t_user_oauth2.create_time IS '记录绑定时间 (不含时区信息)';
COMMENT ON COLUMN t_user_oauth2.update_time IS '记录最后更新时间 (不含时区信息)';
create table t_user_login_log
(id          BIGSERIAL PRIMARY KEY,                              -- 访问ID
 user_id     bigint                      not null default 0,     -- 用户编号
 user_type   character varying(50)       not null default 0,     -- 用户类型
 user_name   character varying(50)       not null default '',    -- 用户账号
 user_ip     character varying(50),                              -- 用户 IP
 user_agent  character varying(512),                             -- 浏览器 UA
 create_time timestamp without time zone not null default now(), -- 创建时间
 tenant_id   bigint
);
create index idx_create_time on t_user_login_log using btree (create_time);
create index idx_user_id on t_user_login_log using btree (user_id);
comment on table t_user_login_log is '系统访问记录';
comment on column t_user_login_log.id is '访问ID';
comment on column t_user_login_log.user_id is '用户编号';
comment on column t_user_login_log.user_type is '用户类型';
comment on column t_user_login_log.user_name is '用户账号';
comment on column t_user_login_log.user_ip is '用户 IP';
comment on column t_user_login_log.user_agent is '浏览器 UA';
comment on column t_user_login_log.create_time is '创建时间';
CREATE TABLE t_user_password_log
(id                  BIGSERIAL PRIMARY KEY,                             -- 主键ID
 user_id             BIGINT                      NOT NULL,              -- 关联到 t_user 表的 ID (无外键)
 change_time         TIMESTAMP WITHOUT TIME ZONE NOT NULL,              -- 密码修改发生的时间
 changed_by_user_id  BIGINT                      NULL,                  -- 执行密码修改操作的用户ID (如管理员)，如果用户自己修改则为NULL (无外键)
 next_reminder_time  TIMESTAMP WITHOUT TIME ZONE NULL,                  -- 基于密码策略计算的下次密码修改提醒时间点 (不含时区信息)
 policy_disable_time TIMESTAMP WITHOUT TIME ZONE NULL,                  -- 基于密码策略计算的账户禁用时间点 (如果到期未修改密码，不含时区信息)
 tenant_id           BIGINT                      NULL,                  -- 租户ID，如果系统支持多租户 (无外键)
 create_time         TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW() -- 记录创建时间 (日志写入时间，不含时区信息)
    -- update_time 字段在此表中不常用，因为日志通常是创建后不再修改
);

-- Comments
COMMENT ON TABLE t_user_password_log IS '用户密码修改记录表，记录用户每次修改密码的事件及相关的策略时间点';
COMMENT ON COLUMN t_user_password_log.id IS '主键ID';
COMMENT ON COLUMN t_user_password_log.user_id IS '关联到 t_user 表的 ID，该密码修改所属的用户';
COMMENT ON COLUMN t_user_password_log.change_time IS '密码修改操作实际发生的时间 (不含时区信息)';
COMMENT ON COLUMN t_user_password_log.changed_by_user_id IS '执行密码修改操作的用户ID。如果用户自己修改，该字段可为NULL；如果是管理员代为修改，则记录管理员的user_id';
COMMENT ON COLUMN t_user_password_log.next_reminder_time IS '基于当前密码策略计算的下次强制修改密码的提醒时间点 (不含时区信息)，如果无相关策略则为NULL';
COMMENT ON COLUMN t_user_password_log.policy_disable_time IS '基于当前密码策略计算的账户可能被禁用的时间点 (如密码过期未修改，不含时区信息)，如果无相关策略则为NULL';
COMMENT ON COLUMN t_user_password_log.tenant_id IS '租户ID，如果系统支持多租户，记录该用户所属的租户；否则为NULL';
COMMENT ON COLUMN t_user_password_log.create_time IS '记录创建时间，即该密码修改日志被写入数据库的时间 (不含时区信息)';


CREATE TABLE *********************
(id                  BIGSERIAL PRIMARY KEY,                                          -- 仍然建议使用BIGSERIAL作为逻辑主键，即使不声明PRIMARY KEY约束
 project_id          BIGINT                      NOT NULL,
 task_id             BIGINT                      NOT NULL,                           -- 允许NULL
 gitlab_project_id   BIGINT                      NOT NULL,
 gitlab_project_path VARCHAR(255),
 mapping_type        VARCHAR(50),
    -- 标准审计字段
 create_time         TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- now()是CURRENT_TIMESTAMP的别名
 update_time         TIMESTAMP WITHOUT TIME ZONE,                                    -- 应用层负责更新
 deleted             INTEGER                              DEFAULT 0,                 -- 0表示未删除，1表示已删除 (或使用BOOLEAN DEFAULT FALSE)
 create_user         BIGINT,
 update_user         BIGINT
);

COMMENT ON TABLE ********************* IS '平台项目/任务与GitLab仓库映射表 (无物理主外键, 无触发器)';
COMMENT ON COLUMN *********************.id IS '逻辑主键，自增序列';
COMMENT ON COLUMN *********************.project_id IS '平台项目ID';
COMMENT ON COLUMN *********************.task_id IS '平台任务ID (可为空)';
COMMENT ON COLUMN *********************.gitlab_project_id IS 'GitLab项目仓库的数字ID';
COMMENT ON COLUMN *********************.gitlab_project_path IS 'GitLab项目仓库的路径 (如 group/project)';
COMMENT ON COLUMN *********************.mapping_type IS '映射类型 (如 MAIN_REPO, TEST_REPO)';
COMMENT ON COLUMN *********************.create_time IS '记录创建时间';
COMMENT ON COLUMN *********************.update_time IS '记录最后更新时间 (应用层维护)';
COMMENT ON COLUMN *********************.deleted IS '逻辑删除标志 (0=未删除, 1=已删除)';
COMMENT ON COLUMN *********************.create_user IS '记录创建人用户ID';
COMMENT ON COLUMN *********************.update_user IS '记录最后修改人用户ID';

CREATE TABLE t_course_info (
    id BIGINT PRIMARY KEY NOT NULL,
    course_code VARCHAR(50) UNIQUE NOT NULL,
    course_name VARCHAR(255) NOT NULL,
    description TEXT,
    credits NUMERIC(3,1),
    hours INTEGER,
    course_type VARCHAR(50),
    department VARCHAR(255),
    instructor_user_id BIGINT, -- 逻辑外键，指向 t_user_info.id
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE', -- 例如: ACTIVE, INACTIVE, ARCHIVED
    start_date DATE,
    end_date DATE,
    prerequisites TEXT,
    syllabus_url VARCHAR(512),
    cover_image_url VARCHAR(512),
    create_time TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_user BIGINT,
    update_time TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_user BIGINT,
    tenant_id BIGINT,
    deleted SMALLINT NOT NULL DEFAULT 0
);

-- 为常用查询字段创建索引

-- 添加注释 (可选, 但推荐)
COMMENT ON TABLE t_course_info IS '课程信息表';
COMMENT ON COLUMN t_course_info.id IS '课程唯一标识 (雪花算法或序列生成)';
COMMENT ON COLUMN t_course_info.course_code IS '课程代码 (学校或机构内部的唯一代码)';
COMMENT ON COLUMN t_course_info.course_name IS '课程完整名称';
COMMENT ON COLUMN t_course_info.description IS '课程详细描述';
COMMENT ON COLUMN t_course_info.credits IS '课程学分';
COMMENT ON COLUMN t_course_info.hours IS '课程总学时';
COMMENT ON COLUMN t_course_info.course_type IS '课程类型 (如: 专业必修, 专业选修, 公共必修)';
COMMENT ON COLUMN t_course_info.department IS '开课院系/部门ID (逻辑关联到 t_department 表)';
COMMENT ON COLUMN t_course_info.instructor_user_id IS '主讲教师的用户ID (逻辑关联到 t_user_info 表)';
COMMENT ON COLUMN t_course_info.status IS '课程状态 (如: ACTIVE-开设中, INACTIVE-未开设, ARCHIVED-已归档)';
COMMENT ON COLUMN t_course_info.start_date IS '课程开始日期 (可选, 如适用学期制)';
COMMENT ON COLUMN t_course_info.end_date IS '课程结束日期 (可选)';
COMMENT ON COLUMN t_course_info.prerequisites IS '先修课程要求描述 (文本形式)';
COMMENT ON COLUMN t_course_info.syllabus_url IS '课程大纲文件URL (可选)';
COMMENT ON COLUMN t_course_info.cover_image_url IS '课程封面图片URL (可选)';
COMMENT ON COLUMN t_course_info.create_time IS '记录创建时间';
COMMENT ON COLUMN t_course_info.create_user IS '创建人用户ID (逻辑关联到 t_user_info 表)';
COMMENT ON COLUMN t_course_info.update_time IS '记录最后更新时间';
COMMENT ON COLUMN t_course_info.update_user IS '最后更新人用户ID (逻辑关联到 t_user_info 表)';
COMMENT ON COLUMN t_course_info.tenant_id IS '租户ID (如果系统支持多租户)';
COMMENT ON COLUMN t_course_info.deleted IS '逻辑删除标志 (0=未删除, 1=已删除)';

CREATE TABLE t_student_course_enrollment (
    "id" BIGINT PRIMARY KEY NOT NULL,
    "student_user_id" BIGINT NOT NULL,
    "course_id" BIGINT NOT NULL,
    "enrollment_date" DATE NOT NULL DEFAULT CURRENT_DATE,
    "enrollment_status" VARCHAR(30) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'ENROLLED'::character varying,
    "final_grade" NUMERIC(5,2),
    "completion_date" DATE,
    "semester" VARCHAR(50) COLLATE "pg_catalog"."default",
    "create_time" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "create_user" BIGINT,
    "update_time" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "update_user" BIGINT,
    "tenant_id" BIGINT,
    "deleted" SMALLINT NOT NULL DEFAULT 0
)
;
COMMENT ON COLUMN t_student_course_enrollment."id" IS '选课记录唯一标识 (雪花算法或序列)';
COMMENT ON COLUMN t_student_course_enrollment."student_user_id" IS '学生的用户ID (逻辑关联 t_user_info.id)';
COMMENT ON COLUMN t_student_course_enrollment."course_id" IS '所选课程的ID (逻辑关联 t_course.id)';
COMMENT ON COLUMN t_student_course_enrollment."enrollment_date" IS '学生选修该课程的日期';
COMMENT ON COLUMN t_student_course_enrollment."enrollment_status" IS '选课状态 (枚举 EnrollmentStatusEnum 的 code)';
COMMENT ON COLUMN t_student_course_enrollment."final_grade" IS '学生在该次选课获得的最终总成绩';
COMMENT ON COLUMN t_student_course_enrollment."completion_date" IS '学生完成该课程的日期 (可选)';
COMMENT ON COLUMN t_student_course_enrollment."semester" IS '学生选修该课程的学期 (例如 "2024-2025秋季学期")';
COMMENT ON COLUMN t_student_course_enrollment."create_time" IS '记录创建时间';
COMMENT ON COLUMN t_student_course_enrollment."create_user" IS '创建人用户ID';
COMMENT ON COLUMN t_student_course_enrollment."update_time" IS '记录最后更新时间';
COMMENT ON COLUMN t_student_course_enrollment."update_user" IS '最后更新人用户ID';
COMMENT ON COLUMN t_student_course_enrollment."tenant_id" IS '租户ID';
COMMENT ON COLUMN t_student_course_enrollment."deleted" IS '逻辑删除标志 (0=未删除, 1=已删除)';
COMMENT ON TABLE t_student_course_enrollment IS '学生课程选课记录表';


CREATE TABLE t_student_course_assessment (
    "id" BIGINT PRIMARY KEY NOT NULL,
    "enrollment_id" BIGINT NOT NULL,
    "assessment_name" VARCHAR(255) COLLATE "pg_catalog"."default" NOT NULL,
    "assessment_type" VARCHAR(30) COLLATE "pg_catalog"."default" NOT NULL,
    "assessment_date" DATE,
    "attended" BOOLEAN NOT NULL DEFAULT false,
    "score" NUMERIC(5,2),
    "weight" NUMERIC(4,3) CHECK (weight >= 0 AND weight <= 1), -- 权重，如 0.400 代表 40%
    "comments" TEXT COLLATE "pg_catalog"."default",
    "create_time" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "create_user" BIGINT,
    "update_time" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "update_user" BIGINT,
    "tenant_id" BIGINT,
    "deleted" SMALLINT NOT NULL DEFAULT 0
)
;

COMMENT ON COLUMN t_student_course_assessment."id" IS '考核记录唯一标识';
COMMENT ON COLUMN t_student_course_assessment."enrollment_id" IS '对应的学生选课记录ID (逻辑关联 t_student_course_enrollment.id)';
COMMENT ON COLUMN t_student_course_assessment."assessment_name" IS '考核名称 (如: "期末考试", "第一次期中测试", "大作业")';
COMMENT ON COLUMN t_student_course_assessment."assessment_type" IS '考核类型 (枚举 AssessmentTypeEnum 的 code)';
COMMENT ON COLUMN t_student_course_assessment."assessment_date" IS '考核日期';
COMMENT ON COLUMN t_student_course_assessment."attended" IS '学生是否参加了本次考核 (true=参加, false=未参加)';
COMMENT ON COLUMN t_student_course_assessment."score" IS '本次考核得分';
COMMENT ON COLUMN t_student_course_assessment."weight" IS '本次考核在总成绩中所占权重 (0.0 到 1.0 之间)';
COMMENT ON COLUMN t_student_course_assessment."comments" IS '教师对本次考核的评语 (可选)';
COMMENT ON COLUMN t_student_course_assessment.create_time IS '记录创建时间';
COMMENT ON COLUMN t_student_course_assessment.create_user IS '创建人用户ID (逻辑关联到 t_user_info 表)';
COMMENT ON COLUMN t_student_course_assessment.update_time IS '记录最后更新时间';
COMMENT ON COLUMN t_student_course_assessment.update_user IS '最后更新人用户ID (逻辑关联到 t_user_info 表)';
COMMENT ON COLUMN t_student_course_assessment.tenant_id IS '租户ID (如果系统支持多租户)';
COMMENT ON COLUMN t_student_course_assessment.deleted IS '逻辑删除标志 (0=未删除, 1=已删除)';
COMMENT ON TABLE t_student_course_assessment IS '学生课程考核记录表';

CREATE TABLE t_permission_info (
    id BIGSERIAL PRIMARY KEY NOT NULL, -- BIGSERIAL 会自动创建序列并设置为主键
    permission_code VARCHAR(100) NOT NULL UNIQUE, -- 权限编码，全局唯一
    permission_name VARCHAR(255) NOT NULL, -- 权限名称
    permission_group_name VARCHAR(100), -- 权限所属分组名称
    description VARCHAR(500), -- 权限详细描述
    parent_id BIGINT, -- 父权限ID (用于树形结构，可选)
    sort_order int4 DEFAULT 0, -- 排序值
    enabled int4 DEFAULT 0, -- 是否启用 (TRUE:启用, FALSE:禁用)
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- TIMESTAMPTZ 包含时区信息
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_user BIGINT,
    update_user BIGINT
);

COMMENT ON TABLE t_permission_info IS '权限信息表';
COMMENT ON COLUMN t_permission_info.id IS '权限ID，主键自增';
COMMENT ON COLUMN t_permission_info.permission_code IS '权限编码，全局唯一 (如 USER_VIEW, ARTICLE_EDIT)';
COMMENT ON COLUMN t_permission_info.permission_name IS '权限名称 (如 查看用户, 编辑文章)';
COMMENT ON COLUMN t_permission_info.permission_group_name IS '权限所属分组名称 (如 用户管理, 内容管理)';
COMMENT ON COLUMN t_permission_info.description IS '权限详细描述';
COMMENT ON COLUMN t_permission_info.parent_id IS '父权限ID (用于树形结构，可选)';
COMMENT ON COLUMN t_permission_info.sort_order IS '排序值，越小越靠前';
COMMENT ON COLUMN t_permission_info.enabled IS '是否启用 (0:启用, 1:禁用)';
COMMENT ON COLUMN t_permission_info.create_time IS '记录创建时间';
COMMENT ON COLUMN t_permission_info.update_time IS '记录最后更新时间';
COMMENT ON COLUMN t_permission_info.create_user IS '创建人ID';
COMMENT ON COLUMN t_permission_info.update_user IS '更新人ID';


-- ----------------------------
-- 表结构 t_role_permission_relation (角色权限关联表)
-- ----------------------------
-- DROP TABLE IF EXISTS t_role_permission_relation; -- 如果需要，先删除旧表
CREATE TABLE t_role_permission_relation (
    id BIGSERIAL PRIMARY KEY, -- 关联ID，主键自增
    role_id BIGINT NOT NULL, -- 角色ID
    permission_id BIGINT NOT NULL, -- 权限ID
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_user BIGINT,
    update_user BIGINT,
    CONSTRAINT uk_role_permission UNIQUE (role_id, permission_id) -- 确保同一角色对同一权限的关联唯一
);

COMMENT ON TABLE t_role_permission_relation IS '角色权限关联表';
COMMENT ON COLUMN t_role_permission_relation.id IS '关联ID，主键自增';
COMMENT ON COLUMN t_role_permission_relation.role_id IS '角色ID (逻辑关联 t_role.id)';
COMMENT ON COLUMN t_role_permission_relation.permission_id IS '权限ID (逻辑关联 t_permission_info.id)';
COMMENT ON COLUMN t_role_permission_relation.create_time IS '记录创建时间';
COMMENT ON COLUMN t_role_permission_relation.update_time IS '记录最后更新时间';
COMMENT ON COLUMN t_role_permission_relation.create_user IS '创建人ID';
COMMENT ON COLUMN t_role_permission_relation.update_user IS '更新人ID';

-- 清空现有权限表 (可选，如果需要全新插入)
-- DELETE FROM t_permission_info;
-- ALTER TABLE t_permission_info AUTO_INCREMENT = 1; -- 如果您的ID是自增的，重置自增起始值

-- 系统管理权限
INSERT INTO t_permission_info (permission_code, permission_name, permission_group_name, description, parent_id, sort_order, enabled, create_time, update_time, create_user, update_user) VALUES
                                                                                                                                                                                             -- 用户管理
                                                                                                                                                                                             ('USER_VIEW_LIST', '查看用户列表', '系统管理', '允许查看系统中的用户列表信息', NULL, 101, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('USER_CREATE', '创建用户', '系统管理', '允许创建新的用户账户', NULL, 102, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('USER_EDIT_INFO', '修改用户信息', '系统管理', '允许修改现有用户的基本信息、角色和状态', NULL, 103, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('USER_DELETE', '删除用户', '系统管理', '允许删除用户账户（通常是逻辑删除）', NULL, 104, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('USER_RESET_PASSWORD', '重置用户密码', '系统管理', '允许管理员重置用户的登录密码', NULL, 105, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('USER_BATCH_IMPORT', '批量导入用户', '系统管理', '允许通过文件批量导入用户数据', NULL, 106, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('USER_EXPORT_DATA', '导出用户信息', '系统管理', '允许导出用户信息列表', NULL, 107, 0, NOW(), NOW(), 1, 1),

                                                                                                                                                                                             -- 角色管理
                                                                                                                                                                                             ('ROLE_VIEW_LIST', '查看角色列表', '系统管理', '允许查看系统中的角色列表及其权限配置', NULL, 201, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('ROLE_CREATE', '创建角色', '系统管理', '允许创建新的角色并分配权限', NULL, 202, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('ROLE_EDIT_INFO', '修改角色信息', '系统管理', '允许修改现有角色的名称、描述和权限', NULL, 203, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('ROLE_DELETE', '删除角色', '系统管理', '允许删除自定义角色（未被使用的）', NULL, 204, 0, NOW(), NOW(), 1, 1),

                                                                                                                                                                                             -- 学校组织管理
                                                                                                                                                                                             ('ACADEMIC_ORG_VIEW', '查看学校组织', '系统管理', '允许查看学校、院系、班级等组织信息', NULL, 301, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('ACADEMIC_ORG_MANAGE', '管理学校组织', '系统管理', '允许创建、修改、删除学校、院系、班级等', NULL, 302, 0, NOW(), NOW(), 1, 1);

-- 项目与任务管理权限
INSERT INTO t_permission_info (permission_code, permission_name, permission_group_name, description, parent_id, sort_order, enabled, create_time, update_time, create_user, update_user) VALUES
                                                                                                                                                                                             -- 项目管理
                                                                                                                                                                                             ('PROJECT_VIEW_ALL', '查看所有项目', '项目与任务管理', '允许查看平台内所有项目列表和详情（管理员/特定角色）', NULL, 1001, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('PROJECT_VIEW_ASSIGNED', '查看我的项目', '项目与任务管理', '允许用户查看自己参与或负责的项目列表和详情', NULL, 1002, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('PROJECT_CREATE', '创建项目', '项目与任务管理', '允许创建新的教学项目', NULL, 1003, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('PROJECT_EDIT_OWN', '修改我创建的项目', '项目与任务管理', '允许用户修改自己创建的项目的基本信息、阶段和评审设置', NULL, 1004, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('PROJECT_EDIT_ANY', '修改任意项目', '项目与任务管理', '允许修改平台内任何项目的配置（管理员/特定角色）', NULL, 1005, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('PROJECT_DELETE_OWN', '删除我创建的项目', '项目与任务管理', '允许用户删除自己创建的（未开始或符合条件）项目', NULL, 1006, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('PROJECT_DELETE_ANY', '删除任意项目', '项目与任务管理', '允许删除平台内任何项目（管理员/特定角色）', NULL, 1007, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('PROJECT_MANAGE_MEMBERS', '管理项目成员', '项目与任务管理', '允许为项目添加、移除成员，或修改项目内角色', NULL, 1008, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('PROJECT_PUBLISH', '发布项目', '项目与任务管理', '允许将草稿状态的项目发布为正式项目', NULL, 1009, 0, NOW(), NOW(), 1, 1),

                                                                                                                                                                                             -- 任务管理 (作为项目管理的一部分，但权限独立)
                                                                                                                                                                                             ('TASK_VIEW_IN_PROJECT', '查看项目任务', '项目与任务管理', '允许查看所属项目中的任务列表和详情', NULL, 1101, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('TASK_CREATE_IN_PROJECT', '在项目中创建任务', '项目与任务管理', '允许在项目中创建新任务', NULL, 1102, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('TASK_EDIT_ASSIGNED', '修改我的任务', '项目与任务管理', '允许用户修改分配给自己的任务的某些属性（如状态、备注）', NULL, 1103, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('TASK_EDIT_IN_PROJECT', '修改项目内任务', '项目与任务管理', '允许项目负责人或管理员修改项目内的任何任务', NULL, 1104, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('TASK_DELETE_IN_PROJECT', '删除项目内任务', '项目与任务管理', '允许项目负责人或管理员删除项目内的任务', NULL, 1105, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('TASK_ASSIGN_MEMBER', '分配任务成员', '项目与任务管理', '允许将任务分配给项目成员', NULL, 1106, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('TASK_SUBMIT_DELIVERABLE', '提交任务成果', '项目与任务管理', '允许学生提交任务的阶段性成果或最终成果', NULL, 1107, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('TASK_REVIEW_SUBMISSION', '评审任务成果', '项目与任务管理', '允许教师或企业导师对提交的任务成果进行评审打分', NULL, 1108, 0, NOW(), NOW(), 1, 1);

-- 可以添加更多分组和权限，例如：
-- 模板管理权限 (permission_group_name: "模板管理")
INSERT INTO t_permission_info (permission_code, permission_name, permission_group_name, description, parent_id, sort_order, enabled, create_time, update_time, create_user, update_user) VALUES
                                                                                                                                                                                             ('TEMPLATE_TASK_VIEW', '查看任务模板', '模板管理', '允许查看任务模板列表', NULL, 2001, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('TEMPLATE_TASK_MANAGE', '管理任务模板', '模板管理', '允许创建、修改、删除任务模板', NULL, 2002, 0, NOW(), NOW(), 1, 1);

-- 评审管理权限 (permission_group_name: "评审管理")
INSERT INTO t_permission_info (permission_code, permission_name, permission_group_name, description, parent_id, sort_order, enabled, create_time, update_time, create_user, update_user) VALUES
                                                                                                                                                                                             ('REVIEW_CRITERIA_VIEW', '查看评审标准', '评审管理', '允许查看系统和自定义的评审标准', NULL, 3001, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('REVIEW_CRITERIA_MANAGE', '管理评审标准', '评审管理', '允许创建、修改、删除自定义评审标准', NULL, 3002, 0, NOW(), NOW(), 1, 1);



CREATE TABLE t_common_tag (
    id BIGSERIAL PRIMARY KEY,
    tag_name VARCHAR(50) NOT NULL,
    tag_category VARCHAR(30) NOT NULL DEFAULT '通用',
    tag_color VARCHAR(20) DEFAULT '#1890ff',
    tag_description VARCHAR(200),
    applicable_entity_type VARCHAR(50),
    usage_count INTEGER DEFAULT 0,
    tag_weight INTEGER DEFAULT 1,
    is_system_tag BOOLEAN DEFAULT false,
    tag_status VARCHAR(20) DEFAULT 'ACTIVE',
    create_user BIGINT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_user BIGINT,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ext_field1 VARCHAR(100),
    ext_field2 VARCHAR(100)
);

-- 添加表注释
COMMENT ON TABLE t_common_tag IS '通用标签表';
COMMENT ON COLUMN t_common_tag.id IS '主键ID';
COMMENT ON COLUMN t_common_tag.tag_name IS '标签名称';
COMMENT ON COLUMN t_common_tag.tag_category IS '标签分类';
COMMENT ON COLUMN t_common_tag.tag_color IS '标签颜色';
COMMENT ON COLUMN t_common_tag.tag_description IS '标签描述';
COMMENT ON COLUMN t_common_tag.applicable_entity_type IS '适用实体类型';
COMMENT ON COLUMN t_common_tag.usage_count IS '使用次数';
COMMENT ON COLUMN t_common_tag.tag_weight IS '标签权重';
COMMENT ON COLUMN t_common_tag.is_system_tag IS '是否系统标签';
COMMENT ON COLUMN t_common_tag.tag_status IS '标签状态';
COMMENT ON COLUMN t_common_tag.create_user IS '创建人';
COMMENT ON COLUMN t_common_tag.create_time IS '创建时间';
COMMENT ON COLUMN t_common_tag.update_user IS '更新人';
COMMENT ON COLUMN t_common_tag.update_time IS '更新时间';
COMMENT ON COLUMN t_common_tag.ext_field1 IS '扩展字段1';
COMMENT ON COLUMN t_common_tag.ext_field2 IS '扩展字段2';

-- 4. 重建关联关系表
CREATE TABLE t_entity_tag_relation (
    id BIGSERIAL PRIMARY KEY,
    entity_id BIGINT NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    tag_id BIGINT NOT NULL,
    relation_status VARCHAR(20) DEFAULT 'ACTIVE',
    create_user BIGINT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_user BIGINT,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT fk_tag_relation_tag FOREIGN KEY (tag_id) REFERENCES t_common_tag(id) ON DELETE CASCADE
);

-- 添加关联表注释
COMMENT ON TABLE t_entity_tag_relation IS '实体标签关联关系表';
COMMENT ON COLUMN t_entity_tag_relation.id IS '主键ID';
COMMENT ON COLUMN t_entity_tag_relation.entity_id IS '实体ID';
COMMENT ON COLUMN t_entity_tag_relation.entity_type IS '实体类型';
COMMENT ON COLUMN t_entity_tag_relation.tag_id IS '标签ID';
COMMENT ON COLUMN t_entity_tag_relation.relation_status IS '关联状态';
COMMENT ON COLUMN t_entity_tag_relation.create_user IS '创建人';
COMMENT ON COLUMN t_entity_tag_relation.create_time IS '创建时间';
COMMENT ON COLUMN t_entity_tag_relation.update_user IS '更新人';
COMMENT ON COLUMN t_entity_tag_relation.update_time IS '更新时间';

-- 5. 创建索引
SELECT '=== 创建索引 ===' as info;

-- 标签表索引
CREATE UNIQUE INDEX idx_tag_name_category ON t_common_tag(tag_name, tag_category);
CREATE INDEX idx_tag_category ON t_common_tag(tag_category);
CREATE INDEX idx_tag_usage_count ON t_common_tag(usage_count DESC);
CREATE INDEX idx_tag_status ON t_common_tag(tag_status);
CREATE INDEX idx_tag_entity_type ON t_common_tag(applicable_entity_type);

-- 关联关系表索引
CREATE UNIQUE INDEX idx_entity_tag_unique ON t_entity_tag_relation(entity_id, entity_type, tag_id);
CREATE INDEX idx_entity_tag_entity ON t_entity_tag_relation(entity_id, entity_type);
CREATE INDEX idx_entity_tag_tag ON t_entity_tag_relation(tag_id);
CREATE INDEX idx_entity_tag_status ON t_entity_tag_relation(relation_status);

-- 1. 技术栈标签
INSERT INTO nybc_edu.t_common_tag (
    tag_name, tag_category, tag_color, tag_description, applicable_entity_type,
    usage_count, tag_weight, is_system_tag, tag_status,
    create_user, create_time, update_user, update_time, ext_field1, ext_field2
) VALUES
      ('Java', 'tech_stack', '#f56a00', 'Java编程语言', 'PROJECT', 0, 90, true, 'ACTIVE', 1, now(), 1, now(), 'programming_language', 'backend'),
      ('JavaScript', 'tech_stack', '#faad14', 'JavaScript编程语言', 'PROJECT', 0, 85, true, 'ACTIVE', 1, now(), 1, now(), 'programming_language', 'frontend'),
      ('Python', 'tech_stack', '#52c41a', 'Python编程语言', 'PROJECT', 0, 88, true, 'ACTIVE', 1, now(), 1, now(), 'programming_language', 'backend'),
      ('Vue.js', 'tech_stack', '#13c2c2', 'Vue.js前端框架', 'PROJECT', 0, 85, true, 'ACTIVE', 1, now(), 1, now(), 'frontend_framework', 'spa'),
      ('React', 'tech_stack', '#1890ff', 'React前端框架', 'PROJECT', 0, 82, true, 'ACTIVE', 1, now(), 1, now(), 'frontend_framework', 'spa'),
      ('Spring Boot', 'tech_stack', '#722ed1', 'Spring Boot后端框架', 'PROJECT', 0, 92, true, 'ACTIVE', 1, now(), 1, now(), 'backend_framework', 'java'),
      ('Node.js', 'tech_stack', '#eb2f96', 'Node.js运行环境', 'PROJECT', 0, 78, true, 'ACTIVE', 1, now(), 1, now(), 'runtime', 'javascript'),
      ('MySQL', 'tech_stack', '#fa8c16', 'MySQL数据库', 'PROJECT', 0, 75, true, 'ACTIVE', 1, now(), 1, now(), 'database', 'relational'),
      ('PostgreSQL', 'tech_stack', '#2f54eb', 'PostgreSQL数据库', 'PROJECT', 0, 80, true, 'ACTIVE', 1, now(), 1, now(), 'database', 'relational'),
      ('Redis', 'tech_stack', '#f5222d', 'Redis缓存数据库', 'PROJECT', 0, 70, true, 'ACTIVE', 1, now(), 1, now(), 'database', 'cache');

-- 2. 项目类型标签（包含智能体）
INSERT INTO nybc_edu.t_common_tag (
    tag_name, tag_category, tag_color, tag_description, applicable_entity_type,
    usage_count, tag_weight, is_system_tag, tag_status,
    create_user, create_time, update_user, update_time, ext_field1, ext_field2
) VALUES
      ('Web应用', 'project_type', '#1890ff', 'Web应用项目', 'PROJECT', 0, 95, true, 'ACTIVE', 1, now(), 1, now(), 'application_type', 'web'),
      ('移动应用', 'project_type', '#52c41a', '移动应用项目', 'PROJECT', 0, 88, true, 'ACTIVE', 1, now(), 1, now(), 'application_type', 'mobile'),
      ('桌面应用', 'project_type', '#722ed1', '桌面应用项目', 'PROJECT', 0, 75, true, 'ACTIVE', 1, now(), 1, now(), 'application_type', 'desktop'),
      ('数据分析', 'project_type', '#fa8c16', '数据分析项目', 'PROJECT', 0, 82, true, 'ACTIVE', 1, now(), 1, now(), 'project_domain', 'data_science'),
      ('机器学习', 'project_type', '#eb2f96', '机器学习项目', 'PROJECT', 0, 85, true, 'ACTIVE', 1, now(), 1, now(), 'project_domain', 'ai_ml'),
      ('小程序', 'project_type', '#13c2c2', '小程序项目', 'PROJECT', 0, 78, true, 'ACTIVE', 1, now(), 1, now(), 'application_type', 'miniprogram'),
      ('API服务', 'project_type', '#faad14', 'API服务项目', 'PROJECT', 0, 80, true, 'ACTIVE', 1, now(), 1, now(), 'service_type', 'api'),
      ('智能体', 'project_type', '#9254de', '智能体项目', 'PROJECT', 0, 90, true, 'ACTIVE', 1, now(), 1, now(), 'ai_type', 'agent');

-- 3. 项目模板标签（与项目类型保持一致）
INSERT INTO nybc_edu.t_common_tag (
    tag_name, tag_category, tag_color, tag_description, applicable_entity_type,
    usage_count, tag_weight, is_system_tag, tag_status,
    create_user, create_time, update_user, update_time, ext_field1, ext_field2
) VALUES
      ('Web应用', 'project_template', '#1890ff', 'Web应用项目模板', 'PROJECT_TEMPLATE', 0, 95, true, 'ACTIVE', 1, now(), 1, now(), 'application_type', 'web'),
      ('移动应用', 'project_template', '#52c41a', '移动应用项目模板', 'PROJECT_TEMPLATE', 0, 88, true, 'ACTIVE', 1, now(), 1, now(), 'application_type', 'mobile'),
      ('桌面应用', 'project_template', '#722ed1', '桌面应用项目模板', 'PROJECT_TEMPLATE', 0, 75, true, 'ACTIVE', 1, now(), 1, now(), 'application_type', 'desktop'),
      ('数据分析', 'project_template', '#fa8c16', '数据分析项目模板', 'PROJECT_TEMPLATE', 0, 82, true, 'ACTIVE', 1, now(), 1, now(), 'project_domain', 'data_science'),
      ('机器学习', 'project_template', '#eb2f96', '机器学习项目模板', 'PROJECT_TEMPLATE', 0, 85, true, 'ACTIVE', 1, now(), 1, now(), 'project_domain', 'ai_ml'),
      ('小程序', 'project_template', '#13c2c2', '小程序项目模板', 'PROJECT_TEMPLATE', 0, 78, true, 'ACTIVE', 1, now(), 1, now(), 'application_type', 'miniprogram'),
      ('API服务', 'project_template', '#faad14', 'API服务项目模板', 'PROJECT_TEMPLATE', 0, 80, true, 'ACTIVE', 1, now(), 1, now(), 'service_type', 'api'),
      ('智能体', 'project_template', '#9254de', '智能体项目模板', 'PROJECT_TEMPLATE', 0, 90, true, 'ACTIVE', 1, now(), 1, now(), 'ai_type', 'agent');

-- 4. 文档类型标签
INSERT INTO nybc_edu.t_common_tag (
    tag_name, tag_category, tag_color, tag_description, applicable_entity_type,
    usage_count, tag_weight, is_system_tag, tag_status,
    create_user, create_time, update_user, update_time, ext_field1, ext_field2
) VALUES
      ('需求文档', 'document_type', '#1890ff', '项目需求说明文档', 'FILE', 0, 90, true, 'ACTIVE', 1, now(), 1, now(), 'document_type', 'requirement'),
      ('设计文档', 'document_type', '#722ed1', '系统设计文档', 'FILE', 0, 85, true, 'ACTIVE', 1, now(), 1, now(), 'document_type', 'design'),
      ('用户手册', 'document_type', '#52c41a', '用户使用手册', 'FILE', 0, 75, true, 'ACTIVE', 1, now(), 1, now(), 'document_type', 'manual'),
      ('API文档', 'document_type', '#faad14', 'API接口文档', 'FILE', 0, 80, true, 'ACTIVE', 1, now(), 1, now(), 'document_type', 'api_doc'),
      ('技术文档', 'document_type', '#13c2c2', '技术实现文档', 'FILE', 0, 82, true, 'ACTIVE', 1, now(), 1, now(), 'document_type', 'technical');

-- 5. 课程相关标签
INSERT INTO nybc_edu.t_common_tag (
    tag_name, tag_category, tag_color, tag_description, applicable_entity_type,
    usage_count, tag_weight, is_system_tag, tag_status,
    create_user, create_time, update_user, update_time, ext_field1, ext_field2
) VALUES
      ('基础课程', 'course_type', '#1890ff', '基础理论课程', 'COURSE', 0, 95, true, 'ACTIVE', 1, now(), 1, now(), 'course_level', 'foundation'),
      ('实践课程', 'course_type', '#52c41a', '实践操作课程', 'COURSE', 0, 90, true, 'ACTIVE', 1, now(), 1, now(), 'course_level', 'practice'),
      ('高级课程', 'course_type', '#722ed1', '高级进阶课程', 'COURSE', 0, 85, true, 'ACTIVE', 1, now(), 1, now(), 'course_level', 'advanced'),
      ('选修课程', 'course_type', '#faad14', '选修课程', 'COURSE', 0, 75, true, 'ACTIVE', 1, now(), 1, now(), 'course_requirement', 'elective'),
      ('必修课程', 'course_type', '#f56a00', '必修课程', 'COURSE', 0, 88, true, 'ACTIVE', 1, now(), 1, now(), 'course_requirement', 'required');

-- 6. 难度等级标签
INSERT INTO nybc_edu.t_common_tag (
    tag_name, tag_category, tag_color, tag_description, applicable_entity_type,
    usage_count, tag_weight, is_system_tag, tag_status,
    create_user, create_time, update_user, update_time, ext_field1, ext_field2
) VALUES
      ('入门', 'difficulty_level', '#52c41a', '入门级别', 'OTHER', 0, 100, true, 'ACTIVE', 1, now(), 1, now(), 'difficulty', 'beginner'),
      ('初级', 'difficulty_level', '#1890ff', '初级水平', 'OTHER', 0, 95, true, 'ACTIVE', 1, now(), 1, now(), 'difficulty', 'elementary'),
      ('中级', 'difficulty_level', '#faad14', '中级水平', 'OTHER', 0, 90, true, 'ACTIVE', 1, now(), 1, now(), 'difficulty', 'intermediate'),
      ('高级', 'difficulty_level', '#fa8c16', '高级水平', 'OTHER', 0, 85, true, 'ACTIVE', 1, now(), 1, now(), 'difficulty', 'advanced'),
      ('专家', 'difficulty_level', '#f5222d', '专家级别', 'OTHER', 0, 80, true, 'ACTIVE', 1, now(), 1, now(), 'difficulty', 'expert');

-- 7. 状态标签
INSERT INTO nybc_edu.t_common_tag (
    tag_name, tag_category, tag_color, tag_description, applicable_entity_type,
    usage_count, tag_weight, is_system_tag, tag_status,
    create_user, create_time, update_user, update_time, ext_field1, ext_field2
) VALUES
      ('进行中', 'status', '#1890ff', '正在进行中', 'OTHER', 0, 100, true, 'ACTIVE', 1, now(), 1, now(), 'status', 'in_progress'),
      ('已完成', 'status', '#52c41a', '已经完成', 'OTHER', 0, 95, true, 'ACTIVE', 1, now(), 1, now(), 'status', 'completed'),
      ('待开始', 'status', '#faad14', '等待开始', 'OTHER', 0, 90, true, 'ACTIVE', 1, now(), 1, now(), 'status', 'pending'),
CREATE EXTENSION pg_trgm;
--启用zhparser插件
CREATE EXTENSION zhparser;
--创建一个名为testzhcfg的文本搜索配置，该配置使用zhparser解析器。
CREATE TEXT SEARCH CONFIGURATION zh (PARSER = zhparser);
--添加令牌映射，在testzhcfg配置中为小写字母a-z添加映射到simple词典。
ALTER TEXT SEARCH CONFIGURATION zh ADD MAPPING FOR n,v,a,i,e,l WITH simple;

-- 任务评论表
-- 存储用户在任务下进行的讨论和评论内容。

CREATE TABLE t_task_comment (
    id BIGSERIAL PRIMARY KEY, -- 评论唯一标识ID，使用BIGSERIAL自动递增

    task_id BIGINT NOT NULL, -- 关联的任务ID
    project_id BIGINT NOT NULL, -- 关联的项目ID（冗余字段，方便查询和权限校验）
    comment_content TEXT NOT NULL, -- 评论内容，支持HTML格式
    parent_comment_id BIGINT, -- 父评论ID，用于支持评论回复（自关联），如果为顶层评论则为NULL

    -- 全文搜索向量字段，由触发器自动维护。初始化时允许为NULL
    -- 'zh' 是您配置的中文文本搜索配置名称
    tsv_comment_content tsvector,

    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 评论创建时间
    create_user BIGINT NOT NULL, -- 评论创建人ID
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 评论最后更新时间
    update_user BIGINT, -- 评论最后修改人ID

    -- 逻辑删除标记 (0:未删除, 1:已删除)。默认0表示未删除
    deleted INTEGER NOT NULL DEFAULT 0
);

-- 为表和列添加注释 (推荐的良好实践)
COMMENT ON TABLE t_task_comment IS '任务评论表，存储用户在特定任务下的讨论和评论内容。';
COMMENT ON COLUMN t_task_comment.id IS '评论唯一标识ID';
COMMENT ON COLUMN t_task_comment.task_id IS '关联的任务ID，外键指向t_project_task表';
COMMENT ON COLUMN t_task_comment.project_id IS '关联的项目ID，冗余字段，外键指向t_project表，方便查询和权限校验';
COMMENT ON COLUMN t_task_comment.comment_content IS '评论内容，支持HTML格式和富文本内容';
COMMENT ON COLUMN t_task_comment.parent_comment_id IS '父评论ID，用于支持评论回复（自关联），如果为顶层评论则为NULL';
COMMENT ON COLUMN t_task_comment.tsv_comment_content IS '评论内容经过全文搜索配置向量化后的表示，由触发器自动维护，用于全文搜索';
COMMENT ON COLUMN t_task_comment.create_time IS '评论创建时间';
COMMENT ON COLUMN t_task_comment.create_user IS '评论创建人ID，外键指向t_user表';
COMMENT ON COLUMN t_task_comment.update_time IS '评论最后更新时间';
COMMENT ON COLUMN t_task_comment.update_user IS '评论最后修改人ID，外键指向t_user表';
COMMENT ON COLUMN t_task_comment.deleted IS '逻辑删除标记 (0:未删除, 1:已删除)';


-- 创建索引 (在 CREATE TABLE 之后单独执行)

-- 全文搜索索引 (使用 GIN 索引加速 tsvector 字段的查询)
-- 索引名称通常以 idx_表名_字段名 命名
CREATE INDEX idx_t_task_comment_tsv ON t_task_comment USING GIN (tsv_comment_content);

-- 普通索引 (加速常见查询和外键关联)
CREATE INDEX idx_t_task_comment_task_id ON t_task_comment (task_id);
CREATE INDEX idx_t_task_comment_project_id ON t_task_comment (project_id);
CREATE INDEX idx_t_task_comment_create_time ON t_task_comment (create_time DESC); -- 按时间倒序查询常用
CREATE INDEX idx_t_task_comment_parent_id ON t_task_comment (parent_comment_id);
-- 组合索引（如果按任务ID和创建时间查询评论树很频繁）
CREATE INDEX idx_t_task_comment_task_create_parent ON t_task_comment (task_id, create_time DESC, parent_comment_id);


-- 定义全文搜索触发器函数 (在创建表和索引之后单独执行)
-- 'zh' 是您配置的中文文本搜索配置名称 (已假设您已执行：CREATE EXTENSION zhparser; CREATE TEXT SEARCH CONFIGURATION zh ...;)
CREATE OR REPLACE FUNCTION update_task_comment_tsv_trigger_func() RETURNS trigger AS $$
BEGIN
    -- 使用 'zh' 配置将评论内容转换为tsvector
    NEW.tsv_comment_content := to_tsvector('zh', NEW.comment_content);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器 (在 CREATE TRIGGER FUNCTION 之后单独执行)
-- 在 t_task_comment 表的 BEFORE INSERT OR UPDATE 事件上触发，确保在数据写入前更新 tsvector 字段
CREATE TRIGGER t_task_comment_tsv_update_trigger
    BEFORE INSERT OR UPDATE ON t_task_comment
    FOR EACH ROW EXECUTE PROCEDURE update_task_comment_tsv_trigger_func();




create table nybc_edu.t_user
(id              bigint primary key          not null default nextval('t_user_id_seq'::regclass), -- 主键ID，平台所有用户的唯一标识
 user_name       character varying(100)      not null,                                            -- 登录账号，全局唯一，可以是学号、工号、邮箱、手机号或自定义用户名
 password_hash   character varying(255)      not null,                                            -- 密码的哈希值，严禁存储明文
 nick_name       character varying(255),                                                          -- 用户昵称或显示名
 full_name       character varying(100)      not null,                                            -- 用户真实姓名
 mobile          character varying(20),                                                           -- 手机号码
 email           character varying(100),                                                          -- 邮箱
 avatar_url      character varying(255),                                                          -- 头像地址
 status          user_status_enum            not null default 'active'::user_status_enum,         -- 账户状态：active-活跃, inactive-非活跃, suspended-暂停, pending_approval-待审批
 source_from     character varying(255),                                                          -- 用户来源，如 school_import, enterprise_register, admin_create
 last_login_time timestamp without time zone,                                                     -- 最后登录时间 (不含时区信息)
 create_time     timestamp without time zone not null default now(),                              -- 记录创建时间 (不含时区信息)
 update_time     timestamp without time zone,                                                     -- 记录最后更新时间 (不含时区信息)
 deleted         integer,
 create_user     bigint,
 update_user     bigint
);
create unique index t_user_email_key on t_user using btree (email);
create unique index t_user_mobile_key on t_user using btree (mobile);
comment on table nybc_edu.t_user is '基础用户信息表，存储平台所有用户的核心身份与认证信息';
comment on column nybc_edu.t_user.id is '主键ID，平台所有用户的唯一标识';
comment on column nybc_edu.t_user.user_name is '登录账号，全局唯一，可以是学号、工号、邮箱、手机号或自定义用户名';
comment on column nybc_edu.t_user.password_hash is '密码的哈希值，严禁存储明文';
comment on column nybc_edu.t_user.nick_name is '用户昵称或显示名';
comment on column nybc_edu.t_user.full_name is '用户真实姓名';
comment on column nybc_edu.t_user.mobile is '手机号码';
comment on column nybc_edu.t_user.email is '邮箱';
comment on column nybc_edu.t_user.avatar_url is '头像地址';
comment on column nybc_edu.t_user.status is '账户状态：active-活跃, inactive-非活跃, suspended-暂停, pending_approval-待审批';
comment on column nybc_edu.t_user.source_from is '用户来源，如 school_import, enterprise_register, admin_create';
comment on column nybc_edu.t_user.last_login_time is '最后登录时间 (不含时区信息)';
comment on column nybc_edu.t_user.create_time is '记录创建时间 (不含时区信息)';
comment on column nybc_edu.t_user.update_time is '记录最后更新时间 (不含时区信息)';


-- 2. t_role (平台角色表)
CREATE TABLE t_role
(id            SERIAL PRIMARY KEY,                        -- 角色ID
 role_key      VARCHAR(50) UNIQUE NOT NULL,               -- 角色键，系统内部标识，对应 RoleEnum.key
 role_name     VARCHAR(50)        NOT NULL,               -- 角色名称，用于界面显示，对应 RoleEnum.name
 role_type_key VARCHAR(50)        NOT NULL,               -- 角色类型键，对应 RoleTypeEnum.value
 description   TEXT               NULL,                   -- 角色描述
 create_time   TIMESTAMP          NOT NULL DEFAULT NOW(), -- 创建时间
 update_time   TIMESTAMP          NULL                    -- 修改时间，由应用或触发器更新
);
COMMENT ON TABLE t_role IS '平台角色表，存储平台中所有角色的定义信息';
COMMENT ON COLUMN t_role.id IS '角色ID';
COMMENT ON COLUMN t_role.role_key IS '角色键，系统内部标识，对应 RoleEnum.key，全局唯一';
COMMENT ON COLUMN t_role.role_name IS '角色名称，用于界面显示，对应 RoleEnum.name';
COMMENT ON COLUMN t_role.role_type_key IS '角色类型键，对应 RoleTypeEnum.value，如 TENANT, CROP, SCH, PROJECT';
COMMENT ON COLUMN t_role.description IS '角色描述';
COMMENT ON COLUMN t_role.create_time IS '记录创建时间 (不含时区信息)';
COMMENT ON COLUMN t_role.update_time IS '记录最后更新时间 (不含时区信息)';


-- 3. t_user_role (用户角色关联表)
CREATE TABLE t_user_role
(id          BIGSERIAL PRIMARY KEY,            -- 主键ID
 user_id     BIGINT    NOT NULL,               -- 关联到 t_user 表的 ID (无外键)
 role_id     INTEGER   NOT NULL,               -- 关联到 t_role 表的 ID (无外键)
 create_time TIMESTAMP NOT NULL DEFAULT NOW(), -- 关联创建时间

    -- Constraints
 UNIQUE (user_id, role_id)                     -- 用户和角色的联合唯一，确保一个用户不会被赋予同一个角色两次
);
COMMENT ON TABLE t_user_role IS '用户角色关联表，记录用户与角色之间的多对多关系';
COMMENT ON COLUMN t_user_role.id IS '主键ID';
COMMENT ON COLUMN t_user_role.user_id IS '关联到 t_user 表的 ID';
COMMENT ON COLUMN t_user_role.role_id IS '关联到 t_role 表的 ID';
COMMENT ON COLUMN t_user_role.create_time IS '关联创建时间 (不含时区信息)';


-- 4. 组织结构表 (t_school, t_company)
CREATE TABLE t_school
(id          BIGSERIAL PRIMARY KEY,                      -- 学校ID
 school_name VARCHAR(100) UNIQUE NOT NULL,               -- 学校名称
 school_code VARCHAR(50) UNIQUE  NULL,                   -- 学校代码
 deleted     BOOLEAN             NOT NULL DEFAULT FALSE, -- 是否已逻辑删除
 create_time TIMESTAMP           NOT NULL DEFAULT NOW(), -- 创建时间
 update_time TIMESTAMP           NULL                    -- 修改时间，由应用或触发器更新
);
COMMENT ON TABLE t_tenant_school IS '学校信息表';
COMMENT ON COLUMN t_tenant_school.id IS '学校ID';
COMMENT ON COLUMN t_tenant_school.school_name IS '学校名称，全局唯一';
COMMENT ON COLUMN t_tenant_school.school_code IS '学校代码，全局唯一，可选';
COMMENT ON COLUMN t_tenant_school.deleted IS '是否已逻辑删除';
COMMENT ON COLUMN t_tenant_school.create_time IS '记录创建时间 (不含时区信息)';
COMMENT ON COLUMN t_tenant_school.update_time IS '记录最后更新时间 (不含时区信息)';


CREATE TABLE t_company
(id                         BIGSERIAL PRIMARY KEY,                      -- 企业ID
 company_name               VARCHAR(100) UNIQUE NOT NULL,               -- 企业名称
 unified_social_credit_code VARCHAR(50) UNIQUE  NULL,                   -- 统一社会信用代码
 deleted                    BOOLEAN             NOT NULL DEFAULT FALSE, -- 是否已逻辑删除
 create_time                TIMESTAMP           NOT NULL DEFAULT NOW(), -- 创建时间
 update_time                TIMESTAMP           NULL                    -- 修改时间，由应用或触发器更新
);
COMMENT ON TABLE t_company IS '企业信息表';
COMMENT ON COLUMN t_company.id IS '企业ID';
COMMENT ON COLUMN t_company.company_name IS '企业名称，全局唯一';
COMMENT ON COLUMN t_company.unified_social_credit_code IS '统一社会信用代码，可选，全局唯一';
COMMENT ON COLUMN t_company.deleted IS '是否已逻辑删除';
COMMENT ON COLUMN t_company.create_time IS '记录创建时间 (不含时区信息)';
COMMENT ON COLUMN t_company.update_time IS '记录最后更新时间 (不含时区信息)';


-- 5. 角色特定的详细信息表

-- t_student_detail (学生详细信息表)
CREATE TABLE t_student_detail
(id              BIGSERIAL PRIMARY KEY,                     -- 主键ID
 user_id         BIGINT UNIQUE      NOT NULL,               -- 关联到 t_user 表的 ID，学生的唯一用户关联 (无外键)
 school_id       BIGINT             NOT NULL,               -- 关联到 t_school 表的 ID，学生所属学校 (无外键)
 student_number  VARCHAR(50) UNIQUE NOT NULL,               -- 学号，学生在学校的唯一标识，全局唯一
 specialty           VARCHAR(100)       NULL,                   -- 专业名称
 grade           VARCHAR(50)        NULL,                   -- 年级，如 2021级
 class_name      VARCHAR(100)       NULL,                   -- 班级名称
 enrollment_year SMALLINT           NULL,                   -- 入学年份
 graduation_year SMALLINT           NULL,                   -- 预计毕业年份
    -- 学生其他详细信息：如 政治面貌、籍贯、教育背景（非本校）、荣誉奖项、技能标签等
 create_time     TIMESTAMP          NOT NULL DEFAULT NOW(), -- 创建时间
 update_time     TIMESTAMP          NULL                    -- 修改时间，由应用或触发器更新
);
COMMENT ON TABLE t_student_detail IS '学生详细信息表，存储学生角色的特有属性';
COMMENT ON COLUMN t_student_detail.id IS '主键ID';
COMMENT ON COLUMN t_student_detail.user_id IS '关联到 t_user 表的 ID，学生的唯一用户关联';
COMMENT ON COLUMN t_student_detail.school_id IS '关联到 t_school 表的 ID，学生所属学校';
COMMENT ON COLUMN t_student_detail.student_number IS '学号，学生在学校的唯一标识，全局唯一';
COMMENT ON COLUMN t_student_detail.specialty IS '专业名称';
COMMENT ON COLUMN t_student_detail.grade IS '年级，如 2021级';
COMMENT ON COLUMN t_student_detail.class_name IS '班级名称';
COMMENT ON COLUMN t_student_detail.enrollment_year IS '入学年份';
COMMENT ON COLUMN t_student_detail.graduation_year IS '预计毕业年份';
COMMENT ON COLUMN t_student_detail.create_time IS '记录创建时间 (不含时区信息)';
COMMENT ON COLUMN t_student_detail.update_time IS '记录最后更新时间 (不含时区信息)';


-- t_teacher_detail (教师详细信息表)
CREATE TABLE t_teacher_detail
(id             BIGSERIAL PRIMARY KEY,                     -- 主键ID
 user_id        BIGINT UNIQUE      NOT NULL,               -- 关联到 t_user 表的 ID，教师的唯一用户关联 (无外键)
 school_id      BIGINT             NOT NULL,               -- 关联到 t_school 表的 ID，教师所属学校 (无外键)
 teacher_number VARCHAR(50) UNIQUE NULL,                   -- 工号，可能非强制唯一
 department     VARCHAR(100)       NULL,                   -- 所属院系/部门
 title          VARCHAR(100)       NULL,                   -- 职称/职务，如 教授, 讲师, 辅导员
    -- 教师其他详细信息
 create_time    TIMESTAMP          NOT NULL DEFAULT NOW(), -- 创建时间
 update_time    TIMESTAMP          NULL                    -- 修改时间，由应用或触发器更新
);
COMMENT ON TABLE t_teacher_detail IS '教师详细信息表，存储教师角色的特有属性';
COMMENT ON COLUMN t_teacher_detail.id IS '主键ID';
COMMENT ON COLUMN t_teacher_detail.user_id IS '关联到 t_user 表的 ID，教师的唯一用户关联';
COMMENT ON COLUMN t_teacher_detail.school_id IS '关联到 t_school 表的 ID，教师所属学校';
COMMENT ON COLUMN t_teacher_detail.teacher_number IS '工号，教师在学校的标识，可能非强制唯一';
COMMENT ON COLUMN t_teacher_detail.department IS '所属院系或部门';
COMMENT ON COLUMN t_teacher_detail.title IS '职称或职务，如 教授, 副教授, 讲师, 辅导员等';
COMMENT ON COLUMN t_teacher_detail.create_time IS '记录创建时间 (不含时区信息)';
COMMENT ON COLUMN t_teacher_detail.update_time IS '记录最后更新时间 (不含时区信息)';


-- t_corporate_user_detail (企业成员详细信息表)
CREATE TABLE t_corporate_user_detail
(id          BIGSERIAL PRIMARY KEY,                      -- 主键ID
 user_id     BIGINT UNIQUE       NOT NULL,               -- 关联到 t_user 表的 ID，企业成员的唯一用户关联 (无外键)
 company_id  BIGINT              NOT NULL,               -- 关联到 t_company 表的 ID，企业成员所属企业 (无外键)
 job_number  VARCHAR(50) UNIQUE  NULL,                   -- 工号 (企业内部)
 department  VARCHAR(100)        NULL,                   -- 所属部门
 position    VARCHAR(100)        NULL,                   -- 职位
 work_email  VARCHAR(100) UNIQUE NULL,                   -- 工作邮箱
 work_phone  VARCHAR(20) UNIQUE  NULL,                   -- 工作电话
    -- 企业成员其他详细信息
 create_time TIMESTAMP           NOT NULL DEFAULT NOW(), -- 创建时间
 update_time TIMESTAMP           NULL                    -- 修改时间，由应用或触发器更新
);
COMMENT ON TABLE t_corporate_user_detail IS '企业成员详细信息表，存储企业成员角色的特有属性';
COMMENT ON COLUMN t_corporate_user_detail.id IS '主键ID';
COMMENT ON COLUMN t_corporate_user_detail.user_id IS '关联到 t_user 表的 ID，企业成员的唯一用户关联';
COMMENT ON COLUMN t_corporate_user_detail.company_id IS '关联到 t_company 表的 ID，企业成员所属企业';
COMMENT ON COLUMN t_corporate_user_detail.job_number IS '企业内部工号，可选，可能唯一';
COMMENT ON COLUMN t_corporate_user_detail.department IS '所属部门';
COMMENT ON COLUMN t_corporate_user_detail.position IS '职位';
COMMENT ON COLUMN t_corporate_user_detail.work_email IS '工作邮箱，可选，可能唯一';
COMMENT ON COLUMN t_corporate_user_detail.work_phone IS '工作电话，可选，可能唯一';
COMMENT ON COLUMN t_corporate_user_detail.create_time IS '记录创建时间 (不含时区信息)';
COMMENT ON COLUMN t_corporate_user_detail.update_time IS '记录最后更新时间 (不含时区信息)';


-- t_system_admin_detail (系统管理员详细信息表)
CREATE TABLE t_system_admin_detail
(id          BIGSERIAL PRIMARY KEY,                            -- 主键ID
 user_id     BIGINT UNIQUE    NOT NULL,                        -- 关联到 t_user 表的 ID，管理员的唯一用户关联 (无外键)
 admin_level admin_level_enum NOT NULL DEFAULT 'normal_admin', -- 管理员级别
    -- 其他管理员特定信息
 create_time TIMESTAMP        NOT NULL DEFAULT NOW(),          -- 创建时间
 update_time TIMESTAMP        NULL                             -- 修改时间，由应用或触发器更新
);
COMMENT ON TABLE t_system_admin_detail IS '系统管理员详细信息表，存储系统管理员角色的特有属性';
COMMENT ON COLUMN t_system_admin_detail.id IS '主键ID';
COMMENT ON COLUMN t_system_admin_detail.user_id IS '关联到 t_user 表的 ID，管理员的唯一用户关联';
COMMENT ON COLUMN t_system_admin_detail.admin_level IS '管理员级别：super_admin-超级管理员, normal_admin-普通管理员, data_admin-数据管理员等';
COMMENT ON COLUMN t_system_admin_detail.create_time IS '记录创建时间 (不含时区信息)';
COMMENT ON COLUMN t_system_admin_detail.update_time IS '记录最后更新时间 (不含时区信息)';


-- ----------------------------
-- Initial Data (Populate t_role based on RoleEnum)
-- ----------------------------
INSERT INTO t_role (role_key, role_name, role_type_key, description, create_time)
VALUES ('SUPER_ADMIN', '超级管理员', 'TENANT', '超级管理员 super-admin', NOW()),
       ('SYSTEM_USER', '平台用户', 'TENANT', '平台用户 SYSTEM_USER', NOW()),
       ('CROP_ADMIN', '企业负责人', 'CROP', '企业负责人', NOW()),
       ('CROP_USER', '企业成员', 'CROP', '企业成员', NOW()),
       ('SCH_TECH', '老师', 'SCH', '学校老师', NOW()),
       ('SCH_STU', '学生', 'SCH', '学校学生', NOW()),
       ('PROJECT_ADMIN', '项目经理', 'PROJECT', '负责管理项目的用户', NOW()),
       ('PROJECT_TEST', '测试', 'PROJECT', '项目测试角色', NOW()),
       ('PROJECT_DEV', '开发', 'PROJECT', '项目开发角色', NOW()),
       ('PROJECT_PM', '产品经理', 'PROJECT', '项目产品经理角色', NOW())


CREATE TABLE t_user_oauth2
(id                      BIGSERIAL PRIMARY KEY,               -- 主键ID
 user_id                 BIGINT       NOT NULL,               -- 关联到 t_user 表的 ID (无外键)
 provider_type           VARCHAR(50)  NOT NULL,               -- 三方登录类型，对应三方服务商，如 'wechat', 'qq', 'github'
 provider_unique_id      VARCHAR(255) NOT NULL,               -- 三方服务商提供的用户唯一ID
 provider_username       VARCHAR(255) NULL,                   -- 三方用户的账号/昵称 (服务商提供)
 credentials             VARCHAR(255) NOT NULL,               -- 三方登录获取的认证信息 (如 access_token)，**敏感信息，考虑加密存储**
 credentials_expire_time TIMESTAMP    NULL,                   -- 三方登录认证信息过期时间 (不含时区信息)
 location                VARCHAR(255) NULL,                   -- 三方用户关联的地理位置信息 (可选)
 create_time             TIMESTAMP    NOT NULL DEFAULT NOW(), -- 绑定时间 (不含时区信息)
 update_time             TIMESTAMP    NULL                    -- 修改时间，由应用或触发器更新 (不含时区信息)
);

-- Comments
COMMENT ON TABLE t_user_oauth2 IS '三方登录账户信息表，记录平台用户与外部OAuth2/OpenID Connect提供者账户的绑定关系';
COMMENT ON COLUMN t_user_oauth2.id IS '主键ID';
COMMENT ON COLUMN t_user_oauth2.user_id IS '关联到 t_user 表的 ID，平台用户的主键';
COMMENT ON COLUMN t_user_oauth2.provider_type IS '三方登录提供者类型，如 ''wechat'', ''qq'', ''github''';
COMMENT ON COLUMN t_user_oauth2.provider_unique_id IS '三方服务商为该用户提供的唯一ID，在特定提供者类型下唯一';
COMMENT ON COLUMN t_user_oauth2.provider_username IS '三方服务商提供的用户账号或昵称 (可选)';
COMMENT ON COLUMN t_user_oauth2.credentials IS '三方登录获取的认证信息 (如 access_token)，应妥善保护或加密存储';
COMMENT ON COLUMN t_user_oauth2.credentials_expire_time IS '三方登录认证信息过期时间 (不含时区信息)';
COMMENT ON COLUMN t_user_oauth2.location IS '三方用户关联的地理位置信息 (可选)';
COMMENT ON COLUMN t_user_oauth2.create_time IS '记录绑定时间 (不含时区信息)';
COMMENT ON COLUMN t_user_oauth2.update_time IS '记录最后更新时间 (不含时区信息)';
create table t_user_login_log
(id          BIGSERIAL PRIMARY KEY,                              -- 访问ID
 user_id     bigint                      not null default 0,     -- 用户编号
 user_type   character varying(50)       not null default 0,     -- 用户类型
 user_name   character varying(50)       not null default '',    -- 用户账号
 user_ip     character varying(50),                              -- 用户 IP
 user_agent  character varying(512),                             -- 浏览器 UA
 create_time timestamp without time zone not null default now(), -- 创建时间
 tenant_id   bigint
);
create index idx_create_time on t_user_login_log using btree (create_time);
create index idx_user_id on t_user_login_log using btree (user_id);
comment on table t_user_login_log is '系统访问记录';
comment on column t_user_login_log.id is '访问ID';
comment on column t_user_login_log.user_id is '用户编号';
comment on column t_user_login_log.user_type is '用户类型';
comment on column t_user_login_log.user_name is '用户账号';
comment on column t_user_login_log.user_ip is '用户 IP';
comment on column t_user_login_log.user_agent is '浏览器 UA';
comment on column t_user_login_log.create_time is '创建时间';
CREATE TABLE t_user_password_log
(id                  BIGSERIAL PRIMARY KEY,                             -- 主键ID
 user_id             BIGINT                      NOT NULL,              -- 关联到 t_user 表的 ID (无外键)
 change_time         TIMESTAMP WITHOUT TIME ZONE NOT NULL,              -- 密码修改发生的时间
 changed_by_user_id  BIGINT                      NULL,                  -- 执行密码修改操作的用户ID (如管理员)，如果用户自己修改则为NULL (无外键)
 next_reminder_time  TIMESTAMP WITHOUT TIME ZONE NULL,                  -- 基于密码策略计算的下次密码修改提醒时间点 (不含时区信息)
 policy_disable_time TIMESTAMP WITHOUT TIME ZONE NULL,                  -- 基于密码策略计算的账户禁用时间点 (如果到期未修改密码，不含时区信息)
 tenant_id           BIGINT                      NULL,                  -- 租户ID，如果系统支持多租户 (无外键)
 create_time         TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW() -- 记录创建时间 (日志写入时间，不含时区信息)
    -- update_time 字段在此表中不常用，因为日志通常是创建后不再修改
);

-- Comments
COMMENT ON TABLE t_user_password_log IS '用户密码修改记录表，记录用户每次修改密码的事件及相关的策略时间点';
COMMENT ON COLUMN t_user_password_log.id IS '主键ID';
COMMENT ON COLUMN t_user_password_log.user_id IS '关联到 t_user 表的 ID，该密码修改所属的用户';
COMMENT ON COLUMN t_user_password_log.change_time IS '密码修改操作实际发生的时间 (不含时区信息)';
COMMENT ON COLUMN t_user_password_log.changed_by_user_id IS '执行密码修改操作的用户ID。如果用户自己修改，该字段可为NULL；如果是管理员代为修改，则记录管理员的user_id';
COMMENT ON COLUMN t_user_password_log.next_reminder_time IS '基于当前密码策略计算的下次强制修改密码的提醒时间点 (不含时区信息)，如果无相关策略则为NULL';
COMMENT ON COLUMN t_user_password_log.policy_disable_time IS '基于当前密码策略计算的账户可能被禁用的时间点 (如密码过期未修改，不含时区信息)，如果无相关策略则为NULL';
COMMENT ON COLUMN t_user_password_log.tenant_id IS '租户ID，如果系统支持多租户，记录该用户所属的租户；否则为NULL';
COMMENT ON COLUMN t_user_password_log.create_time IS '记录创建时间，即该密码修改日志被写入数据库的时间 (不含时区信息)';


CREATE TABLE *********************
(id                  BIGSERIAL PRIMARY KEY,                                          -- 仍然建议使用BIGSERIAL作为逻辑主键，即使不声明PRIMARY KEY约束
 project_id          BIGINT                      NOT NULL,
 task_id             BIGINT                      NOT NULL,                           -- 允许NULL
 gitlab_project_id   BIGINT                      NOT NULL,
 gitlab_project_path VARCHAR(255),
 mapping_type        VARCHAR(50),
    -- 标准审计字段
 create_time         TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- now()是CURRENT_TIMESTAMP的别名
 update_time         TIMESTAMP WITHOUT TIME ZONE,                                    -- 应用层负责更新
 deleted             INTEGER                              DEFAULT 0,                 -- 0表示未删除，1表示已删除 (或使用BOOLEAN DEFAULT FALSE)
 create_user         BIGINT,
 update_user         BIGINT
);

COMMENT ON TABLE ********************* IS '平台项目/任务与GitLab仓库映射表 (无物理主外键, 无触发器)';
COMMENT ON COLUMN *********************.id IS '逻辑主键，自增序列';
COMMENT ON COLUMN *********************.project_id IS '平台项目ID';
COMMENT ON COLUMN *********************.task_id IS '平台任务ID (可为空)';
COMMENT ON COLUMN *********************.gitlab_project_id IS 'GitLab项目仓库的数字ID';
COMMENT ON COLUMN *********************.gitlab_project_path IS 'GitLab项目仓库的路径 (如 group/project)';
COMMENT ON COLUMN *********************.mapping_type IS '映射类型 (如 MAIN_REPO, TEST_REPO)';
COMMENT ON COLUMN *********************.create_time IS '记录创建时间';
COMMENT ON COLUMN *********************.update_time IS '记录最后更新时间 (应用层维护)';
COMMENT ON COLUMN *********************.deleted IS '逻辑删除标志 (0=未删除, 1=已删除)';
COMMENT ON COLUMN *********************.create_user IS '记录创建人用户ID';
COMMENT ON COLUMN *********************.update_user IS '记录最后修改人用户ID';

CREATE TABLE t_course_info (
    id BIGINT PRIMARY KEY NOT NULL,
    course_code VARCHAR(50) UNIQUE NOT NULL,
    course_name VARCHAR(255) NOT NULL,
    description TEXT,
    credits NUMERIC(3,1),
    hours INTEGER,
    course_type VARCHAR(50),
    department VARCHAR(255),
    instructor_user_id BIGINT, -- 逻辑外键，指向 t_user_info.id
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE', -- 例如: ACTIVE, INACTIVE, ARCHIVED
    start_date DATE,
    end_date DATE,
    prerequisites TEXT,
    syllabus_url VARCHAR(512),
    cover_image_url VARCHAR(512),
    create_time TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_user BIGINT,
    update_time TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_user BIGINT,
    tenant_id BIGINT,
    deleted SMALLINT NOT NULL DEFAULT 0
);

-- 为常用查询字段创建索引

-- 添加注释 (可选, 但推荐)
COMMENT ON TABLE t_course_info IS '课程信息表';
COMMENT ON COLUMN t_course_info.id IS '课程唯一标识 (雪花算法或序列生成)';
COMMENT ON COLUMN t_course_info.course_code IS '课程代码 (学校或机构内部的唯一代码)';
COMMENT ON COLUMN t_course_info.course_name IS '课程完整名称';
COMMENT ON COLUMN t_course_info.description IS '课程详细描述';
COMMENT ON COLUMN t_course_info.credits IS '课程学分';
COMMENT ON COLUMN t_course_info.hours IS '课程总学时';
COMMENT ON COLUMN t_course_info.course_type IS '课程类型 (如: 专业必修, 专业选修, 公共必修)';
COMMENT ON COLUMN t_course_info.department IS '开课院系/部门ID (逻辑关联到 t_department 表)';
COMMENT ON COLUMN t_course_info.instructor_user_id IS '主讲教师的用户ID (逻辑关联到 t_user_info 表)';
COMMENT ON COLUMN t_course_info.status IS '课程状态 (如: ACTIVE-开设中, INACTIVE-未开设, ARCHIVED-已归档)';
COMMENT ON COLUMN t_course_info.start_date IS '课程开始日期 (可选, 如适用学期制)';
COMMENT ON COLUMN t_course_info.end_date IS '课程结束日期 (可选)';
COMMENT ON COLUMN t_course_info.prerequisites IS '先修课程要求描述 (文本形式)';
COMMENT ON COLUMN t_course_info.syllabus_url IS '课程大纲文件URL (可选)';
COMMENT ON COLUMN t_course_info.cover_image_url IS '课程封面图片URL (可选)';
COMMENT ON COLUMN t_course_info.create_time IS '记录创建时间';
COMMENT ON COLUMN t_course_info.create_user IS '创建人用户ID (逻辑关联到 t_user_info 表)';
COMMENT ON COLUMN t_course_info.update_time IS '记录最后更新时间';
COMMENT ON COLUMN t_course_info.update_user IS '最后更新人用户ID (逻辑关联到 t_user_info 表)';
COMMENT ON COLUMN t_course_info.tenant_id IS '租户ID (如果系统支持多租户)';
COMMENT ON COLUMN t_course_info.deleted IS '逻辑删除标志 (0=未删除, 1=已删除)';

CREATE TABLE t_student_course_enrollment (
    "id" BIGINT PRIMARY KEY NOT NULL,
    "student_user_id" BIGINT NOT NULL,
    "course_id" BIGINT NOT NULL,
    "enrollment_date" DATE NOT NULL DEFAULT CURRENT_DATE,
    "enrollment_status" VARCHAR(30) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'ENROLLED'::character varying,
    "final_grade" NUMERIC(5,2),
    "completion_date" DATE,
    "semester" VARCHAR(50) COLLATE "pg_catalog"."default",
    "create_time" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "create_user" BIGINT,
    "update_time" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "update_user" BIGINT,
    "tenant_id" BIGINT,
    "deleted" SMALLINT NOT NULL DEFAULT 0
)
;
COMMENT ON COLUMN t_student_course_enrollment."id" IS '选课记录唯一标识 (雪花算法或序列)';
COMMENT ON COLUMN t_student_course_enrollment."student_user_id" IS '学生的用户ID (逻辑关联 t_user_info.id)';
COMMENT ON COLUMN t_student_course_enrollment."course_id" IS '所选课程的ID (逻辑关联 t_course.id)';
COMMENT ON COLUMN t_student_course_enrollment."enrollment_date" IS '学生选修该课程的日期';
COMMENT ON COLUMN t_student_course_enrollment."enrollment_status" IS '选课状态 (枚举 EnrollmentStatusEnum 的 code)';
COMMENT ON COLUMN t_student_course_enrollment."final_grade" IS '学生在该次选课获得的最终总成绩';
COMMENT ON COLUMN t_student_course_enrollment."completion_date" IS '学生完成该课程的日期 (可选)';
COMMENT ON COLUMN t_student_course_enrollment."semester" IS '学生选修该课程的学期 (例如 "2024-2025秋季学期")';
COMMENT ON COLUMN t_student_course_enrollment."create_time" IS '记录创建时间';
COMMENT ON COLUMN t_student_course_enrollment."create_user" IS '创建人用户ID';
COMMENT ON COLUMN t_student_course_enrollment."update_time" IS '记录最后更新时间';
COMMENT ON COLUMN t_student_course_enrollment."update_user" IS '最后更新人用户ID';
COMMENT ON COLUMN t_student_course_enrollment."tenant_id" IS '租户ID';
COMMENT ON COLUMN t_student_course_enrollment."deleted" IS '逻辑删除标志 (0=未删除, 1=已删除)';
COMMENT ON TABLE t_student_course_enrollment IS '学生课程选课记录表';


CREATE TABLE t_student_course_assessment (
    "id" BIGINT PRIMARY KEY NOT NULL,
    "enrollment_id" BIGINT NOT NULL,
    "assessment_name" VARCHAR(255) COLLATE "pg_catalog"."default" NOT NULL,
    "assessment_type" VARCHAR(30) COLLATE "pg_catalog"."default" NOT NULL,
    "assessment_date" DATE,
    "attended" BOOLEAN NOT NULL DEFAULT false,
    "score" NUMERIC(5,2),
    "weight" NUMERIC(4,3) CHECK (weight >= 0 AND weight <= 1), -- 权重，如 0.400 代表 40%
    "comments" TEXT COLLATE "pg_catalog"."default",
    "create_time" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "create_user" BIGINT,
    "update_time" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "update_user" BIGINT,
    "tenant_id" BIGINT,
    "deleted" SMALLINT NOT NULL DEFAULT 0
)
;

COMMENT ON COLUMN t_student_course_assessment."id" IS '考核记录唯一标识';
COMMENT ON COLUMN t_student_course_assessment."enrollment_id" IS '对应的学生选课记录ID (逻辑关联 t_student_course_enrollment.id)';
COMMENT ON COLUMN t_student_course_assessment."assessment_name" IS '考核名称 (如: "期末考试", "第一次期中测试", "大作业")';
COMMENT ON COLUMN t_student_course_assessment."assessment_type" IS '考核类型 (枚举 AssessmentTypeEnum 的 code)';
COMMENT ON COLUMN t_student_course_assessment."assessment_date" IS '考核日期';
COMMENT ON COLUMN t_student_course_assessment."attended" IS '学生是否参加了本次考核 (true=参加, false=未参加)';
COMMENT ON COLUMN t_student_course_assessment."score" IS '本次考核得分';
COMMENT ON COLUMN t_student_course_assessment."weight" IS '本次考核在总成绩中所占权重 (0.0 到 1.0 之间)';
COMMENT ON COLUMN t_student_course_assessment."comments" IS '教师对本次考核的评语 (可选)';
COMMENT ON COLUMN t_student_course_assessment.create_time IS '记录创建时间';
COMMENT ON COLUMN t_student_course_assessment.create_user IS '创建人用户ID (逻辑关联到 t_user_info 表)';
COMMENT ON COLUMN t_student_course_assessment.update_time IS '记录最后更新时间';
COMMENT ON COLUMN t_student_course_assessment.update_user IS '最后更新人用户ID (逻辑关联到 t_user_info 表)';
COMMENT ON COLUMN t_student_course_assessment.tenant_id IS '租户ID (如果系统支持多租户)';
COMMENT ON COLUMN t_student_course_assessment.deleted IS '逻辑删除标志 (0=未删除, 1=已删除)';
COMMENT ON TABLE t_student_course_assessment IS '学生课程考核记录表';

CREATE TABLE t_permission_info (
    id BIGSERIAL PRIMARY KEY NOT NULL, -- BIGSERIAL 会自动创建序列并设置为主键
    permission_code VARCHAR(100) NOT NULL UNIQUE, -- 权限编码，全局唯一
    permission_name VARCHAR(255) NOT NULL, -- 权限名称
    permission_group_name VARCHAR(100), -- 权限所属分组名称
    description VARCHAR(500), -- 权限详细描述
    parent_id BIGINT, -- 父权限ID (用于树形结构，可选)
    sort_order int4 DEFAULT 0, -- 排序值
    enabled int4 DEFAULT 0, -- 是否启用 (TRUE:启用, FALSE:禁用)
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- TIMESTAMPTZ 包含时区信息
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_user BIGINT,
    update_user BIGINT
);

COMMENT ON TABLE t_permission_info IS '权限信息表';
COMMENT ON COLUMN t_permission_info.id IS '权限ID，主键自增';
COMMENT ON COLUMN t_permission_info.permission_code IS '权限编码，全局唯一 (如 USER_VIEW, ARTICLE_EDIT)';
COMMENT ON COLUMN t_permission_info.permission_name IS '权限名称 (如 查看用户, 编辑文章)';
COMMENT ON COLUMN t_permission_info.permission_group_name IS '权限所属分组名称 (如 用户管理, 内容管理)';
COMMENT ON COLUMN t_permission_info.description IS '权限详细描述';
COMMENT ON COLUMN t_permission_info.parent_id IS '父权限ID (用于树形结构，可选)';
COMMENT ON COLUMN t_permission_info.sort_order IS '排序值，越小越靠前';
COMMENT ON COLUMN t_permission_info.enabled IS '是否启用 (0:启用, 1:禁用)';
COMMENT ON COLUMN t_permission_info.create_time IS '记录创建时间';
COMMENT ON COLUMN t_permission_info.update_time IS '记录最后更新时间';
COMMENT ON COLUMN t_permission_info.create_user IS '创建人ID';
COMMENT ON COLUMN t_permission_info.update_user IS '更新人ID';


-- ----------------------------
-- 表结构 t_role_permission_relation (角色权限关联表)
-- ----------------------------
-- DROP TABLE IF EXISTS t_role_permission_relation; -- 如果需要，先删除旧表
CREATE TABLE t_role_permission_relation (
    id BIGSERIAL PRIMARY KEY, -- 关联ID，主键自增
    role_id BIGINT NOT NULL, -- 角色ID
    permission_id BIGINT NOT NULL, -- 权限ID
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_user BIGINT,
    update_user BIGINT,
    CONSTRAINT uk_role_permission UNIQUE (role_id, permission_id) -- 确保同一角色对同一权限的关联唯一
);

COMMENT ON TABLE t_role_permission_relation IS '角色权限关联表';
COMMENT ON COLUMN t_role_permission_relation.id IS '关联ID，主键自增';
COMMENT ON COLUMN t_role_permission_relation.role_id IS '角色ID (逻辑关联 t_role.id)';
COMMENT ON COLUMN t_role_permission_relation.permission_id IS '权限ID (逻辑关联 t_permission_info.id)';
COMMENT ON COLUMN t_role_permission_relation.create_time IS '记录创建时间';
COMMENT ON COLUMN t_role_permission_relation.update_time IS '记录最后更新时间';
COMMENT ON COLUMN t_role_permission_relation.create_user IS '创建人ID';
COMMENT ON COLUMN t_role_permission_relation.update_user IS '更新人ID';

-- 清空现有权限表 (可选，如果需要全新插入)
-- DELETE FROM t_permission_info;
-- ALTER TABLE t_permission_info AUTO_INCREMENT = 1; -- 如果您的ID是自增的，重置自增起始值

-- 系统管理权限
INSERT INTO t_permission_info (permission_code, permission_name, permission_group_name, description, parent_id, sort_order, enabled, create_time, update_time, create_user, update_user) VALUES
                                                                                                                                                                                             -- 用户管理
                                                                                                                                                                                             ('USER_VIEW_LIST', '查看用户列表', '系统管理', '允许查看系统中的用户列表信息', NULL, 101, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('USER_CREATE', '创建用户', '系统管理', '允许创建新的用户账户', NULL, 102, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('USER_EDIT_INFO', '修改用户信息', '系统管理', '允许修改现有用户的基本信息、角色和状态', NULL, 103, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('USER_DELETE', '删除用户', '系统管理', '允许删除用户账户（通常是逻辑删除）', NULL, 104, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('USER_RESET_PASSWORD', '重置用户密码', '系统管理', '允许管理员重置用户的登录密码', NULL, 105, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('USER_BATCH_IMPORT', '批量导入用户', '系统管理', '允许通过文件批量导入用户数据', NULL, 106, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('USER_EXPORT_DATA', '导出用户信息', '系统管理', '允许导出用户信息列表', NULL, 107, 0, NOW(), NOW(), 1, 1),

                                                                                                                                                                                             -- 角色管理
                                                                                                                                                                                             ('ROLE_VIEW_LIST', '查看角色列表', '系统管理', '允许查看系统中的角色列表及其权限配置', NULL, 201, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('ROLE_CREATE', '创建角色', '系统管理', '允许创建新的角色并分配权限', NULL, 202, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('ROLE_EDIT_INFO', '修改角色信息', '系统管理', '允许修改现有角色的名称、描述和权限', NULL, 203, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('ROLE_DELETE', '删除角色', '系统管理', '允许删除自定义角色（未被使用的）', NULL, 204, 0, NOW(), NOW(), 1, 1),

                                                                                                                                                                                             -- 学校组织管理
                                                                                                                                                                                             ('ACADEMIC_ORG_VIEW', '查看学校组织', '系统管理', '允许查看学校、院系、班级等组织信息', NULL, 301, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('ACADEMIC_ORG_MANAGE', '管理学校组织', '系统管理', '允许创建、修改、删除学校、院系、班级等', NULL, 302, 0, NOW(), NOW(), 1, 1);

-- 项目与任务管理权限
INSERT INTO t_permission_info (permission_code, permission_name, permission_group_name, description, parent_id, sort_order, enabled, create_time, update_time, create_user, update_user) VALUES
                                                                                                                                                                                             -- 项目管理
                                                                                                                                                                                             ('PROJECT_VIEW_ALL', '查看所有项目', '项目与任务管理', '允许查看平台内所有项目列表和详情（管理员/特定角色）', NULL, 1001, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('PROJECT_VIEW_ASSIGNED', '查看我的项目', '项目与任务管理', '允许用户查看自己参与或负责的项目列表和详情', NULL, 1002, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('PROJECT_CREATE', '创建项目', '项目与任务管理', '允许创建新的教学项目', NULL, 1003, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('PROJECT_EDIT_OWN', '修改我创建的项目', '项目与任务管理', '允许用户修改自己创建的项目的基本信息、阶段和评审设置', NULL, 1004, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('PROJECT_EDIT_ANY', '修改任意项目', '项目与任务管理', '允许修改平台内任何项目的配置（管理员/特定角色）', NULL, 1005, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('PROJECT_DELETE_OWN', '删除我创建的项目', '项目与任务管理', '允许用户删除自己创建的（未开始或符合条件）项目', NULL, 1006, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('PROJECT_DELETE_ANY', '删除任意项目', '项目与任务管理', '允许删除平台内任何项目（管理员/特定角色）', NULL, 1007, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('PROJECT_MANAGE_MEMBERS', '管理项目成员', '项目与任务管理', '允许为项目添加、移除成员，或修改项目内角色', NULL, 1008, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('PROJECT_PUBLISH', '发布项目', '项目与任务管理', '允许将草稿状态的项目发布为正式项目', NULL, 1009, 0, NOW(), NOW(), 1, 1),

                                                                                                                                                                                             -- 任务管理 (作为项目管理的一部分，但权限独立)
                                                                                                                                                                                             ('TASK_VIEW_IN_PROJECT', '查看项目任务', '项目与任务管理', '允许查看所属项目中的任务列表和详情', NULL, 1101, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('TASK_CREATE_IN_PROJECT', '在项目中创建任务', '项目与任务管理', '允许在项目中创建新任务', NULL, 1102, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('TASK_EDIT_ASSIGNED', '修改我的任务', '项目与任务管理', '允许用户修改分配给自己的任务的某些属性（如状态、备注）', NULL, 1103, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('TASK_EDIT_IN_PROJECT', '修改项目内任务', '项目与任务管理', '允许项目负责人或管理员修改项目内的任何任务', NULL, 1104, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('TASK_DELETE_IN_PROJECT', '删除项目内任务', '项目与任务管理', '允许项目负责人或管理员删除项目内的任务', NULL, 1105, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('TASK_ASSIGN_MEMBER', '分配任务成员', '项目与任务管理', '允许将任务分配给项目成员', NULL, 1106, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('TASK_SUBMIT_DELIVERABLE', '提交任务成果', '项目与任务管理', '允许学生提交任务的阶段性成果或最终成果', NULL, 1107, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('TASK_REVIEW_SUBMISSION', '评审任务成果', '项目与任务管理', '允许教师或企业导师对提交的任务成果进行评审打分', NULL, 1108, 0, NOW(), NOW(), 1, 1);

-- 可以添加更多分组和权限，例如：
-- 模板管理权限 (permission_group_name: "模板管理")
INSERT INTO t_permission_info (permission_code, permission_name, permission_group_name, description, parent_id, sort_order, enabled, create_time, update_time, create_user, update_user) VALUES
                                                                                                                                                                                             ('TEMPLATE_TASK_VIEW', '查看任务模板', '模板管理', '允许查看任务模板列表', NULL, 2001, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('TEMPLATE_TASK_MANAGE', '管理任务模板', '模板管理', '允许创建、修改、删除任务模板', NULL, 2002, 0, NOW(), NOW(), 1, 1);

-- 评审管理权限 (permission_group_name: "评审管理")
INSERT INTO t_permission_info (permission_code, permission_name, permission_group_name, description, parent_id, sort_order, enabled, create_time, update_time, create_user, update_user) VALUES
                                                                                                                                                                                             ('REVIEW_CRITERIA_VIEW', '查看评审标准', '评审管理', '允许查看系统和自定义的评审标准', NULL, 3001, 0, NOW(), NOW(), 1, 1),
                                                                                                                                                                                             ('REVIEW_CRITERIA_MANAGE', '管理评审标准', '评审管理', '允许创建、修改、删除自定义评审标准', NULL, 3002, 0, NOW(), NOW(), 1, 1);



CREATE TABLE t_common_tag (
    id BIGSERIAL PRIMARY KEY,
    tag_name VARCHAR(50) NOT NULL,
    tag_category VARCHAR(30) NOT NULL DEFAULT '通用',
    tag_color VARCHAR(20) DEFAULT '#1890ff',
    tag_description VARCHAR(200),
    applicable_entity_type VARCHAR(50),
    usage_count INTEGER DEFAULT 0,
    tag_weight INTEGER DEFAULT 1,
    is_system_tag BOOLEAN DEFAULT false,
    tag_status VARCHAR(20) DEFAULT 'ACTIVE',
    create_user BIGINT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_user BIGINT,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ext_field1 VARCHAR(100),
    ext_field2 VARCHAR(100)
);

-- 添加表注释
COMMENT ON TABLE t_common_tag IS '通用标签表';
COMMENT ON COLUMN t_common_tag.id IS '主键ID';
COMMENT ON COLUMN t_common_tag.tag_name IS '标签名称';
COMMENT ON COLUMN t_common_tag.tag_category IS '标签分类';
COMMENT ON COLUMN t_common_tag.tag_color IS '标签颜色';
COMMENT ON COLUMN t_common_tag.tag_description IS '标签描述';
COMMENT ON COLUMN t_common_tag.applicable_entity_type IS '适用实体类型';
COMMENT ON COLUMN t_common_tag.usage_count IS '使用次数';
COMMENT ON COLUMN t_common_tag.tag_weight IS '标签权重';
COMMENT ON COLUMN t_common_tag.is_system_tag IS '是否系统标签';
COMMENT ON COLUMN t_common_tag.tag_status IS '标签状态';
COMMENT ON COLUMN t_common_tag.create_user IS '创建人';
COMMENT ON COLUMN t_common_tag.create_time IS '创建时间';
COMMENT ON COLUMN t_common_tag.update_user IS '更新人';
COMMENT ON COLUMN t_common_tag.update_time IS '更新时间';
COMMENT ON COLUMN t_common_tag.ext_field1 IS '扩展字段1';
COMMENT ON COLUMN t_common_tag.ext_field2 IS '扩展字段2';

-- 4. 重建关联关系表
CREATE TABLE t_entity_tag_relation (
    id BIGSERIAL PRIMARY KEY,
    entity_id BIGINT NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    tag_id BIGINT NOT NULL,
    relation_status VARCHAR(20) DEFAULT 'ACTIVE',
    create_user BIGINT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_user BIGINT,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT fk_tag_relation_tag FOREIGN KEY (tag_id) REFERENCES t_common_tag(id) ON DELETE CASCADE
);

-- 添加关联表注释
COMMENT ON TABLE t_entity_tag_relation IS '实体标签关联关系表';
COMMENT ON COLUMN t_entity_tag_relation.id IS '主键ID';
COMMENT ON COLUMN t_entity_tag_relation.entity_id IS '实体ID';
COMMENT ON COLUMN t_entity_tag_relation.entity_type IS '实体类型';
COMMENT ON COLUMN t_entity_tag_relation.tag_id IS '标签ID';
COMMENT ON COLUMN t_entity_tag_relation.relation_status IS '关联状态';
COMMENT ON COLUMN t_entity_tag_relation.create_user IS '创建人';
COMMENT ON COLUMN t_entity_tag_relation.create_time IS '创建时间';
COMMENT ON COLUMN t_entity_tag_relation.update_user IS '更新人';
COMMENT ON COLUMN t_entity_tag_relation.update_time IS '更新时间';

-- 5. 创建索引
SELECT '=== 创建索引 ===' as info;

-- 标签表索引
CREATE UNIQUE INDEX idx_tag_name_category ON t_common_tag(tag_name, tag_category);
CREATE INDEX idx_tag_category ON t_common_tag(tag_category);
CREATE INDEX idx_tag_usage_count ON t_common_tag(usage_count DESC);
CREATE INDEX idx_tag_status ON t_common_tag(tag_status);
CREATE INDEX idx_tag_entity_type ON t_common_tag(applicable_entity_type);

-- 关联关系表索引
CREATE UNIQUE INDEX idx_entity_tag_unique ON t_entity_tag_relation(entity_id, entity_type, tag_id);
CREATE INDEX idx_entity_tag_entity ON t_entity_tag_relation(entity_id, entity_type);
CREATE INDEX idx_entity_tag_tag ON t_entity_tag_relation(tag_id);
CREATE INDEX idx_entity_tag_status ON t_entity_tag_relation(relation_status);

-- 1. 技术栈标签
INSERT INTO nybc_edu.t_common_tag (
    tag_name, tag_category, tag_color, tag_description, applicable_entity_type,
    usage_count, tag_weight, is_system_tag, tag_status,
    create_user, create_time, update_user, update_time, ext_field1, ext_field2
) VALUES
      ('Java', 'tech_stack', '#f56a00', 'Java编程语言', 'PROJECT', 0, 90, true, 'ACTIVE', 1, now(), 1, now(), 'programming_language', 'backend'),
      ('JavaScript', 'tech_stack', '#faad14', 'JavaScript编程语言', 'PROJECT', 0, 85, true, 'ACTIVE', 1, now(), 1, now(), 'programming_language', 'frontend'),
      ('Python', 'tech_stack', '#52c41a', 'Python编程语言', 'PROJECT', 0, 88, true, 'ACTIVE', 1, now(), 1, now(), 'programming_language', 'backend'),
      ('Vue.js', 'tech_stack', '#13c2c2', 'Vue.js前端框架', 'PROJECT', 0, 85, true, 'ACTIVE', 1, now(), 1, now(), 'frontend_framework', 'spa'),
      ('React', 'tech_stack', '#1890ff', 'React前端框架', 'PROJECT', 0, 82, true, 'ACTIVE', 1, now(), 1, now(), 'frontend_framework', 'spa'),
      ('Spring Boot', 'tech_stack', '#722ed1', 'Spring Boot后端框架', 'PROJECT', 0, 92, true, 'ACTIVE', 1, now(), 1, now(), 'backend_framework', 'java'),
      ('Node.js', 'tech_stack', '#eb2f96', 'Node.js运行环境', 'PROJECT', 0, 78, true, 'ACTIVE', 1, now(), 1, now(), 'runtime', 'javascript'),
      ('MySQL', 'tech_stack', '#fa8c16', 'MySQL数据库', 'PROJECT', 0, 75, true, 'ACTIVE', 1, now(), 1, now(), 'database', 'relational'),
      ('PostgreSQL', 'tech_stack', '#2f54eb', 'PostgreSQL数据库', 'PROJECT', 0, 80, true, 'ACTIVE', 1, now(), 1, now(), 'database', 'relational'),
      ('Redis', 'tech_stack', '#f5222d', 'Redis缓存数据库', 'PROJECT', 0, 70, true, 'ACTIVE', 1, now(), 1, now(), 'database', 'cache');

-- 2. 项目类型标签（包含智能体）
INSERT INTO nybc_edu.t_common_tag (
    tag_name, tag_category, tag_color, tag_description, applicable_entity_type,
    usage_count, tag_weight, is_system_tag, tag_status,
    create_user, create_time, update_user, update_time, ext_field1, ext_field2
) VALUES
      ('Web应用', 'project_type', '#1890ff', 'Web应用项目', 'PROJECT', 0, 95, true, 'ACTIVE', 1, now(), 1, now(), 'application_type', 'web'),
      ('移动应用', 'project_type', '#52c41a', '移动应用项目', 'PROJECT', 0, 88, true, 'ACTIVE', 1, now(), 1, now(), 'application_type', 'mobile'),
      ('桌面应用', 'project_type', '#722ed1', '桌面应用项目', 'PROJECT', 0, 75, true, 'ACTIVE', 1, now(), 1, now(), 'application_type', 'desktop'),
      ('数据分析', 'project_type', '#fa8c16', '数据分析项目', 'PROJECT', 0, 82, true, 'ACTIVE', 1, now(), 1, now(), 'project_domain', 'data_science'),
      ('机器学习', 'project_type', '#eb2f96', '机器学习项目', 'PROJECT', 0, 85, true, 'ACTIVE', 1, now(), 1, now(), 'project_domain', 'ai_ml'),
      ('小程序', 'project_type', '#13c2c2', '小程序项目', 'PROJECT', 0, 78, true, 'ACTIVE', 1, now(), 1, now(), 'application_type', 'miniprogram'),
      ('API服务', 'project_type', '#faad14', 'API服务项目', 'PROJECT', 0, 80, true, 'ACTIVE', 1, now(), 1, now(), 'service_type', 'api'),
      ('智能体', 'project_type', '#9254de', '智能体项目', 'PROJECT', 0, 90, true, 'ACTIVE', 1, now(), 1, now(), 'ai_type', 'agent');

-- 3. 项目模板标签（与项目类型保持一致）
INSERT INTO nybc_edu.t_common_tag (
    tag_name, tag_category, tag_color, tag_description, applicable_entity_type,
    usage_count, tag_weight, is_system_tag, tag_status,
    create_user, create_time, update_user, update_time, ext_field1, ext_field2
) VALUES
      ('Web应用', 'project_template', '#1890ff', 'Web应用项目模板', 'PROJECT_TEMPLATE', 0, 95, true, 'ACTIVE', 1, now(), 1, now(), 'application_type', 'web'),
      ('移动应用', 'project_template', '#52c41a', '移动应用项目模板', 'PROJECT_TEMPLATE', 0, 88, true, 'ACTIVE', 1, now(), 1, now(), 'application_type', 'mobile'),
      ('桌面应用', 'project_template', '#722ed1', '桌面应用项目模板', 'PROJECT_TEMPLATE', 0, 75, true, 'ACTIVE', 1, now(), 1, now(), 'application_type', 'desktop'),
      ('数据分析', 'project_template', '#fa8c16', '数据分析项目模板', 'PROJECT_TEMPLATE', 0, 82, true, 'ACTIVE', 1, now(), 1, now(), 'project_domain', 'data_science'),
      ('机器学习', 'project_template', '#eb2f96', '机器学习项目模板', 'PROJECT_TEMPLATE', 0, 85, true, 'ACTIVE', 1, now(), 1, now(), 'project_domain', 'ai_ml'),
      ('小程序', 'project_template', '#13c2c2', '小程序项目模板', 'PROJECT_TEMPLATE', 0, 78, true, 'ACTIVE', 1, now(), 1, now(), 'application_type', 'miniprogram'),
      ('API服务', 'project_template', '#faad14', 'API服务项目模板', 'PROJECT_TEMPLATE', 0, 80, true, 'ACTIVE', 1, now(), 1, now(), 'service_type', 'api'),
      ('智能体', 'project_template', '#9254de', '智能体项目模板', 'PROJECT_TEMPLATE', 0, 90, true, 'ACTIVE', 1, now(), 1, now(), 'ai_type', 'agent');

-- 4. 文档类型标签
INSERT INTO nybc_edu.t_common_tag (
    tag_name, tag_category, tag_color, tag_description, applicable_entity_type,
    usage_count, tag_weight, is_system_tag, tag_status,
    create_user, create_time, update_user, update_time, ext_field1, ext_field2
) VALUES
      ('需求文档', 'document_type', '#1890ff', '项目需求说明文档', 'FILE', 0, 90, true, 'ACTIVE', 1, now(), 1, now(), 'document_type', 'requirement'),
      ('设计文档', 'document_type', '#722ed1', '系统设计文档', 'FILE', 0, 85, true, 'ACTIVE', 1, now(), 1, now(), 'document_type', 'design'),
      ('用户手册', 'document_type', '#52c41a', '用户使用手册', 'FILE', 0, 75, true, 'ACTIVE', 1, now(), 1, now(), 'document_type', 'manual'),
      ('API文档', 'document_type', '#faad14', 'API接口文档', 'FILE', 0, 80, true, 'ACTIVE', 1, now(), 1, now(), 'document_type', 'api_doc'),
      ('技术文档', 'document_type', '#13c2c2', '技术实现文档', 'FILE', 0, 82, true, 'ACTIVE', 1, now(), 1, now(), 'document_type', 'technical');

-- 5. 课程相关标签
INSERT INTO nybc_edu.t_common_tag (
    tag_name, tag_category, tag_color, tag_description, applicable_entity_type,
    usage_count, tag_weight, is_system_tag, tag_status,
    create_user, create_time, update_user, update_time, ext_field1, ext_field2
) VALUES
      ('基础课程', 'course_type', '#1890ff', '基础理论课程', 'COURSE', 0, 95, true, 'ACTIVE', 1, now(), 1, now(), 'course_level', 'foundation'),
      ('实践课程', 'course_type', '#52c41a', '实践操作课程', 'COURSE', 0, 90, true, 'ACTIVE', 1, now(), 1, now(), 'course_level', 'practice'),
      ('高级课程', 'course_type', '#722ed1', '高级进阶课程', 'COURSE', 0, 85, true, 'ACTIVE', 1, now(), 1, now(), 'course_level', 'advanced'),
      ('选修课程', 'course_type', '#faad14', '选修课程', 'COURSE', 0, 75, true, 'ACTIVE', 1, now(), 1, now(), 'course_requirement', 'elective'),
      ('必修课程', 'course_type', '#f56a00', '必修课程', 'COURSE', 0, 88, true, 'ACTIVE', 1, now(), 1, now(), 'course_requirement', 'required');

-- 6. 难度等级标签
INSERT INTO nybc_edu.t_common_tag (
    tag_name, tag_category, tag_color, tag_description, applicable_entity_type,
    usage_count, tag_weight, is_system_tag, tag_status,
    create_user, create_time, update_user, update_time, ext_field1, ext_field2
) VALUES
      ('入门', 'difficulty_level', '#52c41a', '入门级别', 'OTHER', 0, 100, true, 'ACTIVE', 1, now(), 1, now(), 'difficulty', 'beginner'),
      ('初级', 'difficulty_level', '#1890ff', '初级水平', 'OTHER', 0, 95, true, 'ACTIVE', 1, now(), 1, now(), 'difficulty', 'elementary'),
      ('中级', 'difficulty_level', '#faad14', '中级水平', 'OTHER', 0, 90, true, 'ACTIVE', 1, now(), 1, now(), 'difficulty', 'intermediate'),
      ('高级', 'difficulty_level', '#fa8c16', '高级水平', 'OTHER', 0, 85, true, 'ACTIVE', 1, now(), 1, now(), 'difficulty', 'advanced'),
      ('专家', 'difficulty_level', '#f5222d', '专家级别', 'OTHER', 0, 80, true, 'ACTIVE', 1, now(), 1, now(), 'difficulty', 'expert');

-- 7. 状态标签
INSERT INTO nybc_edu.t_common_tag (
    tag_name, tag_category, tag_color, tag_description, applicable_entity_type,
    usage_count, tag_weight, is_system_tag, tag_status,
    create_user, create_time, update_user, update_time, ext_field1, ext_field2
) VALUES
      ('进行中', 'status', '#1890ff', '正在进行中', 'OTHER', 0, 100, true, 'ACTIVE', 1, now(), 1, now(), 'status', 'in_progress'),
      ('已完成', 'status', '#52c41a', '已经完成', 'OTHER', 0, 95, true, 'ACTIVE', 1, now(), 1, now(), 'status', 'completed'),
      ('待开始', 'status', '#faad14', '等待开始', 'OTHER', 0, 90, true, 'ACTIVE', 1, now(), 1, now(), 'status', 'pending'),
      ('已暂停', 'status', '#fa8c16', '已暂停执行', 'OTHER', 0, 85, true, 'ACTIVE', 1, now(), 1, now(), 'status', 'paused'),
      ('已取消', 'status', '#f5222d', '已取消执行', 'OTHER', 0, 80, true, 'ACTIVE', 1, now(), 1, now(), 'status', 'cancelled');

-- 8. 作业类型标签
INSERT INTO nybc_edu.t_common_tag (
    tag_name, tag_category, tag_color, tag_description, applicable_entity_type,
    usage_count, tag_weight, is_system_tag, tag_status,
    create_user, create_time, update_user, update_time, ext_field1, ext_field2
) VALUES
      ('编程作业', 'assignment_type', '#722ed1', '编程实现作业', 'ASSIGNMENT', 0, 95, true, 'ACTIVE', 1, now(), 1, now(), 'assignment_type', 'coding'),
      ('理论作业', 'assignment_type', '#1890ff', '理论分析作业', 'ASSIGNMENT', 0, 85, true, 'ACTIVE', 1, now(), 1, now(), 'assignment_type', 'theory'),
      ('实验报告', 'assignment_type', '#52c41a', '实验报告作业', 'ASSIGNMENT', 0, 80, true, 'ACTIVE', 1, now(), 1, now(), 'assignment_type', 'experiment'),
      ('小组作业', 'assignment_type', '#faad14', '小组协作作业', 'ASSIGNMENT', 0, 88, true, 'ACTIVE', 1, now(), 1, now(), 'assignment_type', 'group'),
      ('个人作业', 'assignment_type', '#13c2c2', '个人独立作业', 'ASSIGNMENT', 0, 90, true, 'ACTIVE', 1, now(), 1, now(), 'assignment_type', 'individual');



INSERT INTO t_common_tag (tag_name, tag_category, tag_color, tag_description, usage_count, is_system_tag, tag_status, applicable_entity_type) VALUES
                                                                                                                                                  ('Web应用', 'task_type', '#1890ff', 'Web应用开发任务', 0, true, 'ACTIVE', 'TASK'),
                                                                                                                                                  ('移动应用', 'task_type', '#52c41a', '移动应用开发任务', 0, true, 'ACTIVE', 'TASK'),
                                                                                                                                                  ('桌面应用', 'task_type', '#722ed1', '桌面应用开发任务', 0, true, 'ACTIVE', 'TASK'),
                                                                                                                                                  ('数据分析', 'task_type', '#fa8c16', '数据分析处理任务', 0, true, 'ACTIVE', 'TASK'),
                                                                                                                                                  ('机器学习', 'task_type', '#eb2f96', '机器学习算法任务', 0, true, 'ACTIVE', 'TASK'),
                                                                                                                                                  ('小程序', 'task_type', '#13c2c2', '小程序开发任务', 0, true, 'ACTIVE', 'TASK'),
                                                                                                                                                  ('API服务', 'task_type', '#faad14', 'API接口开发任务', 0, true, 'ACTIVE', 'TASK'),
                                                                                                                                                  ('系统工具', 'task_type', '#f56a00', '系统工具开发任务', 0, true, 'ACTIVE', 'TASK');






CREATE TABLE edu_ops_git_stats_daily (
    id BIGSERIAL PRIMARY KEY,
    platform_project_id BIGINT NOT NULL,
    platform_task_id BIGINT,
    gitlab_project_id BIGINT NOT NULL,
    stat_date DATE NOT NULL,
    committer_email VARCHAR(255) NOT NULL,
    commit_count INT NOT NULL DEFAULT 0,
    lines_added INT NOT NULL DEFAULT 0,
    lines_deleted INT NOT NULL DEFAULT 0,
    create_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    update_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT uk_project_task_user_date UNIQUE (platform_project_id, platform_task_id, committer_email, stat_date)
);

-- 表和字段注释
COMMENT ON TABLE edu_ops_git_stats_daily IS 'Git每日提交聚合统计表';
COMMENT ON COLUMN edu_ops_git_stats_daily.id IS '主键ID';
COMMENT ON COLUMN edu_ops_git_stats_daily.platform_project_id IS '平台项目ID (对应 t_project.id)';
COMMENT ON COLUMN edu_ops_git_stats_daily.platform_task_id IS '平台任务ID (对应 t_project_task.id)';
COMMENT ON COLUMN edu_ops_git_stats_daily.gitlab_project_id IS 'GitLab项目ID';
COMMENT ON COLUMN edu_ops_git_stats_daily.stat_date IS '统计日期';
COMMENT ON COLUMN edu_ops_git_stats_daily.committer_email IS '提交者邮箱';
COMMENT ON COLUMN edu_ops_git_stats_daily.commit_count IS '提交次数';
COMMENT ON COLUMN edu_ops_git_stats_daily.lines_added IS '新增代码行数';
COMMENT ON COLUMN edu_ops_git_stats_daily.lines_deleted IS '删除代码行数';
COMMENT ON COLUMN edu_ops_git_stats_daily.create_time IS '记录创建时间';
COMMENT ON COLUMN edu_ops_git_stats_daily.update_time IS '记录更新时间';

-- 索引
CREATE INDEX idx_stats_daily_date ON edu_ops_git_stats_daily(stat_date);
CREATE TABLE edu_ops_git_commit_file_stats (
    id BIGSERIAL PRIMARY KEY,
    gitlab_project_id BIGINT NOT NULL,
    platform_project_id BIGINT NOT NULL,
    platform_task_id BIGINT,
    commit_sha VARCHAR(40) NOT NULL,
    committer_email VARCHAR(255) NOT NULL,
    commit_date TIMESTAMPTZ NOT NULL,
    file_path TEXT NOT NULL,
    file_extension VARCHAR(50),
    lines_added INTEGER NOT NULL,
    lines_deleted INTEGER NOT NULL,
    create_time TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 表和字段注释
COMMENT ON TABLE edu_ops_git_commit_file_stats IS 'Git提交中单个文件的统计信息表';
COMMENT ON COLUMN edu_ops_git_commit_file_stats.id IS '主键ID';
COMMENT ON COLUMN edu_ops_git_commit_file_stats.gitlab_project_id IS 'GitLab项目ID';
COMMENT ON COLUMN edu_ops_git_commit_file_stats.platform_project_id IS '平台项目ID';
COMMENT ON COLUMN edu_ops_git_commit_file_stats.platform_task_id IS '平台任务ID (可能为空)';
COMMENT ON COLUMN edu_ops_git_commit_file_stats.commit_sha IS '提交的SHA值';
COMMENT ON COLUMN edu_ops_git_commit_file_stats.committer_email IS '提交者邮箱';
COMMENT ON COLUMN edu_ops_git_commit_file_stats.commit_date IS '提交日期';
COMMENT ON COLUMN edu_ops_git_commit_file_stats.file_path IS '变更的文件路径';
COMMENT ON COLUMN edu_ops_git_commit_file_stats.file_extension IS '文件后缀名，用于快速按类型统计';
COMMENT ON COLUMN edu_ops_git_commit_file_stats.lines_added IS '新增的代码行数';
COMMENT ON COLUMN edu_ops_git_commit_file_stats.lines_deleted IS '删除的代码行数';
COMMENT ON COLUMN edu_ops_git_commit_file_stats.create_time IS '记录创建时间';

-- 推荐索引，用于加速查询
CREATE INDEX idx_commit_file_stats_query ON edu_ops_git_commit_file_stats (platform_project_id, platform_task_id, commit_date);
CREATE INDEX idx_commit_file_stats_extension ON edu_ops_git_commit_file_stats (file_extension);

ALTER TABLE edu_ops_git_commit_file_stats
    ADD COLUMN is_new_file BOOLEAN NOT NULL DEFAULT FALSE,
    ADD COLUMN is_deleted_file BOOLEAN NOT NULL DEFAULT FALSE,
    ADD COLUMN is_renamed_file BOOLEAN NOT NULL DEFAULT FALSE;

COMMENT ON COLUMN edu_ops_git_commit_file_stats.is_new_file IS '是否为新创建的文件';
COMMENT ON COLUMN edu_ops_git_commit_file_stats.is_deleted_file IS '是否为删除的文件';
COMMENT ON COLUMN edu_ops_git_commit_file_stats.is_renamed_file IS '是否为重命名的文件';