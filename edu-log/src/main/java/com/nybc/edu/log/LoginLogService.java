package com.nybc.edu.log;

import com.nybc.edu.common.page.PageResult;
import com.nybc.edu.common.model.LoginLog;
import com.nybc.edu.log.dto.LoginLogQueryDto;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 类描述：登录日志服务接口 提供登录日志的保存、查询、统计等业务操作。
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
public interface LoginLogService {
    /**
     * 保存一条用户活动日志记录。
     * 此方法为通用保存方法，具体事件类型和状态应在传入的 LoginLog 对象中设置。
     *
     * @param activityLog 待保存的用户活动日志实体
     *                    - eventType: LOGIN, LOGOUT, LOGIN_FAILURE
     *                    - status: SUCCESS, FAILURE (主要用于LOGIN/LOGIN_FAILURE)
     *                    - eventTime: 事件发生时间 (若为null，则设置为当前时间)
     *                    - 其他相关信息如 userId, userName, ipAddress, userAgent, sessionId, failureReason
     * @return 影响行数
     */
    int saveActivityLog(LoginLog activityLog);

    /**
     * 获取指定用户最近一次成功的登录日志记录。
     *
     * @param userId 用户 ID
     * @return 最近一条成功的登录日志实体，如果不存在则返回 null
     */
    LoginLog getLastSuccessfulLogin(Long userId);

    /**
     * 根据用户 ID 统计成功登录的次数。
     *
     * @param userId 用户 ID
     * @return 成功登录次数
     */
    int countSuccessfulLoginsByUserId(Long userId);

    /**
     * 根据条件分页查询用户活动日志列表。
     *
     * @param queryDto 查询条件 DTO (可能需要扩展以支持按 eventType, status 等查询)
     * @return 分页结果 (LoginLog 实体)
     */
    PageResult<LoginLog> queryUserActivityLogs(LoginLogQueryDto queryDto);

    /**
     * 根据 ID 删除用户活动日志记录。
     * (注意：日志删除需谨慎，通常有归档策略)
     *
     * @param logId 日志记录 ID
     * @return 影响行数
     */
    int deleteUserActivityLogById(Long logId);

    /**
     * 查询指定天数内没有成功登录记录的用户ID列表。
     * "没有成功登录记录" 指的是在指定天数内，没有 event_type = 'LOGIN' 且 status = 'SUCCESS' 的记录。
     *
     * @param days 未登录的天数，例如 90
     * @return 用户ID列表
     */
    List<Long> findUsersNotLoggedInForDays(int days);

    /**
     * 查询在指定时间窗口内（例如5分钟）登录失败达到或超过指定次数的用户ID列表。
     *
     * @param minutes          时间窗口（分钟数）
     * @param failureThreshold 失败次数阈值
     * @return 用户ID列表及对应的失败次数 (Map<UserId, FailureCount>)
     */
    Map<Long, Long> findUsersWithFrequentLoginFailures(int minutes, int failureThreshold);

    /**
     * 查询指定时间点之后没有成功登录记录的用户ID列表。
     * "没有成功登录记录" 指的是在指定时间点之后，用户没有 event_type = 'LOGIN' 且 status = 'SUCCESS' 的记录。
     * 此方法现在利用 t_user.last_login_time 字段，因此只需传入时间点。
     *
     * @param sinceTime 查询的时间点，例如 LocalDateTime.now().minusDays(90)
     * @return 用户ID列表
     */
    List<Long> findUsersNotLoggedInForDays(LocalDateTime sinceTime);

    /**
     * 清理指定日期之前的旧登录日志。
     *
     * @param retentionTime 需要保留的最早时间点（此时间点之前的日志将被删除）
     * @return 删除的记录数
     */
    int cleanOldLogs(LocalDateTime retentionTime);

}
