<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nybc.edu.biz.dao.ProjectReviewCriterionMapper">

    <resultMap id="BaseResultMap" type="com.nybc.edu.biz.entity.ProjectReviewCriterion">
        <id column="id" property="id"/>
        <result column="reference_id" property="referenceId"/>
        <result column="reference_type" property="referenceType"/>
        <result column="criterion_id" property="criterionId"/>
        <result column="weight" property="weight"/>
        <result column="order_index" property="orderIndex"/>
        <result column="creator_id" property="creatorId"/>
        <result column="created_at" property="createdAt"/>
        <result column="updater_id" property="updaterId"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, reference_id, reference_type, criterion_id, weight, order_index,
        creator_id, created_at, updater_id, updated_at, tenant_id
    </sql>

    <insert id="insert" parameterType="com.nybc.edu.biz.entity.ProjectReviewCriterion" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO t_project_review_criterion (reference_id, reference_type, criterion_id, weight, order_index,
                                                creator_id, created_at, updater_id, updated_at, tenant_id)
        VALUES (#{referenceId}, #{referenceType}, #{criterionId}, #{weight}, #{orderIndex},
                #{creatorId}, #{createdAt}, #{updaterId}, #{updatedAt}, #{tenantId})
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_project_review_criterion (
        reference_id, reference_type, criterion_id, weight, order_index,
        creator_id, created_at, updater_id, updated_at, tenant_id
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.referenceId}, #{item.referenceType}, #{item.criterionId}, #{item.weight}, #{item.orderIndex},
            #{item.creatorId}, #{item.createdAt}, #{item.updaterId}, #{item.updatedAt}, #{item.tenantId}
            )
        </foreach>
    </insert>

    <update id="updateById" parameterType="com.nybc.edu.biz.entity.ProjectReviewCriterion">
        UPDATE t_project_review_criterion
        <set>
            <if test="weight != null">weight = #{weight},</if>
            <if test="orderIndex != null">order_index = #{orderIndex},</if>
            <if test="updaterId != null">updater_id = #{updaterId},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
            <if test="tenantId != null">tenant_id = #{tenantId}</if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE
        FROM t_project_review_criterion
        WHERE id = #{id}
    </delete>

    <delete id="deleteByReference">
        DELETE
        FROM t_project_review_criterion
        WHERE reference_id = #{referenceId}
          AND reference_type = #{referenceType}
    </delete>

    <delete id="deleteByProjectId" parameterType="java.lang.Long">
        DELETE
        FROM t_project_review_criterion
        WHERE reference_id = #{projectId}
          AND reference_type = 'PROJECT'
    </delete>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_project_review_criterion
        WHERE id = #{id}
    </select>

    <select id="selectByReference" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_project_review_criterion
        WHERE reference_id = #{referenceId} AND reference_type = #{referenceType}
        ORDER BY order_index
    </select>
    <select id="selectByReferenceAndCriterionId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_project_review_criterion
        WHERE reference_id = #{referenceId} AND reference_type = #{referenceType} AND criterion_id = #{criterionId}
    </select>

    <select id="selectByProjectIdAndCriterionId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_project_review_criterion
        WHERE reference_id = #{projectId} AND reference_type = 'PROJECT' AND criterion_id = #{criterionId}
    </select>
</mapper> 