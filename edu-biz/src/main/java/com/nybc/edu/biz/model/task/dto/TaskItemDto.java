package com.nybc.edu.biz.model.task.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import com.nybc.edu.biz.entity.ProjectTask;
import com.nybc.edu.biz.enums.ReviewStatusEnum;
import com.nybc.edu.biz.enums.TaskActionEnum;
import com.nybc.edu.biz.enums.TaskStatusEnum;
import com.nybc.edu.common.model.UserDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

@Data
public class TaskItemDto {
    @Schema(description = "任务ID")
    private Long id;

    @Schema(description = "所属项目ID")
    private Long projectId;

    @Schema(description = "任务标题")
    private String title;

    @Schema(description = "任务分类")
    private String taskType;

    @Schema(description = "任务标签列表")
    private List<String> tags;

    /*    @Schema(description = "简要描述")
        private String description;
        */
    @Schema(description = "负责人列表，展示前3个人")
    private List<UserDto> assignees;
    @JSONField(format = "yyyy-MM-dd")
    @Schema(description = "开始日期")
    private LocalDate startDate;
    @JSONField(format = "yyyy-MM-dd")
    @Schema(description = "截止日期")
    private LocalDate deadline;

    @Schema(description = "任务状态")
    private TaskStatusEnum status;

    @Schema(description = "指导教师")
    private UserDto instructor;

    @Schema(description = "剩余时间(天)")
    private Long remainingTime;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "创建人")
    private UserDto creator;

    @Schema(description = "阶段状态映射")
    private List<PhaseItemStatusDto> phaseItemStatus;

    @Schema(description = "学生评审状态")
    private ReviewStatusEnum studentReviewStatus;

    @Schema(description = "教师评审状态")
    private ReviewStatusEnum teacherReviewStatus;

    @Schema(description = "企业评审状态")
    private ReviewStatusEnum companyReviewStatus;

    @Schema(description = "当前用户可执行的操作")
    private List<TaskActionEnum> currentUserAction;


    public static TaskItemDto coverTaskItemDto(ProjectTask projectTask) {
        TaskItemDto taskItemDto = new TaskItemDto();
        taskItemDto.setId(projectTask.getId());
        taskItemDto.setProjectId(projectTask.getProjectId());
        taskItemDto.setTitle(projectTask.getName());
        taskItemDto.setTaskType(projectTask.getTaskType());
        // TODO: 标签暂时设为null，待实现标签关联查询
        taskItemDto.setTags(null);
        // taskItemDto.setDescription(projectTask.getDescription());
        // 展示前三个
        taskItemDto.setStartDate(projectTask.getStartDate());
        taskItemDto.setDeadline(projectTask.getDeadline());
        taskItemDto.setStatus(projectTask.getStatus());
        taskItemDto.setCreateTime(projectTask.getCreatedAt());
        taskItemDto.setUpdateTime(projectTask.getUpdatedAt());
        // 阶段状态
        // 评审状态
        taskItemDto.setStudentReviewStatus(projectTask.getStudentReviewStatus());
        taskItemDto.setTeacherReviewStatus(projectTask.getTeacherReviewStatus());
        taskItemDto.setCompanyReviewStatus(projectTask.getCompanyReviewStatus());
        return taskItemDto;
    }

}