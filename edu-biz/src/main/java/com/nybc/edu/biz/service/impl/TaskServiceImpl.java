package com.nybc.edu.biz.service.impl;

import com.alibaba.fastjson2.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.nybc.edu.common.page.PageResult;
import com.nybc.edu.common.page.PageUtils;
import com.nybc.edu.biz.dao.*;
import com.nybc.edu.biz.entity.*;
import com.nybc.edu.biz.enums.*;
import com.nybc.edu.biz.model.project.resp.ProjectDetailResponse;
import com.nybc.edu.biz.model.task.dto.*;
import com.nybc.edu.biz.model.task.req.*;
import com.nybc.edu.biz.model.task.resp.*;
import com.nybc.edu.biz.model.template.dto.TemplateAttachmentDto;
import com.nybc.edu.biz.model.template.resp.TaskTemplateDetailResponse;
import com.nybc.edu.biz.service.*;
import com.nybc.edu.common.aop.LogRecord;
import com.nybc.edu.common.enums.EntityTypeEnum;
import com.nybc.edu.common.enums.LogRecordEnum;
import com.nybc.edu.common.enums.NotificationTypeEnum;
import com.nybc.edu.common.enums.ProjectStatusEnum;
import com.nybc.edu.common.event.*;
import com.nybc.edu.common.model.*;
import com.nybc.edu.core.exception.BizException;
import com.nybc.edu.storage.dao.FileMetadataMapper;
import com.nybc.edu.storage.entity.FileMetadata;
import com.nybc.edu.tag.dto.CommonTagDto;
import com.nybc.edu.tag.dto.TagToEntityRequest;
import com.nybc.edu.tag.service.EntityTagRelationService;
import com.nybc.user.context.UserHold;
import com.nybc.user.model.dto.UserListItemDto;
import com.nybc.user.model.dto.UserQueryDto;
import com.nybc.user.service.AuthService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 任务服务实现类
 */
@Slf4j
@Service("taskService")
public class TaskServiceImpl implements TaskService {
    @Resource
    private ProjectPhaseMapper projectPhaseMapper;
    @Resource
    private ProjectTaskMapper projectTaskMapper;

    @Resource
    private TaskTemplateService taskTemplateService;

    @Resource
    private TaskPhaseMapper taskPhaseMapper;

    @Resource
    private TaskTechnicalRequirementMapper taskTechnicalRequirementMapper;

    @Resource
    private TaskAttachmentMapper taskAttachmentMapper;

    @Resource
    private TaskMemberMapper taskMemberMapper;

    @Resource
    private ProjectService projectService;

    @Resource
    private TaskPhaseStatusService taskPhaseStatusService;

    @Resource
    private AuthService authService;

    @Resource
    private ProjectMapper projectMapper;

    @Resource
    private DivisionService divisionService;
    @Resource
    private TaskMemberDivisionMapper taskMemberDivisionMapper;
    @Resource
    private TaskDivisionTypeMapper taskDivisionTypeMapper;
    @Resource
    private TaskPhaseFeedbackMapper taskPhaseFeedbackMapper;
    @Resource
    private TaskPhaseSubmissionMapper taskPhaseSubmissionMapper;
    @Resource
    private ApplicationEventPublisher eventPublisher;
    @Resource
    private TaskReviewService taskReviewService;
    @Resource
    private ProjectMemberMapper projectMemberMapper;
    @Resource
    private ProjectReviewRoleMapper projectReviewRoleMapper;
    @Resource
    private ProjectReviewSettingsMapper projectReviewSettingsMapper;
    @Resource
    private TaskStatusService taskStatusService;
    @Resource
    private PhaseDeliverableMapper phaseDeliverableMapper;
    @Resource
    private FileMetadataMapper fileMetadataMapper;
    @Resource
    private EntityTagRelationService entityTagRelationService;
    @Autowired
    private ProjectMemberGroupMapper projectMemberGroupMapper;
    @Autowired
    private ProjectMemberGroupRelationMapper projectMemberGroupRelationMapper;

    @Override
    public PageResult<TaskItemDto> getTaskList(TaskQuery request) {

        ProjectMember projectMember = projectMemberMapper.selectByProjectIdAndUserId(request.getProjectId(), UserHold.getUserId());
        // 设置当前用户ID用于草稿任务过滤
        request.setUserId(UserHold.getUserId());
        // 过滤草稿任务：如果任务状态是草稿，且当前用户不是老师，则不可见
        PageInfo<ProjectTask> projectTaskPage = filterDraftTasks(request);
        if (CollectionUtils.isEmpty(projectTaskPage.getList())) {
            return PageResult.result();
        }

        ProjectReviewSettings settings = projectReviewSettingsMapper.selectByProjectId(request.getProjectId());

        List<ProjectTask> projectTaskList = projectTaskPage.getList();

        List<Long> taskIds = projectTaskList.stream().map(ProjectTask::getId).toList();
        Map<Long, ReviewStatusEnum> reviewStatusEnumMap = taskReviewService.getTaskReviewStatus(taskIds, settings);

        Map<Long, List<UserDto>> memberByTask = getTaskMemberList(taskIds);
        Map<Long, List<PhaseItemStatusDto>> batchGetPhaseStatus = taskPhaseStatusService.batchGetPhaseStatus(request.getProjectId(), taskIds);
        // 批量获取任务标签
        Map<Long, List<CommonTagDto>> taskTagsMap = entityTagRelationService.batchGetTagsForEntities(taskIds, EntityTypeEnum.TASK);

        List<Long> createIds = projectTaskList.stream().map(ProjectTask::getCreatorId).toList();
        Map<Long, UserDto> createMap = authService.getMapByUserIds(createIds);
        // 当前用户所在的项目
        List<TaskMember> taskMemberList = taskMemberMapper.selectByProjectAndUserId(request.getProjectId(), UserHold.getUserId());
        Map<Long, List<Long>> userListByTaskGroup = taskMemberList.stream()
                .collect(Collectors.groupingBy(TaskMember::getTaskId, Collectors.mapping(TaskMember::getUserId, Collectors.toList())));
        List<TaskItemDto> items = Lists.newArrayList();
        for (ProjectTask projectTask : projectTaskList) {
            TaskItemDto taskItemDto = TaskItemDto.coverTaskItemDto(projectTask);
            // 展示前三个
            taskItemDto.setAssignees(memberByTask.get(projectTask.getId()));
            taskItemDto.setRemainingTime(getRemainingTime(projectTask.getDeadline()));
            taskItemDto.setCreator(createMap.get(projectTask.getCreatorId()));
            taskItemDto.setCreateTime(projectTask.getCreatedAt());
            taskItemDto.setUpdateTime(projectTask.getUpdatedAt());
            // 设置任务标签
            List<CommonTagDto> taskTags = taskTagsMap.get(projectTask.getId());
            if (taskTags != null) {
                List<String> tagNames = taskTags.stream().map(CommonTagDto::getTagName).toList();
                taskItemDto.setTags(tagNames);
            }
            // 阶段状态
            taskItemDto.setPhaseItemStatus(batchGetPhaseStatus.get(projectTask.getId()));
            List<Long> taskMemberId = userListByTaskGroup.get(projectTask.getId());
            Boolean isTaskMember = taskMemberId != null && taskMemberId.contains(UserHold.getUserId());
            // 设置当前用户可执行的操作
            List<TaskActionEnum> actionEnum = determineUserAction(projectTask, batchGetPhaseStatus.get(projectTask.getId()),
                                                                  projectMember, reviewStatusEnumMap.get(projectTask.getId()), isTaskMember);
            taskItemDto.setCurrentUserAction(actionEnum);
            items.add(taskItemDto);
        }
        return PageResult.result(projectTaskPage, items);
    }

    @Override
    public TaskFiltersResponse getTaskFilters(GetTaskFiltersRequest request) {
        return new TaskFiltersResponse();
    }

    @Override
    public TaskStatsResponse getTaskStats(GetTaskStatsRequest request) {
        return new TaskStatsResponse();
    }

    @LogRecord(
            type = LogRecordEnum.TASK_CREATE,
            action = "执行了",
            detail = "", // 留空，让系统自动生成详细友好的描述
            targetId = "{#result.id}",
            parentId = "{#request.projectId}",
            condition = "#result.id != null"
    )
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public CreateTaskResponse createTask(TaskSaveRequest request) {
        log.info("尝试创建任务，项目ID: {}", request.getProjectId());
        // 1. 保存或更新主任务
        ProjectTask task = handleMainTask(request);
        // 2. 处理技术要求
        handleTechnicalRequirements(request.getTechnicalRequirements(), task);
        // 3. 处理标签
        handleTaskTags(request.getTags(), task);
        // 4. 处理附件
        handleAttachments(request.getAttachments(), task, null);
        // 4. 处理任务阶段
        handleTaskPhasesAndDeliverables(task);
        // 5. 处理任务成员
        handleTaskMembers(request.getAssigneeIds(), task);
        CreateTaskResponse createTaskResponse = new CreateTaskResponse();
        createTaskResponse.setId(task.getId());
        ProjectTaskDto projectTaskDto = new ProjectTaskDto();
        projectTaskDto.setProjectId(task.getProjectId());
        projectTaskDto.setTaskId(task.getId());
        Project project = projectMapper.selectById(task.getProjectId());
        projectTaskDto.setProjectName(project.getTitle());
        projectTaskDto.setTaskName(task.getName());
        projectTaskDto.setTeacherId(project.getCreatorId());
        projectTaskDto.setUserList(request.getAssigneeIds());
        GitProjectEvent gitProjectEvent = new GitProjectEvent(this, projectTaskDto);
        if (CollectionUtils.isNotEmpty(request.getAssigneeIds())) {
            String taskName = task.getName();
            String projectName = project.getTitle();
            String subject = String.format("【任务分配】您有新的任务：'%s'。", taskName);
            String contentHtml = String.format("<p>尊敬的用户，您被分配了新的任务：<strong>%s</strong>。</p>" +
                                                       "<p>所属项目：<strong>%s</strong></p>" +
                                                       "<p>任务描述：%s</p>" +
                                                       "<p>截止日期：%s</p>" +
                                                       "<p>请尽快查看并开始处理。</p>",
                                               taskName, projectName, task.getDescription(), task.getDeadline().toString());
            MessageMetadata metadata = new MessageMetadata()
                    .setEntityType("task")
                    .setEntityId(task.getId())
                    .setProjectId(task.getProjectId())
                    .setTaskId(task.getId())
                    .setJumpUrl(String.format("/projects/%d/tasks/%d", task.getProjectId(), task.getId()));
            MessageSendDto messageSendDto = new MessageSendDto()
                    .setSendUserId(UserHold.getUserId())
                    .setRecipientUserIds(request.getAssigneeIds())
                    .setMessageType(NotificationTypeEnum.TASK_ASSIGNED)
                    .setSubject(subject)
                    .setTenantId(task.getTenantId())
                    .setContentHtml(contentHtml)
                    .setMetadata(metadata);

            eventPublisher.publishEvent(new MessageEvent(this, messageSendDto));
            log.info("已为新创建任务ID: {} 发布TASK_ASSIGNED通知给{}位负责人。", task.getId(), request.getAssigneeIds().size());
        }
        eventPublisher.publishEvent(gitProjectEvent);
        log.info("已为任务ID: {} 创建成功。", JSON.toJSONString(createTaskResponse));
        return createTaskResponse;
    }

    @LogRecord(
            type = LogRecordEnum.TASK_CREATE,
            action = "批量创建任务",
            detail = "批量创建任务，数量：{#request.items.size()}，项目ID：{#request.projectId}",
            targetId = "{#result.results.![id].toString()}",
            parentId = "{#request.projectId}",
            condition = "#result.totalCount > 0"
    )
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public BatchCreateTaskResponse batchCreateTask(BatchCreateTaskRequest request) {
        BatchCreateTaskResponse response = new BatchCreateTaskResponse();
        if (CollectionUtils.isEmpty(request.getItems())) {
            return response;
        }
        List<TaskCreationResultDto> results = Lists.newArrayList();
        ProjectDetailResponse projectDetailResponse = projectService.getProjectDetail(request.getProjectId());
        List<BatchTaskItemDto> items = request.getItems();
        for (BatchTaskItemDto item : items) {
            TaskCreationResultDto taskCreationResultDto = new TaskCreationResultDto();

            TaskTemplateDetailResponse templateDetail = taskTemplateService.getTemplateDetail(item.getTemplateId());
            String title = item.getTitle();
            if (StringUtils.isEmpty(title)) {
                title = templateDetail.getName();
            }
            String descriptionHTML = templateDetail.getDescriptionHTML();
            // 技术点
            List<String> technicalRequirements = templateDetail.getTechnicalRequirements();
            // 附件
            List<TemplateAttachmentDto> attachments = templateDetail.getAttachments();
            List<Long> attachmentIds = attachments.stream().map(TemplateAttachmentDto::getId).toList();

            ProjectTask task = createProjectTask(request.getProjectId(), title, descriptionHTML, projectDetailResponse.getStartDate(), projectDetailResponse.getDeadline(), null);
            handleTechnicalRequirements(technicalRequirements, task);
            handleAttachments(attachmentIds, task, null);
            // 4. 处理任务阶段
            handleTaskPhasesAndDeliverables(task);
            // 5. 处理任务成员
            List<Long> assigneeIds = aggregateMembersAndGroups(item.getAssigneeIds(), item.getAssigneGroupIds());
            handleTaskMembers(assigneeIds, task);

            taskCreationResultDto.setId(task.getId());
            taskCreationResultDto.setTitle(title);
            ProjectTaskDto projectTaskDto = new ProjectTaskDto();
            projectTaskDto.setProjectId(task.getProjectId());
            projectTaskDto.setTaskId(task.getId());
            projectTaskDto.setProjectName(projectDetailResponse.getTitle());
            projectTaskDto.setTaskName(task.getName());
            projectTaskDto.setTeacherId(projectDetailResponse.getCreator().getId());
            projectTaskDto.setUserList(assigneeIds);
            projectTaskDto.setTenantId(UserHold.getTenantId());
            GitProjectEvent gitProjectEvent = new GitProjectEvent(this, projectTaskDto);
            if (CollectionUtils.isNotEmpty(assigneeIds)) {
                String currentTaskName = task.getName();
                String projectName = projectDetailResponse.getTitle();
                String subject = String.format("【任务分配】您有新的任务：'%s'。", currentTaskName);
                String contentHtml = String.format("<p>尊敬的用户，您被分配了新的任务：<strong>%s</strong>。</p>" +
                                                           "<p>所属项目：<strong>%s</strong></p>" +
                                                           "<p>任务描述：%s</p>" +
                                                           "<p>截止日期：%s</p>" +
                                                           "<p>请尽快查看并开始处理。</p>",
                                                   currentTaskName, projectName, task.getDescription(), task.getDeadline().toString());
                MessageMetadata metadata = new MessageMetadata()
                        .setEntityType("task")
                        .setEntityId(task.getId())
                        .setProjectId(task.getProjectId())
                        .setTaskId(task.getId())
                        .setJumpUrl(String.format("/projects/%d/tasks/%d", task.getProjectId(), task.getId()));
                MessageSendDto messageSendDto = new MessageSendDto()
                        .setSendUserId(UserHold.getUserId())
                        .setRecipientUserIds(assigneeIds)
                        .setMessageType(NotificationTypeEnum.TASK_ASSIGNED)
                        .setSubject(subject)
                        .setContentHtml(contentHtml)
                        .setMetadata(metadata);
                eventPublisher.publishEvent(new MessageEvent(this, messageSendDto));
                log.info("已为批量创建任务ID: {} 发布TASK_ASSIGNED通知给{}位负责人。", task.getId(), item.getAssigneeIds().size());
            }
            eventPublisher.publishEvent(gitProjectEvent);
            results.add(taskCreationResultDto);
        }
        response.setResults(results);
        response.setTotalCount(results.size());
        return response;
    }

    @Override
    public TaskBasicDto getTaskBasic(Long taskId) {
        ProjectTask projectTask = projectTaskMapper.selectById(taskId);
        if (projectTask == null) {
            throw new BizException("任务不存在");
        }
        Project project = projectMapper.selectById(projectTask.getProjectId());
        List<TaskTechnicalRequirement> technicalRequirements = taskTechnicalRequirementMapper.selectByProjectAndTask(projectTask.getProjectId(), projectTask.getId());
        List<String> technicalRequiremen = technicalRequirements
                .stream()
                .map(TaskTechnicalRequirement::getName)
                .toList();
        TaskBasicDto basicDto = new TaskBasicDto();
        basicDto.setId(projectTask.getId());
        basicDto.setProjectId(projectTask.getProjectId());
        basicDto.setProjectName(project.getTitle());
        basicDto.setTitle(projectTask.getName());
        basicDto.setDescription(projectTask.getDescription());
        basicDto.setStartDate(projectTask.getStartDate());
        basicDto.setDeadline(projectTask.getDeadline());
        basicDto.setStatus(projectTask.getStatus());
        basicDto.setTaskType(projectTask.getTaskType());
        // 获取任务标签
        List<CommonTagDto> taskTags = entityTagRelationService.getTagsForEntity(projectTask.getId(), EntityTypeEnum.TASK);
        if (taskTags != null) {
            basicDto.setTags(taskTags.stream().map(CommonTagDto::getTagName).toList());
        }
        basicDto.setRemainingTime(getRemainingTime(projectTask.getDeadline()));
        basicDto.setCreator(authService.getByUserId(projectTask.getCreatorId()));
        basicDto.setCreateTime(projectTask.getCreatedAt());
        basicDto.setUpdateTime(projectTask.getUpdatedAt());
        basicDto.setTechnicalRequiremen(technicalRequiremen);
        List<TaskAttachment> taskAttachments = taskAttachmentMapper.selectByProjectAndTask(projectTask.getProjectId(), projectTask.getId());
        if (CollectionUtils.isNotEmpty(taskAttachments)) {
            List<FileMetadata> fileMetadataList = fileMetadataMapper.findByIds(taskAttachments.stream().map(TaskAttachment::getFileId).toList());
            Map<Long, FileMetadata> fileMetadataMap = fileMetadataList.stream().collect(Collectors.toMap(FileMetadata::getId, fileMetadata -> fileMetadata));
            List<AttachmentDto> attachments = Lists.newArrayList();
            for (TaskAttachment taskAttachment : taskAttachments) {
                if (fileMetadataMap.get(taskAttachment.getFileId()) == null) {
                    continue;
                }
                AttachmentDto attachmentDto = new AttachmentDto();
                attachmentDto.setId(taskAttachment.getFileId());
                attachmentDto.setName(fileMetadataMap.get(taskAttachment.getFileId()).getFileName());
                attachmentDto.setUrl(fileMetadataMap.get(taskAttachment.getFileId()).getPreviewUrl());
                attachmentDto.setSize(fileMetadataMap.get(taskAttachment.getFileId()).getFileSize());
                attachmentDto.setType(fileMetadataMap.get(taskAttachment.getFileId()).getFileType());
                attachmentDto.setUploadTime(fileMetadataMap.get(taskAttachment.getFileId()).getCreateTime());
                attachments.add(attachmentDto);
            }
            basicDto.setAttachments(attachments);
        }

        // 获取并设置任务成员 (assignees)
        Map<Long, List<UserDto>> taskMembersMap = getTaskMemberList(Collections.singletonList(taskId));
        if (taskMembersMap.containsKey(taskId)) {
            basicDto.setAssignees(taskMembersMap.get(taskId));
        }

        return basicDto;
    }

    @LogRecord(
            type = LogRecordEnum.TASK_UPDATE,
            action = "更新任务",
            detail = "", // 留空，让系统自动生成详细友好的描述
            targetId = "{#request.taskId}",
            parentId = "{@taskService.getTaskBasic(#request.taskId).projectId}",
            enableDiff = true,
            diffBeforeExecutionExpression = "@taskService.getTaskBasic(#request.taskId)",
            diffAfterExecutionExpression = "@taskService.getTaskBasic(#request.taskId)",
            diffIgnoreFields = {"createTime", "updateTime"}
    )
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void updateTaskBasic(TaskUpdateRequest request) {
        ProjectTask task = projectTaskMapper.selectById(request.getTaskId());
        if (task == null) {
            throw new BizException("任务不存在");
        }
        task.setName(request.getName());
        task.setDescription(request.getDescription());
        task.setStartDate(request.getStartDate());
        task.setDeadline(request.getDeadline());
        task.setTaskType(request.getTaskType());
        task.setUpdatedAt(new Date());
        task.setUpdaterId(UserHold.getUserId());
        projectTaskMapper.updateById(task);
        handleTechnicalRequirements(request.getTechnicalRequirements(), task);
        // 2. 处理标签
        handleTaskTags(request.getTags(), task);
        // 3. 处理附件
        handleAttachments(request.getAttachments(), task, request.getDelAchments());
        // 4. 处理任务成员变更
        if (request.getAssigneeIds() != null) {
            handleTaskMembers(request.getAssigneeIds(), task);
        }
    }


    @Override
    public CommentResponse addTaskComment(AddTaskCommentRequest request) {
        return new CommentResponse();
    }

    @LogRecord(
            type = LogRecordEnum.TASK_DELETE,
            action = "删除任务",
            detail = "", // 留空，让系统自动生成详细友好的描述
            targetId = "{#request.taskId}",
            parentId = "{#task.projectId}"
    )
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void deleteTask(DeleteTaskRequest request) {
        // 实现删除任务的逻辑
        ProjectTask task = projectTaskMapper.selectById(request.getTaskId());
        if (task == null) {
            throw new BizException("任务不存在");
        }
        deleteTask(task);

    }


    @LogRecord(
            type = LogRecordEnum.TASK_DELETE,
            action = "批量删除任务",
            detail = "批量删除任务，数量：{#request.taskIds.size()}",
            targetId = "{#request.taskIds.![toString()].toString()}",
            parentId = "{#projectTaskList[0].projectId}"
    )
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void batchDeleteTask(BatchDeleteTaskRequest request) {
        // 实现批量删除任务的逻辑
        if (CollectionUtils.isEmpty(request.getTaskIds())) {
            return;
        }
        List<ProjectTask> projectTaskList = projectTaskMapper.selectByIds(request.getTaskIds());
        if (CollectionUtils.isEmpty(projectTaskList)) {
            throw new BizException("任务不存在");
        }
        for (ProjectTask projectTask : projectTaskList) {
            deleteTask(projectTask);
        }
    }


    @Override
    public List<UserListItemDto> getTaskUser(Long taskId) {
        ProjectTask projectTask = projectTaskMapper.selectById(taskId);
        if (projectTask == null) {
            throw new BizException("任务不存在");
        }
        List<TaskMember> taskMemberList = taskMemberMapper.selectByProjectAndTask(projectTask.getProjectId(), projectTask.getId());
        if (CollectionUtils.isEmpty(taskMemberList)) {
            return List.of();
        }
        List<Long> userIds = taskMemberList.stream().map(TaskMember::getUserId).distinct().toList();
        UserQueryDto userQueryDto = new UserQueryDto();
        userQueryDto.setIds(userIds);
        return authService.getUserList(userQueryDto);
    }

    @LogRecord(
            type = LogRecordEnum.TASK_ASSIGNMENT,
            action = "添加任务成员",
            detail = "添加{#request.userIds.size()}位成员", // 显示添加的成员数量
            targetId = "{#request.taskId}",
            parentId = "{#projectTask.projectId}"
    )
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void addTaskUser(AddTaskUserRequest request) {
        if (CollectionUtils.isEmpty(request.getUserIds())) {
            return;
        }
        ProjectTask projectTask = projectTaskMapper.selectById(request.getTaskId());
        if (projectTask == null) {
            throw new BizException("任务不存在");
        }
        if (!canUpdateTaskUser(projectTask.getProjectId())) {
            throw new BizException("评审已开始，无法修改用户");
        }

        List<Long> userIds = request.getUserIds();
        List<TaskMember> taskMemberList = taskMemberMapper.selectByProjectAndTaskAndUserList(projectTask.getProjectId(), projectTask.getId(), userIds);
        if (CollectionUtils.isNotEmpty(taskMemberList)) {
            List<Long> existUserIds = taskMemberList.stream().map(TaskMember::getUserId).distinct().toList();
            userIds.removeAll(existUserIds);
        }
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        List<TaskMember> insertTaskMember = Lists.newArrayList();
        for (Long userId : userIds) {
            TaskMember taskMember = new TaskMember();
            taskMember.setProjectId(projectTask.getProjectId());
            taskMember.setTaskId(projectTask.getId());
            taskMember.setUserId(userId);
            taskMember.setTenantId(UserHold.getTenantId());
            taskMember.setCreatedAt(new Date());
            taskMember.setUpdatedAt(new Date());
            insertTaskMember.add(taskMember);
        }
        if (CollectionUtils.isNotEmpty(insertTaskMember)) {
            if (taskMemberMapper.batchInsert(insertTaskMember) > 0) {
                TaskMemberAssignmentDto taskMemberAssignmentDto = new TaskMemberAssignmentDto();
                taskMemberAssignmentDto.setPlatformTaskId(projectTask.getId());
                taskMemberAssignmentDto.setPlatformUserIds(userIds);
                eventPublisher.publishEvent(new TaskMemberAssignedEvent(this, taskMemberAssignmentDto));
            }
        }

    }

    @LogRecord(
            type = LogRecordEnum.TASK_ASSIGNMENT,
            action = "移除任务成员",
            detail = "移除{#request.userIds.size()}位成员", // 显示移除的成员数量
            targetId = "{#request.taskId}",
            parentId = "{#projectTask.projectId}"
    )
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void deleteTaskUser(DeleteTaskUserRequest request) {
        if (CollectionUtils.isEmpty(request.getUserIds())) {
            return;
        }
        ProjectTask projectTask = projectTaskMapper.selectById(request.getTaskId());
        if (projectTask == null) {
            throw new BizException("任务不存在");
        }
        if (!canUpdateTaskUser(projectTask.getProjectId())) {
            throw new BizException("评审已开始，无法修改用户");
        }

        taskMemberMapper.deleteByProjectAndTaskAndUserList(projectTask.getProjectId(), projectTask.getId(), request.getUserIds());
        // 删除分工
        divisionService.noticeMemberDivisions(projectTask.getProjectId(), projectTask.getId());
        TaskMemberRemovalDto removalData = new TaskMemberRemovalDto();
        removalData.setPlatformTaskId(projectTask.getId());
        removalData.setPlatformUserIds(request.getUserIds());
        eventPublisher.publishEvent(new TaskMemberRemovedEvent(this, removalData));
    }

    /**
     * 处理主任务信息
     */
    private ProjectTask handleMainTask(TaskSaveRequest request) {
        return createProjectTask(request.getProjectId(), request.getName(), request.getDescription(),
                                 request.getStartDate(), request.getDeadline(), request.getTaskType());
    }

    /**
     * 处理技术要求
     */
    private void handleTechnicalRequirements(List<String> technicalRequirements, ProjectTask task) {
        List<TaskTechnicalRequirement> taskTechnicalRequirements = taskTechnicalRequirementMapper.selectByProjectAndTask(task.getProjectId(), task.getId());
        if (CollectionUtils.isNotEmpty(taskTechnicalRequirements)) {
            // 暂时先删除，增加
            taskTechnicalRequirementMapper.deleteByProjectAndTask(task.getProjectId(), task.getId());
        }
        if (CollectionUtils.isEmpty(technicalRequirements)) {
            return;
        }
        // 创建新的技术要求
        AtomicInteger orderIndex = new AtomicInteger(0);
        List<TaskTechnicalRequirement> requirements = technicalRequirements.stream()
                .map(req -> {
                    TaskTechnicalRequirement requirement = new TaskTechnicalRequirement();
                    requirement.setProjectId(task.getProjectId());
                    requirement.setTaskId(task.getId());
                    requirement.setName(req);
                    requirement.setOrderIndex(orderIndex.getAndDecrement());
                    requirement.setCreatedAt(new Date());
                    requirement.setUpdatedAt(new Date());
                    requirement.setTenantId(UserHold.getTenantId());
                    return requirement;
                }).collect(Collectors.toList());
        taskTechnicalRequirementMapper.batchInsert(requirements);
    }

    /**
     * 处理附件
     */
    private void handleAttachments(List<Long> attachments, ProjectTask task, List<Long> delAchments) {
        if (CollectionUtils.isNotEmpty(delAchments)) {
            taskAttachmentMapper.deleteByProjectAndTask(task.getProjectId(), task.getId(), delAchments);
            FileDeleteEvent fileDeleteEvent = new FileDeleteEvent(this, new FileDeleteDto().setFileIds(delAchments));
            eventPublisher.publishEvent(fileDeleteEvent);
        }
        if (CollectionUtils.isEmpty(attachments)) {
            return;
        }
        List<TaskAttachment> list = taskAttachmentMapper.selectByProjectAndTask(task.getProjectId(), task.getId());
        List<Long> existAttachments = list.stream().map(TaskAttachment::getFileId).toList();
        List<TaskAttachment> taskAttachments = attachments.stream()
                .map(attachment -> {
                    if (existAttachments.contains(attachment)) {
                        return null;
                    }
                    TaskAttachment taskAttachment = new TaskAttachment();
                    taskAttachment.setProjectId(task.getProjectId());
                    taskAttachment.setTaskId(task.getId());
                    taskAttachment.setFileId(attachment);
                    taskAttachment.setCreatedAt(new Date());
                    taskAttachment.setTenantId(UserHold.getTenantId());
                    return taskAttachment;
                }).collect(Collectors.toList());
        taskAttachmentMapper.batchInsert(taskAttachments);
    }

    /**
     * 处理任务阶段和交付物
     */
    private void handleTaskPhasesAndDeliverables(ProjectTask task) {
        List<ProjectPhase> projectPhases = getProjectPhases(task.getProjectId());
        if (CollectionUtils.isEmpty(projectPhases)) {
            log.warn("项目ID: {} 未定义任何阶段，任务ID: {} 将不创建任务阶段和交付物。", task.getProjectId(), task.getId());
            return;
        }

        List<TaskPhase> taskPhasesToInsert = createTaskPhases(task, projectPhases);
        // 先保存任务阶段，获得生成的ID
        if (CollectionUtils.isNotEmpty(taskPhasesToInsert)) {
            taskPhaseMapper.batchInsert(taskPhasesToInsert);
        }

        // 然后基于已保存的任务阶段创建交付物
        List<PhaseDeliverable> phaseDeliverablesToInsert = createPhaseDeliverables(projectPhases, taskPhasesToInsert);
        if (CollectionUtils.isNotEmpty(phaseDeliverablesToInsert)) {
            phaseDeliverableMapper.batchInsert(phaseDeliverablesToInsert);
        }
    }

    /**
     * 获取项目阶段列表
     */
    private List<ProjectPhase> getProjectPhases(Long projectId) {
        return projectPhaseMapper.selectByProjectId(projectId);
    }

    /**
     * 创建任务阶段列表
     */
    private List<TaskPhase> createTaskPhases(ProjectTask task, List<ProjectPhase> projectPhases) {
        List<TaskPhase> taskPhasesToInsert = new ArrayList<>();
        for (ProjectPhase pPhase : projectPhases) {
            TaskPhase taskPhase = buildTaskPhase(task, pPhase);
            taskPhasesToInsert.add(taskPhase);
        }
        return taskPhasesToInsert;
    }

    /**
     * 构建单个任务阶段对象
     */
    private TaskPhase buildTaskPhase(ProjectTask task, ProjectPhase projectPhase) {
        TaskPhase taskPhase = new TaskPhase();
        taskPhase.setProjectId(task.getProjectId());
        taskPhase.setTaskId(task.getId());
        taskPhase.setPhaseId(projectPhase.getId());
        taskPhase.setName(projectPhase.getName());
        taskPhase.setStatus(PhaseStatusEnum.NOT_STARTED);
        taskPhase.setDescription(projectPhase.getDescription());
        taskPhase.setStartDate(projectPhase.getStartDate());
        taskPhase.setEndDate(projectPhase.getEndDate());
        taskPhase.setTenantId(UserHold.getTenantId());
        taskPhase.setCreatedAt(LocalDate.now());
        taskPhase.setUpdatedAt(LocalDate.now());
        return taskPhase;
    }

    /**
     * 创建阶段交付物列表
     */
    private List<PhaseDeliverable> createPhaseDeliverables(List<ProjectPhase> projectPhases, List<TaskPhase> taskPhases) {
        List<PhaseDeliverable> phaseDeliverablesToInsert = new ArrayList<>();

        // 创建项目阶段ID到任务阶段的映射，便于查找
        Map<Long, TaskPhase> phaseIdToTaskPhaseMap = taskPhases.stream()
                .collect(Collectors.toMap(TaskPhase::getPhaseId, taskPhase -> taskPhase));

        for (ProjectPhase projectPhase : projectPhases) {
            List<PhaseDeliverable> projectPhaseDeliverables = phaseDeliverableMapper.selectByPhaseId(projectPhase.getId());
            if (CollectionUtils.isNotEmpty(projectPhaseDeliverables)) {
                TaskPhase correspondingTaskPhase = phaseIdToTaskPhaseMap.get(projectPhase.getId());
                List<PhaseDeliverable> taskDeliverables = projectPhaseDeliverables.stream()
                        .map(pDeliverable -> buildTaskDeliverable(pDeliverable, correspondingTaskPhase))
                        .toList();
                phaseDeliverablesToInsert.addAll(taskDeliverables);
            }
        }
        return phaseDeliverablesToInsert;
    }


    /**
     * 构建单个任务交付物对象
     */
    private PhaseDeliverable buildTaskDeliverable(PhaseDeliverable projectDeliverable, TaskPhase taskPhase) {
        PhaseDeliverable taskDeliverable = new PhaseDeliverable();
        taskDeliverable.setProjectId(taskPhase.getProjectId());
        taskDeliverable.setTaskId(taskPhase.getTaskId());
        taskDeliverable.setPhaseId(taskPhase.getId());
        taskDeliverable.setDeliverableName(projectDeliverable.getDeliverableName());
        taskDeliverable.setDescription(projectDeliverable.getDescription());
        taskDeliverable.setRequired(projectDeliverable.getRequired());
        taskDeliverable.setContentType(projectDeliverable.getContentType());
        taskDeliverable.setCreatorId(UserHold.getUserId());
        taskDeliverable.setCreatedAt(new Date());
        taskDeliverable.setUpdaterId(UserHold.getUserId());
        taskDeliverable.setUpdatedAt(new Date());
        taskDeliverable.setTenantId(UserHold.getTenantId());
        return taskDeliverable;
    }


    /**
     * 聚合成员和组
     */
    private List<Long> aggregateMembersAndGroups(List<Long> assigneeIds, List<Long> assigneeGroupIds) {
        if (CollectionUtils.isEmpty(assigneeIds) && CollectionUtils.isEmpty(assigneeGroupIds)) {
            return Lists.newArrayList();
        }
        List<Long> aggregateMembers = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(assigneeIds)) {
            aggregateMembers.addAll(assigneeIds);
        }
        if (CollectionUtils.isNotEmpty(assigneeGroupIds)) {
            List<ProjectMemberGroupRelation> memberGroupRelationList = projectMemberGroupRelationMapper.selectByGroupIds(assigneeGroupIds);
            if (CollectionUtils.isNotEmpty(memberGroupRelationList)) {
                aggregateMembers.addAll(memberGroupRelationList.stream().map(ProjectMemberGroupRelation::getUserId).toList());
            }
        }
        return aggregateMembers;
    }

    /**
     * 处理任务成员
     */
    private void handleTaskMembers(List<Long> assigneeIds, ProjectTask task) {
        if (!canUpdateTaskUser(task.getProjectId())) {
            throw new BizException("评审已开始，无法修改用户");
        }

        // 获取当前任务的成员列表
        List<TaskMember> currentMembers = taskMemberMapper.selectByProjectAndTask(task.getProjectId(), task.getId());
        List<Long> currentUserIds = currentMembers.stream().map(TaskMember::getUserId).toList();

        // 清空当前成员
        taskMemberMapper.deleteByProjectAndTask(task.getProjectId(), task.getId());

        // 如果有移除的成员，发布移除事件
        if (CollectionUtils.isNotEmpty(currentUserIds)) {
            // 计算被移除的用户
            List<Long> removedUserIds = currentUserIds.stream()
                    .filter(userId -> assigneeIds == null || !assigneeIds.contains(userId))
                    .toList();

            if (CollectionUtils.isNotEmpty(removedUserIds)) {
                TaskMemberRemovalDto removalData = new TaskMemberRemovalDto();
                removalData.setPlatformTaskId(task.getId());
                removalData.setPlatformUserIds(removedUserIds);
                eventPublisher.publishEvent(new TaskMemberRemovedEvent(this, removalData));
                log.info("任务ID: {} 移除成员数量: {}", task.getId(), removedUserIds.size());
            }
        }

        if (CollectionUtils.isEmpty(assigneeIds)) {
            // 通知分工服务
            divisionService.noticeMemberDivisions(task.getProjectId(), task.getId());
            return;
        }

        // 创建新的成员
        List<TaskMember> members = assigneeIds.stream()
                .map(userId -> {
                    TaskMember member = new TaskMember();
                    member.setProjectId(task.getProjectId());
                    member.setTaskId(task.getId());
                    member.setUserId(userId);
                    member.setTenantId(UserHold.getTenantId());
                    member.setCreatedAt(new Date());
                    member.setUpdatedAt(new Date());
                    return member;
                }).collect(Collectors.toList());
        taskMemberMapper.batchInsert(members);

        // 计算新增的用户
        List<Long> addedUserIds = assigneeIds.stream()
                .filter(userId -> !currentUserIds.contains(userId))
                .toList();

        // 如果有新增的成员，发布分配事件
        if (CollectionUtils.isNotEmpty(addedUserIds)) {
            TaskMemberAssignmentDto taskMemberAssignmentDto = new TaskMemberAssignmentDto();
            taskMemberAssignmentDto.setPlatformTaskId(task.getId());
            taskMemberAssignmentDto.setPlatformUserIds(addedUserIds);
            eventPublisher.publishEvent(new TaskMemberAssignedEvent(this, taskMemberAssignmentDto));
            log.info("任务ID: {} 添加成员数量: {}", task.getId(), addedUserIds.size());
        }

        // 通知分工服务
        divisionService.noticeMemberDivisions(task.getProjectId(), task.getId());
    }

    private ProjectTask createProjectTask(
            Long projectId, String title, String descriptionHTML,
            LocalDate startDate, LocalDate deadline, String taskType) {
        ProjectTask task = new ProjectTask();
        task.setProjectId(projectId);
        task.setName(title);
        task.setDescription(descriptionHTML);
        task.setStartDate(startDate);
        task.setDeadline(deadline);
        task.setTaskType(taskType);
        task.setStatus(TaskStatusEnum.DRAFT);
        task.setStudentReviewStatus(ReviewStatusEnum.NOT_STARTED);
        task.setTeacherReviewStatus(ReviewStatusEnum.NOT_STARTED);
        task.setCompanyReviewStatus(ReviewStatusEnum.NOT_STARTED);
        task.setCreatedAt(new Date());
        task.setCreatorId(UserHold.getUserId());
        task.setUpdatedAt(new Date());
        task.setUpdaterId(UserHold.getUserId());
        // 设置租户ID
        task.setTenantId(UserHold.getTenantId());
        projectTaskMapper.insert(task);
        return task;
    }

    /**
     * 计算从当前日期到指定截止日期之间剩余的日历天数。
     * 如果截止日期是今天，返回 0。
     * 如果截止日期是明天，返回 1。
     * 如果截止日期已过，返回负数。
     *
     * @param deadline 截止日期
     * @return 剩余的天数，如果 deadline 为 null 则返回 null
     */
    private Long getRemainingTime(LocalDate deadline) {
        if (deadline == null) {
            return null;
        }
        // 获取当前的日期（不包含时间部分）
        LocalDate now = LocalDate.now();
        // 计算两个 LocalDate 之间的天数
        long remain = ChronoUnit.DAYS.between(now, deadline);
        return remain < 0 ? 0 : remain;
    }

    public Map<Long, List<UserDto>> getTaskMemberList(List<Long> taskIds) {
        if (CollectionUtils.isEmpty(taskIds)) {
            return Maps.newHashMap();
        }
        List<TaskMember> taskMemberList = taskMemberMapper.selectByTaskList(taskIds);
        List<Long> userIds = taskMemberList.stream().map(TaskMember::getUserId).distinct().toList();
        Map<Long, UserDto> userDtoMap = authService.getMapByUserIds(userIds);

        Map<Long, List<UserDto>> result = new HashMap<>();
        for (TaskMember taskMember : taskMemberList) {
            result.computeIfAbsent(taskMember.getTaskId(), k -> new ArrayList<>());
            List<UserDto> userDtoByTask = result.get(taskMember.getTaskId());
            // list最多展示3个
            if (userDtoByTask.size() < 3) {
                userDtoByTask.add(userDtoMap.get(taskMember.getUserId()));
            }
        }
        return result;
    }


    private void deleteTask(ProjectTask projectTask) {
        Long projectId = projectTask.getProjectId();
        Long taskId = projectTask.getId();
        // 删除用户和分工
        taskMemberMapper.deleteByProjectAndTask(projectId, taskId);
        taskMemberDivisionMapper.deleteByProjectAndTask(projectId, taskId);
        taskDivisionTypeMapper.deleteByProjectAndTask(projectId, taskId);
        // 删除阶段信息、作答、反馈
        taskPhaseMapper.deleteByProjectAndTask(projectId, taskId);
        taskPhaseFeedbackMapper.deleteByProjectAndTask(projectId, taskId);
        taskPhaseSubmissionMapper.deleteByProjectAndTask(projectId, taskId);
        // 删除基础信息、附件和知识点
        taskTechnicalRequirementMapper.deleteByProjectAndTask(projectId, taskId);
        List<TaskAttachment> list = taskAttachmentMapper.selectByProjectAndTask(projectId, taskId);
        if (CollectionUtils.isNotEmpty(list)) {
            List<Long> fileIds = list.stream().map(TaskAttachment::getFileId).toList();
            taskAttachmentMapper.deleteByProjectAndTask(projectId, taskId, fileIds);
            FileDeleteEvent fileDeleteEvent = new FileDeleteEvent(this, new FileDeleteDto().setFileIds(fileIds));
            eventPublisher.publishEvent(fileDeleteEvent);
        }
        projectTaskMapper.deleteById(taskId);

        GitProjectStatusEvent gitProjectStatusEvent = new GitProjectStatusEvent(this, new ProjectTaskStatusDto()
                .setProjectId(projectId)
                .setTaskId(taskId)
                .setActive(false));
        eventPublisher.publishEvent(gitProjectStatusEvent);
    }

    @LogRecord(
            type = LogRecordEnum.TASK_PUBLISH,
            action = "发布任务",
            detail = "发布任务数量：{#taskIds.size()}",
            targetId = "{#taskIds.toString()}",
            parentId = "{#projectTaskList[0].projectId}"
    )
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void publishTask(List<Long> taskIds) {

        List<ProjectTask> projectTaskList = projectTaskMapper.selectByIds(taskIds);
        if (CollectionUtils.isEmpty(projectTaskList)) {
            throw new BizException("任务不存在");
        }
        for (ProjectTask projectTask : projectTaskList) {
            // 将任务状态从草稿状态更新为未开始状态（发布后的初始状态）
            if (projectTask.getStatus() != TaskStatusEnum.DRAFT) {
                throw new BizException("只有草稿状态的任务才能发布");
            }
            taskStatusService.publishTask(projectTask.getId());
        }
    }


    @Override
    public PendingTaskDto getPendingTaskList(Long userId) {

        PendingTaskDto pendingTaskDto = new PendingTaskDto();
        // 当前老师参与的任务
        List<Project> projects = projectMapper.findProjectsByUserId(userId);
        if (CollectionUtils.isEmpty(projects)) {
            pendingTaskDto.setPendingGuidanceTasks(Lists.newArrayList());
            pendingTaskDto.setPendingReviewTasks(Lists.newArrayList());
            return pendingTaskDto;
        }
        List<Project> progressProjects = projects.stream()
                .filter(project -> project.getStatus().equals(ProjectStatusEnum.IN_PROGRESS))
                .toList();
        List<Long> progressProjectIds = progressProjects.stream().map(Project::getId).toList();

        pendingTaskDto.setPendingReviewTasks(getPendingReviewList(progressProjectIds));
        pendingTaskDto.setPendingGuidanceTasks(getGuidanceList(progressProjectIds));

        return pendingTaskDto;
    }


    /**
     * 判断当前用户对任务的操作状态
     */
    private List<TaskActionEnum> determineUserAction(
            ProjectTask projectTask, List<PhaseItemStatusDto> phaseItemStatusList,
            ProjectMember projectMember, ReviewStatusEnum reviewStatusEnum,
            Boolean isTaskMember) {
        List<TaskActionEnum> actions = new ArrayList<>();
        actions.add(TaskActionEnum.VIEW);

        if (projectMember == null) {
            return actions;
        }
        TaskStatusEnum taskStatus = projectTask.getStatus();
        ProjectMemberTypeEnum userRole = projectMember.getRole();

        // 如果不是项目成员，只能查看
        if (userRole == ProjectMemberTypeEnum.NOT_MEMBER) {
            return actions;
        }

        if (userRole == ProjectMemberTypeEnum.TEACHER && projectTask.getStatus() == TaskStatusEnum.DRAFT) {
            actions.add(TaskActionEnum.PUBLISH);
            return actions;
        }
        // 1. 任务结束后，展示查看结果
        if (taskStatus == TaskStatusEnum.COMPLETED) {
            actions.add(TaskActionEnum.VIEW_RESULT);
            return actions;
        }
        // 3. 当当前时间是在评审期间，而且当前用户可以评审，显示去评审
        ProjectReviewRole projectReviewRole = projectReviewRoleMapper.selectByProjectIdAndRoleCode(projectTask.getProjectId(), projectMember.getRole());
        if (projectReviewRole != null && projectReviewRole.getEnabled() && canUserReviewTask(reviewStatusEnum)) {
            actions.add(TaskActionEnum.GO_REVIEW);
            return actions;
        }
        // 4. 当阶段是已作答，老师的操作就是去指导
        if (userRole == ProjectMemberTypeEnum.TEACHER && hasAnsweredPhases(phaseItemStatusList)) {
            actions.add(TaskActionEnum.GO_GUIDE);
            return actions;
        }

        // 5. 当有阶段显示进行中，而且是学生用户的时候，就展示去作答
        if (userRole == ProjectMemberTypeEnum.STUDENT && isTaskMember && hasInProgressPhases(phaseItemStatusList)) {
            actions.add(TaskActionEnum.GO_ANSWER);
            return actions;
        }
        // 其他情况默认显示查看
        return actions;
    }

    /**
     * 判断用户是否可以对任务进行评审
     * 直接调用TaskReviewService的getProjectTasks方法来判断
     */
    private boolean canUserReviewTask(ReviewStatusEnum reviewStatusEnum) {
        if (reviewStatusEnum == null) {
            return false;
        }
        // 判断评审状态是否为未评审或评审中
        return reviewStatusEnum.equals(ReviewStatusEnum.PENDING) || reviewStatusEnum.equals(ReviewStatusEnum.IN_PROGRESS);
    }

    /**
     * 判断是否有进行中的阶段
     */
    private boolean hasInProgressPhases(List<PhaseItemStatusDto> phaseItemStatusList) {
        if (CollectionUtils.isEmpty(phaseItemStatusList)) {
            return false;
        }
        return phaseItemStatusList.stream()
                .anyMatch(phase -> phase.getStatus() == PhaseStatusEnum.IN_PROGRESS);
    }


    /**
     * 判断是否有已作答的阶段
     */
    private boolean hasAnsweredPhases(List<PhaseItemStatusDto> phaseItemStatusList) {
        if (CollectionUtils.isEmpty(phaseItemStatusList)) {
            return false;
        }
        return phaseItemStatusList.stream()
                .anyMatch(phase -> phase.getStatus() == PhaseStatusEnum.SUBMITTED);
    }


    /**
     * 获取待评审的任务列表
     *
     * @param reviewProjectIds 评审项目id
     * @return pendingReviewTaskList
     */
    private List<ProjectTask> getPendingReviewList(List<Long> reviewProjectIds) {
        // 查询教师评审是否开启
        List<ProjectReviewRole> projectReviewRoleList = projectReviewRoleMapper.selectByProjectIdsAndRole(reviewProjectIds, ProjectMemberTypeEnum.TEACHER);
        // 获取角色角色启用的projects
        List<Long> teacherEnableList = projectReviewRoleList.stream().filter(ProjectReviewRole::getEnabled).map(ProjectReviewRole::getProjectId).toList();
        if (CollectionUtils.isEmpty(teacherEnableList)) {
            return Lists.newArrayList();
        }
        // 获取教师评审启用的任务
        List<Long> enableRoleProjectIdList = reviewProjectIds.stream().filter(teacherEnableList::contains).toList();
        List<ProjectTask> reviewTasks = projectTaskMapper.selectByProjectIdsAndStatusList(enableRoleProjectIdList, List.of(TaskStatusEnum.REVIEWING));
        if (CollectionUtils.isEmpty(reviewTasks)) {
            return Lists.newArrayList();
        }
        // 当前所有老师可以进行评审的任务
        List<Long> reviewTaskIds = reviewTasks.stream().map(ProjectTask::getId).toList();
        Map<Long, ReviewStatusEnum> taskReviewStatusMap = taskReviewService.getTaskReviewStatus(reviewTaskIds);
        List<ProjectTask> pendingReviewTaskList = Lists.newArrayList();
        for (ProjectTask projectTask : reviewTasks) {
            ReviewStatusEnum taskReviewEnum = taskReviewStatusMap.get(projectTask.getId());
            if (taskReviewEnum == ReviewStatusEnum.PENDING || taskReviewEnum == ReviewStatusEnum.IN_PROGRESS) {
                pendingReviewTaskList.add(projectTask);
            }
        }
        return pendingReviewTaskList;
    }

    private List<ProjectTask> getGuidanceList(List<Long> projects) {
        // 查询对应的task，根据阶段已提交的状态
        List<Long> pendingGuidanceTaskIdList = taskPhaseMapper.selectTaskIdByProjectIdsAndStatus(projects, PhaseStatusEnum.SUBMITTED);
        if (CollectionUtils.isEmpty(pendingGuidanceTaskIdList)) {
            return Lists.newArrayList();
        }
        return projectTaskMapper.selectByIds(pendingGuidanceTaskIdList);
    }

    // 当前task状态是否允许修改用户
    private Boolean canUpdateTaskUser(Long projectId) {
        ProjectReviewSettings settings = projectReviewSettingsMapper.selectByProjectId(projectId);
        // 判断开始区间
        return LocalDate.now().isBefore(settings.getStartDate());
    }

    private PageInfo<ProjectTask> filterDraftTasks(TaskQuery request) {
        PageInfo<ProjectTask> pageInfo = PageUtils.queryPage(request, () -> projectTaskMapper.queryList(request));
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return PageInfo.emptyPageInfo();
        }
        // 草稿任务过滤逻辑已经在XML的queryList中处理
        return pageInfo;
    }

    @Override
    public PageResult<UserListItemDto> getAvailableTaskUsers(GetAvailableTaskUsersRequest request) {
        // 1. 验证任务是否存在
        ProjectTask task = projectTaskMapper.selectById(request.getTaskId());
        if (task == null) {
            throw new BizException("任务不存在");
        }

        // 2. 获取项目中的所有成员（排除老师角色）
        List<ProjectMember> projectMembers = projectMemberMapper.selectByProjectId(task.getProjectId());
        if (CollectionUtils.isEmpty(projectMembers)) {
            return new PageResult<>();
        }

        // 过滤掉老师角色的成员
        List<Long> availableUserIds = projectMembers.stream()
                .filter(member -> member.getRole() != ProjectMemberTypeEnum.TEACHER)
                .map(ProjectMember::getUserId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(availableUserIds)) {
            return new PageResult<>();
        }

        // 3. 获取已经添加到该任务的用户ID列表
        List<TaskMember> taskMembers = taskMemberMapper.selectByProjectAndTask(task.getProjectId(), task.getId());
        List<Long> existingTaskUserIds = taskMembers.stream()
                .map(TaskMember::getUserId)
                .toList();

        // 4. 从可用用户中排除已经添加到任务的用户
        availableUserIds.removeAll(existingTaskUserIds);

        if (CollectionUtils.isEmpty(availableUserIds)) {
            return new PageResult<>();
        }
        // 5. 构建用户查询条件
        UserQueryDto userQueryDto = new UserQueryDto();
        userQueryDto.setIds(availableUserIds);
        userQueryDto.setKeyword(request.getKeyword());
        userQueryDto.setPageNum(request.getPageNum());
        userQueryDto.setPageSize(request.getPageSize());

        // 6. 调用用户服务获取分页用户列表
        return authService.getUserPage(userQueryDto);
    }

    /**
     * 处理任务标签
     */
    private void handleTaskTags(List<String> tags, ProjectTask task) {
        if (CollectionUtils.isEmpty(tags)) {
            // 如果标签为空，移除所有现有标签
            entityTagRelationService.removeAllTagsFromEntity(task.getId(), EntityTypeEnum.TASK, UserHold.getUserId());
            return;
        }

        // 使用setTagsForEntity方法，它会先移除现有标签再添加新标签
        TagToEntityRequest tagRequest = new TagToEntityRequest()
                .setEntityId(task.getId())
                .setEntityType(EntityTypeEnum.TASK)
                .setTagNames(tags)
                .setTagCategory("任务标签") // 设置默认分类
                .setOperatorId(UserHold.getUserId());

        int result = entityTagRelationService.setTagsForEntity(tagRequest);
        if (result != 0) {
            log.warn("为任务ID: {} 设置标签失败，错误码: {}", task.getId(), result);
        }
    }

}