package com.nybc.edu.biz.controller;

import com.nybc.edu.common.page.PageResult;
import com.nybc.edu.biz.model.task.dto.TaskCommentDto;
import com.nybc.edu.biz.model.task.req.AddCommentRequest;
import com.nybc.edu.biz.model.task.req.DeleteCommentRequest;
import com.nybc.edu.biz.model.task.req.GetCommentsRequest;
import com.nybc.edu.biz.model.task.req.UpdateCommentRequest;
import com.nybc.edu.biz.service.TaskCommentService;
import com.nybc.edu.common.model.ResultInfo;
import com.nybc.user.context.UserHold;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 类描述：任务评论API控制器 (TaskCommentController)
 * 提供对任务评论的 RESTful 接口，包括添加、获取、更新和删除评论。
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Tag(name = "任务评论API", description = "提供任务评论的增删改查接口")
@RestController
@RequestMapping("/api/tasks/comments")
public class TaskCommentController {

    @Resource
    private TaskCommentService taskCommentService;

    /**
     * 添加任务评论。
     *
     * @param request 添加评论请求DTO
     * @return 添加后的评论信息 (TaskCommentDto)
     */
    @PreAuthorize("isAuthenticated()")
    @PostMapping("/add")
    @Operation(summary = "添加任务评论", description = "为指定任务添加一条新评论或回复。")
    public ResultInfo<TaskCommentDto> addComment(@Valid @RequestBody AddCommentRequest request) {
        Long currentUserId = UserHold.getUserId();
        return taskCommentService.addComment(request, currentUserId);
    }

    /**
     * 获取任务评论列表。
     *
     * @param request 获取评论列表请求DTO (包含任务ID、关键词、分页信息等)
     * @return 分页的任务评论列表
     */
    @PreAuthorize("isAuthenticated()")
    @PostMapping("/list")
    @Operation(summary = "获取任务评论列表", description = "根据任务ID和关键词获取评论列表，支持分页和全文搜索。")
    public ResultInfo<PageResult<TaskCommentDto>> getComments(@Valid @RequestBody GetCommentsRequest request) {
        return ResultInfo.success(taskCommentService.getComments(request));
    }

    /**
     * 更新任务评论。
     *
     * @param request 更新评论请求DTO
     * @return 操作结果
     */
    @PreAuthorize("isAuthenticated()")
    @PostMapping("/update")
    @Operation(summary = "更新任务评论", description = "更新指定评论的内容。")
    public ResultInfo<Void> updateComment(@Valid @RequestBody UpdateCommentRequest request) {
        Long currentUserId = UserHold.getUserId();
        return taskCommentService.updateComment(request, currentUserId);
    }

    /**
     * 逻辑删除任务评论。
     *
     * @param request 删除评论请求DTO
     * @return 操作结果
     */
    @PreAuthorize("isAuthenticated()")
    @PostMapping("/delete")
    @Operation(summary = "删除任务评论", description = "逻辑删除指定评论及其所有回复。")
    public ResultInfo<Void> deleteComment(@Valid @RequestBody DeleteCommentRequest request) {
        Long currentUserId = UserHold.getUserId();
        return taskCommentService.deleteComment(request, currentUserId);
    }

}
