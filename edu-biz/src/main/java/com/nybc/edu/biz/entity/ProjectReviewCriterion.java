package com.nybc.edu.biz.entity;

import com.nybc.edu.biz.enums.CriterionTypeEnum;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 项目评审项关联
 */
@Data
public class ProjectReviewCriterion implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 引用ID（非系统评审项必填）
     */
    private Long referenceId;

    /**
     * 引用类型（PROJECT-项目，TEMPLATE-模版）
     */
    private CriterionTypeEnum referenceType;

    /**
     * 评审项ID
     */
    private Long criterionId;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 排序索引
     */
    private Integer orderIndex;

    /**
     * 创建者ID
     */
    private Long creatorId;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新者ID
     */
    private Long updaterId;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 租户ID
     */
    private Long tenantId;

}