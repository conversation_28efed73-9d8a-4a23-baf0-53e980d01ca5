package com.nybc.edu.biz.service.impl;

import com.github.pagehelper.PageInfo;
import com.nybc.edu.common.page.PageResult;
import com.nybc.edu.common.page.PageUtils;
import com.nybc.edu.biz.dao.*;
import com.nybc.edu.biz.entity.*;
import com.nybc.edu.biz.enums.CriterionTypeEnum;
import com.nybc.edu.biz.model.project.dto.PhaseDto;
import com.nybc.edu.biz.model.project.dto.ProjectReviewCriterionDto;
import com.nybc.edu.biz.model.project.dto.ReviewSettingsDto;
import com.nybc.edu.biz.model.project.req.CreateProjectRequest;
import com.nybc.edu.biz.model.project.resp.CreateProjectResponse;
import com.nybc.edu.biz.model.project.template.dto.ProjectCriterionTemplateDto;
import com.nybc.edu.biz.model.project.template.dto.ProjectPhaseTemplateDto;
import com.nybc.edu.biz.model.project.template.dto.ProjectReviewTemplateDto;
import com.nybc.edu.biz.model.project.template.dto.ProjectTemplateDto;
import com.nybc.edu.biz.model.project.template.req.*;
import com.nybc.edu.biz.model.project.template.resp.ApplyTemplateToProjectResponse;
import com.nybc.edu.biz.model.project.template.resp.CreateProjectTemplateResponse;
import com.nybc.edu.biz.model.project.template.resp.ProjectTemplateDetailResponse;
import com.nybc.edu.biz.service.ProjectService;
import com.nybc.edu.biz.service.ProjectTemplateService;
import com.nybc.edu.common.enums.EntityTypeEnum;
import com.nybc.edu.common.enums.ProjectTypeEnum;
import com.nybc.edu.core.exception.BizException;
import com.nybc.edu.tag.dto.CommonTagDto;
import com.nybc.edu.tag.dto.TagCreateRequest;
import com.nybc.edu.tag.dto.TagToEntityRequest;
import com.nybc.edu.tag.service.CommonTagService;
import com.nybc.edu.tag.service.EntityTagRelationService;
import com.nybc.user.context.UserHold;
import com.nybc.user.service.AuthService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 项目模板服务实现类
 */
@Slf4j
@Service
public class ProjectTemplateServiceImpl implements ProjectTemplateService {

    @Resource
    private ProjectTemplateMapper projectTemplateMapper;

    @Resource
    private ProjectPhaseTemplateMapper projectPhaseTemplateMapper;

    @Resource
    private ProjectReviewTemplateMapper projectReviewTemplateMapper;

    @Resource
    private ReviewCriterionMapper reviewCriterionMapper;

    @Resource
    private AuthService authService;
    @Resource
    private ProjectReviewCriterionMapper projectReviewCriterionMapper;
    @Resource
    private EntityTagRelationService entityTagRelationService;

    @Resource
    private CommonTagService commonTagService;

    @Resource
    private ProjectService projectService;

    @Override
    public PageResult<ProjectTemplateDto> getTemplateList(ProjectTemplateQuery templateQuery) {
        PageInfo<ProjectTemplate> pageInfo = PageUtils.queryPage(templateQuery, () -> projectTemplateMapper.queryList(templateQuery));
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return PageResult.result();
        }
        List<ProjectTemplate> templates = pageInfo.getList();
        List<ProjectTemplateDto> items = convertTemplatesToDtos(templates);
        return PageResult.result(pageInfo, items);
    }

    @Override
    public ProjectTemplateDetailResponse getTemplateDetail(ProjectTemplateDetailRequest request) {

        ProjectTemplate template = getTemplateById(request.getTemplateId());
        ProjectTemplateDetailResponse response = new ProjectTemplateDetailResponse();
        BeanUtils.copyProperties(template.toDto(), response);
        response.setCreateUser(authService.getByUserId(template.getCreateUserId()));

        // 查询并设置阶段信息
        List<ProjectPhaseTemplate> phases = projectPhaseTemplateMapper.selectByTemplateId(template.getId());
        response.setPhases(phases.stream().map(ProjectPhaseTemplate::toDto).collect(Collectors.toList()));

        // 查询并设置评审信息
        ProjectReviewTemplate review = projectReviewTemplateMapper.selectByTemplateId(template.getId());
        if (review != null) {
            response.setReview(review.toDto());
        }

        // 查询并设置评审指标信息
        List<ProjectReviewCriterion> projectReviewCriterionList = projectReviewCriterionMapper.selectByReference(template.getId(), CriterionTypeEnum.TEMPLATE);
        if (CollectionUtils.isEmpty(projectReviewCriterionList)) {
            return response;
        }

        List<Long> criterionIds = projectReviewCriterionList.stream()
                .map(ProjectReviewCriterion::getCriterionId)
                .toList();
        List<ReviewCriterion> criteria = reviewCriterionMapper.selectByIds(criterionIds);
        Map<Long, ReviewCriterion> criteriaMap = criteria.stream().collect(Collectors.toMap(ReviewCriterion::getId, c -> c));
        List<ProjectCriterionTemplateDto> projectCriterionTemplateDtoList = Lists.newArrayList();
        for (ProjectReviewCriterion projectReviewCriterion : projectReviewCriterionList) {
            ProjectCriterionTemplateDto projectCriterionTemplateDto = new ProjectCriterionTemplateDto();
            projectCriterionTemplateDto.setId(projectReviewCriterion.getCriterionId());
            projectCriterionTemplateDto.setWeight(projectReviewCriterion.getWeight());
            ReviewCriterion reviewCriterion = criteriaMap.get(projectReviewCriterion.getCriterionId());
            if (reviewCriterion != null) {
                projectCriterionTemplateDto.setName(reviewCriterion.getName());
                projectCriterionTemplateDto.setDescription(reviewCriterion.getDescription());
                projectCriterionTemplateDto.setIsSystem(reviewCriterion.getIsSystem());
            }
            projectCriterionTemplateDtoList.add(projectCriterionTemplateDto);
        }
        // 获取模板的所有标签并分离技术栈和普通标签
        List<CommonTagDto> allTags = entityTagRelationService.getTagsForEntity(template.getId(), EntityTypeEnum.PROJECT_TEMPLATE);
        if (CollectionUtils.isNotEmpty(allTags)) {
            // 分离技术栈和普通标签
            List<String> regularTags = allTags.stream()
                    .filter(tag -> !"tech_stack".equals(tag.getTagCategory()))
                    .map(CommonTagDto::getTagName)
                    .collect(Collectors.toList());
            
            List<String> techStackTags = allTags.stream()
                    .filter(tag -> "tech_stack".equals(tag.getTagCategory()))
                    .map(CommonTagDto::getTagName)
                    .collect(Collectors.toList());
            
            response.setTags(regularTags);
            response.setTechStack(techStackTags);
        } else {
            response.setTags(new ArrayList<>());
            response.setTechStack(new ArrayList<>());
        }
        response.setCriteria(projectCriterionTemplateDtoList);
        return response;
    }

    @Override
    @Transactional
    public CreateProjectTemplateResponse createTemplate(CreateProjectTemplateRequest request) {
        // 创建模板主体
        ProjectTemplate template = createTemplateEntity(request);
        projectTemplateMapper.insert(template);
        // 保存关联数据
        saveTemplateRelatedData(template.getId(), request);
        CreateProjectTemplateResponse response = new CreateProjectTemplateResponse();
        response.setTemplateId(String.valueOf(template.getId()));
        return response;
    }

    @Override
    @Transactional
    public void updateTemplate(UpdateProjectTemplateRequest request) {
        Long templateId = Long.valueOf(request.getTemplateId());

        validateTemplateExists(templateId);
        updateTemplateEntity(templateId, request);
        replaceTemplateRelatedData(templateId, request);
    }

    @Override
    @Transactional
    public void deleteTemplate(DeleteProjectTemplateRequest request) {
        Long templateId = Long.valueOf(request.getTemplateId());
        validateTemplateExists(templateId);
        softDeleteTemplate(templateId);
        TagToEntityRequest tagRequest = new TagToEntityRequest();
        tagRequest.setEntityId(templateId);
        tagRequest.setEntityType(EntityTypeEnum.PROJECT_TEMPLATE);
        tagRequest.setTagNames(new ArrayList<>());
        tagRequest.setOperatorId(UserHold.getUserId());
        entityTagRelationService.setTagsForEntity(tagRequest);
    }

    @Override
    @Transactional
    public ApplyTemplateToProjectResponse applyTemplateToProject(ApplyTemplateToProjectRequest request) {
        // 验证参数
        if (request.getTemplateId() == null) {
            throw new BizException("模板ID不能为空");
        }
        if (StringUtils.isBlank(request.getProjectName())) {
            throw new BizException("项目名称不能为空");
        }

        Long templateId = request.getTemplateId();

        // 获取模板详情
        ProjectTemplateDetailRequest detailRequest = new ProjectTemplateDetailRequest();
        detailRequest.setTemplateId(templateId);
        ProjectTemplateDetailResponse templateDetail = getTemplateDetail(detailRequest);

        // 构建创建项目的请求
        CreateProjectRequest createProjectRequest = new CreateProjectRequest();
        createProjectRequest.setTitle(request.getProjectName());
        createProjectRequest.setDescription(request.getProjectDescription());
        createProjectRequest.setType(ProjectTypeEnum.valueOf(templateDetail.getType()));
        createProjectRequest.setCategory(templateDetail.getCategory());
        createProjectRequest.setTemplateId(templateId);

        // 设置日期
        if (StringUtils.isNotBlank(request.getStartDate())) {
            createProjectRequest.setStartDate(LocalDate.parse(request.getStartDate()));
        }
        if (StringUtils.isNotBlank(request.getEndDate())) {
            createProjectRequest.setDeadline(LocalDate.parse(request.getEndDate()));
        }

        // 设置参与者
        if (CollectionUtils.isNotEmpty(request.getParticipantsIds())) {
            createProjectRequest.setParticipantsIds(request.getParticipantsIds().stream()
                                                            .map(Long::valueOf)
                                                            .collect(Collectors.toList()));
        }

        // 设置标签
        if (CollectionUtils.isNotEmpty(templateDetail.getTags())) {
            createProjectRequest.setTags(templateDetail.getTags());
        }

        // 设置技术栈
        if (CollectionUtils.isNotEmpty(templateDetail.getTechStack())) {
            createProjectRequest.setTechStack(templateDetail.getTechStack());
        }

        // 转换模板阶段为项目阶段
        if (CollectionUtils.isNotEmpty(templateDetail.getPhases())) {
            List<PhaseDto> phases = new ArrayList<>();
            for (ProjectPhaseTemplateDto phaseTemplate : templateDetail.getPhases()) {
                PhaseDto phase = new PhaseDto();
                phase.setName(phaseTemplate.getName());
                phase.setDescription(phaseTemplate.getDescription());
                // 注意：PhaseDto没有orderIndex字段，ProjectPhaseTemplateDto使用order字段

                // 暂时不处理交付物转换，等待相关DTO类完善
                phases.add(phase);
            }
            createProjectRequest.setPhases(phases);
        }

        // 转换评审设置
        if (templateDetail.getReview() != null) {
            ReviewSettingsDto review = new ReviewSettingsDto();
            ProjectReviewTemplateDto reviewTemplate = templateDetail.getReview();
            review.setStudentReview(reviewTemplate.getStudentReview());
            review.setStudentReviewWeight(reviewTemplate.getStudentReviewWeight());
            review.setTeacherReview(reviewTemplate.getTeacherReview());
            review.setTeacherReviewWeight(reviewTemplate.getTeacherReviewWeight());
            review.setCompanyReview(reviewTemplate.getCompanyReview());
            review.setCompanyReviewWeight(reviewTemplate.getCompanyReviewWeight());
            // 注意：实际的评审开始和结束日期应该由用户在项目中另外设置
            createProjectRequest.setReview(review);
        }

        // 转换评审标准
        if (CollectionUtils.isNotEmpty(templateDetail.getCriteria())) {
            List<ProjectReviewCriterionDto> criteria = new ArrayList<>();
            for (ProjectCriterionTemplateDto criterionTemplate : templateDetail.getCriteria()) {
                ProjectReviewCriterionDto criterion = new ProjectReviewCriterionDto();
                criterion.setId(criterionTemplate.getId());
                criterion.setCriterionName(criterionTemplate.getName());
                criterion.setWeight(criterionTemplate.getWeight());
                criterion.setIsSystem(criterionTemplate.getIsSystem());
                criteria.add(criterion);
            }
            createProjectRequest.setReviewCriterionDtos(criteria);
        }

        // 创建项目
        CreateProjectResponse projectResponse = projectService.createProject(createProjectRequest);

        // 成功创建项目后，增加模板使用计数
        if (projectResponse != null && projectResponse.getId() != null) {
            try {
                int result = projectTemplateMapper.incrementUsageCount(templateId);
                if (result > 0) {
                    log.info("模板[{}]使用计数增加成功，已应用到项目[{}]", templateId, projectResponse.getId());
                } else {
                    log.warn("模板[{}]使用计数增加失败", templateId);
                }
            } catch (Exception e) {
                log.error("更新模板[{}]使用计数时发生异常: {}", templateId, e.getMessage(), e);
                // 不抛出异常，避免影响项目创建的主流程
            }
        }

        // 构建响应
        ApplyTemplateToProjectResponse response = new ApplyTemplateToProjectResponse();
        response.setProjectId(String.valueOf(projectResponse.getId()));
        return response;
    }

    @Override
    public CreateProjectTemplateResponse createTemplateFromProject(CreateTemplateFromProjectRequest request) {
        // TODO: 实现从项目创建模板的逻辑
        CreateProjectTemplateResponse response = new CreateProjectTemplateResponse();
        response.setTemplateId("generated-template-id");
        return response;
    }

    /**
     * 转换模板列表为DTO列表
     */
    private List<ProjectTemplateDto> convertTemplatesToDtos(List<ProjectTemplate> templates) {
        List<ProjectTemplateDto> templateDtoList = Lists.newArrayList();
        for (ProjectTemplate template : templates) {
            ProjectTemplateDto projectTemplateDto = template.toDto();
            projectTemplateDto.setCreateUser(authService.getByUserId(template.getCreateUserId()));

            // 获取模板的标签信息并分离技术栈和普通标签
            try {
                List<CommonTagDto> allTags = entityTagRelationService.getTagsForEntity(template.getId(), EntityTypeEnum.PROJECT_TEMPLATE);
                if (CollectionUtils.isNotEmpty(allTags)) {
                    // 分离技术栈和普通标签
                    List<String> regularTags = allTags.stream()
                            .filter(tag -> !"tech_stack".equals(tag.getTagCategory()))
                            .map(CommonTagDto::getTagName)
                            .collect(Collectors.toList());
                    
                    List<String> techStackTags = allTags.stream()
                            .filter(tag -> "tech_stack".equals(tag.getTagCategory()))
                            .map(CommonTagDto::getTagName)
                            .collect(Collectors.toList());
                    
                    projectTemplateDto.setTags(regularTags);
                    projectTemplateDto.setTechStack(techStackTags);
                } else {
                    projectTemplateDto.setTags(new ArrayList<>());
                    projectTemplateDto.setTechStack(new ArrayList<>());
                }
            } catch (Exception e) {
                log.warn("获取模板[{}]标签失败: {}", template.getId(), e.getMessage());
                projectTemplateDto.setTags(new ArrayList<>());
                projectTemplateDto.setTechStack(new ArrayList<>());
            }

            templateDtoList.add(projectTemplateDto);
        }

        return templateDtoList;
    }

    /**
     * 根据ID获取模板
     */
    private ProjectTemplate getTemplateById(Long templateId) {
        ProjectTemplate template = projectTemplateMapper.selectById(templateId);
        if (template == null) {
            throw new BizException("模板不存在");
        }
        return template;
    }


    /**
     * 创建模板实体
     */
    private ProjectTemplate createTemplateEntity(CreateProjectTemplateRequest request) {
        return ProjectTemplate.createNew(
                request.getName(),
                request.getDescription(),
                request.getType(),
                request.getCategory(),
                authService.getByUserId(UserHold.getUserId()));
    }

    /**
     * 保存模板关联数据
     */
    private void saveTemplateRelatedData(Long templateId, CreateProjectTemplateRequest request) {
        savePhaseTemplates(templateId, request.getPhases());
        saveReviewTemplate(templateId, request.getReview());
        // 验证评审标准数据
        validateCriterionTemplates(request.getCriteria());
        saveCriterionTemplates(templateId, request.getCriteria());
        // 处理标签和技术栈
        handleTemplateTagsAndTechStack(templateId, request.getTags(), request.getTechStack(), request.getCategory());
    }

    /**
     * 保存阶段模板
     */
    private void savePhaseTemplates(Long templateId, List<ProjectPhaseTemplateDto> phaseDtos) {
        // 获取数据库中现有的阶段
        projectPhaseTemplateMapper.deleteByTemplateId(templateId);
        if (phaseDtos == null) {
            return;
        }
        List<ProjectPhaseTemplate> projectPhaseTemplateList = Lists.newArrayList();
        for (ProjectPhaseTemplateDto phaseDto : phaseDtos) {
            ProjectPhaseTemplate phase = ProjectPhaseTemplate.fromDto(phaseDto, templateId);
            // 设置租户ID
            phase.setTenantId(UserHold.getTenantId());
            projectPhaseTemplateList.add(phase);
        }

        // 批量插入阶段模板
        if (!CollectionUtils.isEmpty(projectPhaseTemplateList)) {
            projectPhaseTemplateMapper.insertBatch(projectPhaseTemplateList);
        }
    }

    /**
     * 保存评审模板
     */
    private void saveReviewTemplate(Long templateId, ProjectReviewTemplateDto reviewDto) {
        if (reviewDto != null) {
            ProjectReviewTemplate projectReviewTemplate = projectReviewTemplateMapper.selectByTemplateId(templateId);
            if (projectReviewTemplate == null) {
                ProjectReviewTemplate review = ProjectReviewTemplate.fromDto(reviewDto, templateId);
                projectReviewTemplateMapper.insert(review);
            } else {
                projectReviewTemplate.setStudentReview(reviewDto.getStudentReview());
                projectReviewTemplate.setStudentReviewWeight(projectReviewTemplate.getStudentReviewWeight());
                projectReviewTemplate.setTeacherReview(reviewDto.getTeacherReview());
                projectReviewTemplate.setTeacherReviewWeight(projectReviewTemplate.getTeacherReviewWeight());
                projectReviewTemplate.setCompanyReview(reviewDto.getCompanyReview());
                projectReviewTemplate.setCompanyReviewWeight(projectReviewTemplate.getCompanyReviewWeight());
                projectReviewTemplate.setReviewDurationDays(reviewDto.getReviewDurationDays());
                projectReviewTemplate.setUpdateTime(new Date());
                projectReviewTemplateMapper.update(projectReviewTemplate);
            }
        }
    }

    /**
     * 保存评审指标模板
     */
    private void saveCriterionTemplates(Long templateId, List<ProjectCriterionTemplateDto> criterionDtos) {
        // 先删除现有的评审指标
        projectReviewCriterionMapper.deleteByReference(templateId, CriterionTypeEnum.TEMPLATE);
        reviewCriterionMapper.deleteByReference(templateId, CriterionTypeEnum.TEMPLATE);
        // 如果没有新的评审标准，直接返回
        if (CollectionUtils.isEmpty(criterionDtos)) {
            return;
        }
        int orderIndex = 0;
        List<ProjectReviewCriterion> projectReviewCriterionList = Lists.newArrayList();
        for (ProjectCriterionTemplateDto criterionDto : criterionDtos) {
            Long criterionId = criterionDto.getId();
            if (criterionDto.getIsSystem() == null || !criterionDto.getIsSystem()) {
                // 新增指标 (只对非系统指标进行创建)
                ReviewCriterion reviewCriterion = createCriterion(templateId, criterionDto);
                if (reviewCriterion == null) {
                    if (criterionDto.getId() == null) {
                        log.warn("尝试关联系统标签但ID为空，跳过: {}", criterionDto.getName());
                        continue;
                    }
                    criterionId = criterionDto.getId();
                } else {
                    criterionId = reviewCriterion.getId();
                }
            } else { // 如果是系统指标，直接使用其ID
                if (criterionDto.getId() == null) {
                    log.warn("尝试关联系统标签但ID为空，跳过: {}", criterionDto.getName());
                    continue;
                }
            }
            ProjectReviewCriterion projectReviewCriterion = new ProjectReviewCriterion();
            projectReviewCriterion.setReferenceId(templateId);
            projectReviewCriterion.setCriterionId(criterionId);
            projectReviewCriterion.setReferenceType(CriterionTypeEnum.TEMPLATE);
            projectReviewCriterion.setOrderIndex(orderIndex++);
            projectReviewCriterion.setWeight(criterionDto.getWeight());
            projectReviewCriterion.setCreatorId(UserHold.getUserId());
            projectReviewCriterion.setUpdaterId(UserHold.getUserId());
            projectReviewCriterion.setCreatedAt(new Date());
            projectReviewCriterion.setUpdatedAt(new Date());
            projectReviewCriterion.setTenantId(UserHold.getTenantId());
            projectReviewCriterionList.add(projectReviewCriterion);
        }

        // 批量插入关联记录
        if (!CollectionUtils.isEmpty(projectReviewCriterionList)) {
            projectReviewCriterionMapper.batchInsert(projectReviewCriterionList);
        }
    }


    private ReviewCriterion createCriterion(Long templateId, ProjectCriterionTemplateDto criterionDto) {
        // 新增指标
        if (criterionDto.getIsSystem()) {
            return null;
        }
        ReviewCriterion criterion = new ReviewCriterion();
        criterion.setName(criterionDto.getName());
        criterion.setDescription(criterionDto.getDescription());
        criterion.setDefaultWeight(criterionDto.getWeight());
        criterion.setIsSystem(false);
        criterion.setReferenceId(templateId);
        criterion.setReferenceType(CriterionTypeEnum.TEMPLATE);
        criterion.setCreatedAt(new Date());
        criterion.setUpdatedAt(new Date());
        criterion.setCreatorId(UserHold.getUserId());
        criterion.setUpdaterId(UserHold.getUserId());
        criterion.setTenantId(UserHold.getTenantId());
        reviewCriterionMapper.insert(criterion);
        return criterion;
    }

    /**
     * 验证模板是否存在
     */
    private void validateTemplateExists(Long templateId) {
        ProjectTemplate template = projectTemplateMapper.selectById(templateId);
        if (template == null) {
            throw new BizException("模板不存在");
        }
    }

    /**
     * 更新模板实体
     */
    private void updateTemplateEntity(Long templateId, UpdateProjectTemplateRequest request) {
        ProjectTemplate template = projectTemplateMapper.selectById(templateId);
        if (template == null) {
            throw new BizException("模板不存在");
        }
        if (StringUtils.isNotBlank(request.getName())) {
            template.setName(request.getName());
        }
        if (request.getCategory() != null) {
            template.setCategory(request.getCategory());
        }
        if (StringUtils.isNotBlank(request.getDescription())) {
            template.setDescription(request.getDescription());
        }
        if (request.getType() != null) {
            template.setType(request.getType());
        }
        template.setUpdateTime(new Date());
        projectTemplateMapper.update(template);
    }

    /**
     * 替换模板关联数据
     */
    private void replaceTemplateRelatedData(Long templateId, UpdateProjectTemplateRequest request) {
        // 1. 阶段更新：先删除所有旧阶段，再插入新阶段
        projectPhaseTemplateMapper.deleteByTemplateId(templateId);
        savePhaseTemplates(templateId, request.getPhases());
        // 2. 评审设置更新：直接覆盖
        saveReviewTemplate(templateId, request.getReview());
        // 3. 评审指标更新：先删除所有旧指标（包括自定义的），再插入新指标
        projectReviewCriterionMapper.deleteByReference(templateId, CriterionTypeEnum.TEMPLATE);
        reviewCriterionMapper.deleteByReference(templateId, CriterionTypeEnum.TEMPLATE);
        validateCriterionTemplates(request.getCriteria());
        saveCriterionTemplates(templateId, request.getCriteria());
        // 4. 处理标签和技术栈更新
        handleTemplateTagsAndTechStack(templateId, request.getTags(), request.getTechStack(), request.getCategory());
    }

    /**
     * 删除模板关联数据
     */
    private void deleteTemplateRelatedData(Long templateId) {
        projectPhaseTemplateMapper.deleteByTemplateId(templateId);
        projectReviewTemplateMapper.deleteByTemplateId(templateId);
        reviewCriterionMapper.deleteByReference(templateId, CriterionTypeEnum.TEMPLATE);
        projectReviewCriterionMapper.deleteByReference(templateId, CriterionTypeEnum.TEMPLATE);
        // 删除标签关联
        handleTemplateTags(templateId, new ArrayList<>(), null);
    }

    /**
     * 软删除模板
     */
    private void softDeleteTemplate(Long templateId) {
        projectTemplateMapper.softDelete(templateId);
    }

    /**
     * 处理项目模板的标签和技术栈数据（创建、更新、删除关联）
     * 同时为项目实体类型创建对应的标签，便于项目创建时复用
     *
     * @param templateId 模板ID
     * @param tags       标签名称列表
     * @param techStack  技术栈名称列表
     * @param category   标签分类
     */
    private void handleTemplateTagsAndTechStack(Long templateId, List<String> tags, List<String> techStack, String category) {
        if (templateId == null) {
            log.warn("尝试为null模板ID处理标签，跳过。");
            return;
        }
        // 1. 先清除模板的所有标签关联
        entityTagRelationService.removeAllTagsFromEntity(templateId, EntityTypeEnum.PROJECT_TEMPLATE, UserHold.getUserId());
        // 2. 为项目模板设置普通标签
        if (CollectionUtils.isNotEmpty(tags)) {
            TagToEntityRequest regularTagRequest = new TagToEntityRequest();
            regularTagRequest.setEntityId(templateId);
            regularTagRequest.setEntityType(EntityTypeEnum.PROJECT_TEMPLATE);
            regularTagRequest.setTagNames(tags);
            regularTagRequest.setTagCategory("project_template");
            regularTagRequest.setOperatorId(UserHold.getUserId());
            regularTagRequest.setTenantId(UserHold.getTenantId());
            entityTagRelationService.addTagsToEntity(regularTagRequest);
        }

        // 3. 为项目模板设置技术栈标签
        if (CollectionUtils.isNotEmpty(techStack)) {
            TagToEntityRequest techStackTagRequest = new TagToEntityRequest();
            techStackTagRequest.setEntityId(templateId);
            techStackTagRequest.setEntityType(EntityTypeEnum.PROJECT);
            techStackTagRequest.setTagNames(techStack);
            techStackTagRequest.setTagCategory("tech_stack");
            techStackTagRequest.setOperatorId(UserHold.getUserId());
            entityTagRelationService.addTagsToEntity(techStackTagRequest);
        }
        // 4. 同步创建项目类型的普通标签
        if (CollectionUtils.isNotEmpty(tags)) {
            createProjectTagsFromTemplate(tags, "project_category");
        }

        // 5. 同步创建项目类型的技术栈标签
        if (CollectionUtils.isNotEmpty(techStack)) {
            createProjectTagsFromTemplate(techStack, "tech_stack");
        }
    }

    /**
     * 从模板创建项目类型的标签
     */
    private void createProjectTagsFromTemplate(List<String> tagNames, String tagCategory) {
        for (String tagName : tagNames) {
            if (StringUtils.isNotBlank(tagName)) {
                // 检查项目类型的标签是否已存在
                List<CommonTagDto> existingTags = commonTagService.searchTagSuggestions(tagName, EntityTypeEnum.PROJECT, 10);
                boolean tagExists = existingTags.stream()
                        .anyMatch(tag -> tagName.equals(tag.getTagName())
                                && EntityTypeEnum.PROJECT.equals(tag.getApplicableEntityType())
                                && tagCategory.equals(tag.getTagCategory()));

                if (!tagExists) {
                    // 创建项目类型的标签
                    TagCreateRequest projectTagRequest = new TagCreateRequest();
                    projectTagRequest.setTagName(tagName);
                    projectTagRequest.setTagCategory(tagCategory);
                    projectTagRequest.setApplicableEntityType(EntityTypeEnum.PROJECT);
                    projectTagRequest.setTagDescription("从项目模板同步创建的" + ("tech_stack".equals(tagCategory) ? "技术栈" : "标签"));
                    projectTagRequest.setIsSystemTag(false);
                    commonTagService.createTag(projectTagRequest, UserHold.getUserId());
                    log.info("为项目实体类型同步创建{}标签: {}", tagCategory, tagName);
                }
            }
        }
    }

    /**
     * 处理项目模板的标签数据（创建、更新、删除关联）
     * 同时为项目实体类型创建对应的标签，便于项目创建时复用
     *
     * @param templateId 模板ID
     * @param tags       标签名称列表
     * @param category   标签分类
     */
    private void handleTemplateTags(Long templateId, List<String> tags, String category) {
        if (templateId == null) {
            log.warn("尝试为null模板ID处理标签，跳过。");
            return;
        }

        // 1. 为项目模板设置标签
        TagToEntityRequest templateTagRequest = new TagToEntityRequest();
        templateTagRequest.setEntityId(templateId);
        templateTagRequest.setEntityType(EntityTypeEnum.PROJECT_TEMPLATE);
        templateTagRequest.setTagNames(tags != null ? tags : new ArrayList<>());
        templateTagRequest.setTagCategory(category);
        templateTagRequest.setOperatorId(UserHold.getUserId());
        entityTagRelationService.setTagsForEntity(templateTagRequest);
        log.info("项目模板ID: {} 的标签已更新。", templateId);

        // 2. 同步创建项目类型的标签，便于项目创建时复用
        if (CollectionUtils.isNotEmpty(tags)) {
            try {
                for (String tagName : tags) {
                    if (StringUtils.isNotBlank(tagName)) {
                        // 检查项目类型的标签是否已存在
                        List<CommonTagDto> existingTags = commonTagService.searchTagSuggestions(tagName, EntityTypeEnum.PROJECT, 10);
                        boolean tagExists = existingTags.stream()
                                .anyMatch(tag -> tagName.equals(tag.getTagName()) && EntityTypeEnum.PROJECT.equals(tag.getApplicableEntityType()));

                        if (!tagExists) {
                            // 创建项目类型的标签
                            TagCreateRequest projectTagRequest = new TagCreateRequest();
                            projectTagRequest.setTagName(tagName);
                            projectTagRequest.setTagCategory("PROJECT"); // 项目标签分类
                            projectTagRequest.setApplicableEntityType(EntityTypeEnum.PROJECT);
                            projectTagRequest.setTagDescription("从项目模板同步创建的标签");
                            projectTagRequest.setIsSystemTag(false);

                            commonTagService.createTag(projectTagRequest, UserHold.getUserId());
                            log.info("为项目实体类型同步创建标签: {}", tagName);
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("同步创建项目标签时发生异常: {}", e.getMessage(), e);
                // 不抛出异常，避免影响模板标签创建的主流程
            }
        }
    }

    /**
     * 验证评审标准数据
     *
     * @param criterionDtos 评审标准DTO列表
     */
    private void validateCriterionTemplates(List<ProjectCriterionTemplateDto> criterionDtos) {
        if (CollectionUtils.isEmpty(criterionDtos)) {
            return;
        }
        // 检查名称是否重复
        Set<String> names = new HashSet<>();
        for (ProjectCriterionTemplateDto dto : criterionDtos) {
            if (dto.getName() == null || dto.getName().trim().isEmpty()) {
                throw new BizException("评审标准名称不能为空");
            }
            if (!names.add(dto.getName().trim())) {
                throw new BizException("评审标准名称不能重复: " + dto.getName());
            }

            if (dto.getWeight() == null || dto.getWeight() < 0 || dto.getWeight() > 100) {
                throw new BizException("评审标准[" + dto.getName() + "]权重必须在0-100之间");
            }
        }

        // 检查权重总和是否合理（可选验证）
        int totalWeight = criterionDtos.stream()
                .mapToInt(ProjectCriterionTemplateDto::getWeight)
                .sum();

        if (totalWeight > 100) {
            throw new BizException("评审标准权重总和不能超过100%，当前总和为: " + totalWeight + "%");
        }
    }

    /**
     * 数据修复方法：修复现有模板的标签分离问题
     * 此方法应该在系统升级后执行一次，处理之前合并存储的标签数据
     * 
     * @return 修复的模板数量
     */
    @Transactional
    public int fixExistingTemplateTagSeparation() {
        log.info("开始修复现有模板的标签分离问题");
        
        try {
            // 获取所有模板
            ProjectTemplateQuery query = new ProjectTemplateQuery();
            List<ProjectTemplate> allTemplates = projectTemplateMapper.queryList(query);
            if (CollectionUtils.isEmpty(allTemplates)) {
                log.info("没有发现需要修复的模板");
                return 0;
            }

            int fixedCount = 0;
            for (ProjectTemplate template : allTemplates) {
                try {
                    // 获取模板的所有标签
                    List<CommonTagDto> allTags = entityTagRelationService.getTagsForEntity(template.getId(), EntityTypeEnum.PROJECT_TEMPLATE);
                    if (CollectionUtils.isEmpty(allTags)) {
                        continue;
                    }

                    // 检查是否存在技术栈标签混合的情况
                    boolean hasTechStackTags = allTags.stream()
                            .anyMatch(tag -> "tech_stack".equals(tag.getTagCategory()));
                    boolean hasRegularTags = allTags.stream()
                            .anyMatch(tag -> !"tech_stack".equals(tag.getTagCategory()));

                    // 如果标签已经正确分离，跳过
                    if (hasTechStackTags && hasRegularTags) {
                        // 已经正确分离
                        continue;
                    }

                    // 如果所有标签都是非tech_stack分类，需要进行智能分离
                    if (!hasTechStackTags && hasRegularTags) {
                        log.info("发现需要修复的模板[{}]，标签: {}", template.getId(), 
                                allTags.stream().map(CommonTagDto::getTagName).collect(Collectors.toList()));

                        // 智能识别技术栈标签
                        List<String> techStackKeywords = Arrays.asList(
                                "Spring Boot", "Spring", "Vue", "React", "Angular", "Node.js", "Java", "Python", 
                                "JavaScript", "TypeScript", "MySQL", "MongoDB", "Redis", "Docker", "Kubernetes",
                                "微服务", "前端", "后端", "数据库", "缓存", "消息队列", "容器化", "框架", "库"
                        );

                        List<String> regularTags = new ArrayList<>();
                        List<String> techStackTags = new ArrayList<>();

                        for (CommonTagDto tag : allTags) {
                            String tagName = tag.getTagName();
                            boolean isTechStack = techStackKeywords.stream()
                                    .anyMatch(keyword -> tagName.contains(keyword) || keyword.contains(tagName));
                            
                            if (isTechStack) {
                                techStackTags.add(tagName);
                            } else {
                                regularTags.add(tagName);
                            }
                        }

                        // 重新设置分离后的标签
                        if (!techStackTags.isEmpty() || !regularTags.isEmpty()) {
                            handleTemplateTagsAndTechStack(template.getId(), regularTags, techStackTags, template.getCategory());
                            fixedCount++;
                            log.info("修复模板[{}]完成，普通标签: {}, 技术栈标签: {}", 
                                    template.getId(), regularTags, techStackTags);
                        }
                    }
                } catch (Exception e) {
                    log.warn("修复模板[{}]标签分离时发生异常: {}", template.getId(), e.getMessage(), e);
                }
            }

            log.info("模板标签分离修复完成，共修复{}个模板", fixedCount);
            return fixedCount;
        } catch (Exception e) {
            log.error("修复模板标签分离过程中发生严重异常", e);
            throw new BizException("数据修复失败: " + e.getMessage());
        }
    }

}