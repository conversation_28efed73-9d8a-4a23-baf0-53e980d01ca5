package com.nybc.edu.biz.model.project.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import com.nybc.edu.common.enums.ProjectStatusEnum;
import com.nybc.edu.common.enums.ProjectTypeEnum;
import com.nybc.edu.common.model.UserDto;
import com.nybc.edu.tag.dto.CommonTagDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 项目列表项DTO
 */
@Data
public class ProjectItemDto {
    @Schema(description = "项目ID")
    private Long id;

    @Schema(description = "项目code")
    private String code;

    @Schema(description = "项目标题")
    private String title;

    @Schema(description = "项目简要描述")
    private String description;

    @Schema(description = "所属课程")
    private String course;

    @Schema(description = "项目类型")
    private ProjectTypeEnum type;

    @Schema(description = "开始日期")
    @JSONField(format = "yyyy-MM-dd")
    private LocalDate startDate;

    @Schema(description = "截止日期")
    @JSONField(format = "yyyy-MM-dd")
    private LocalDate deadline;

    @Schema(description = "参与人数")
    private Integer participants;

    @Schema(description = "项目进度(0-100)")
    private Integer progress;

    @Schema(description = "项目状态")
    private ProjectStatusEnum status;

    @Schema(description = "项目标签(空格分隔)")
    private List<CommonTagDto> tag;

    @Schema(description = "创建者姓名")
    private UserDto creator;

    @Schema(description = "创建时间")
    private Date createTime;

}