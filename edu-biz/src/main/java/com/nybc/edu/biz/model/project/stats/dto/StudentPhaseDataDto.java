package com.nybc.edu.biz.model.project.stats.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 学生的单个阶段数据
 */
@Data
public class StudentPhaseDataDto {

    @Schema(description = "阶段ID")
    private String phaseId;

    @Schema(description = "阶段名称")
    private String phaseName;

    @Schema(description = "成果物数量")
    private Integer deliverableCount;

    @Schema(description = "指导意见数量")
    private Integer guidanceCount;

}