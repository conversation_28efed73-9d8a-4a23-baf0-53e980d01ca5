package com.nybc.edu.biz.util;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.nybc.edu.biz.dao.*;
import com.nybc.edu.biz.entity.*;
import com.nybc.edu.biz.enums.ProjectMemberTypeEnum;
import com.nybc.edu.common.context.SpringUtils;
import com.nybc.edu.common.model.UserDto;
import com.nybc.user.service.AuthService;
import lombok.Getter;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class StatsContext {
    // Getter 方法
    // 基础数据
    @Getter
    private final Long projectId;
    @Getter
    private final List<ProjectTask> tasks;
    @Getter
    private final List<ProjectPhase> phases;
    @Getter
    private final List<ProjectMember> students;
    @Getter
    private final List<TaskPhase> taskPhases;
    @Getter
    private final List<TaskPhaseSubmission> submissions;
    @Getter
    private final List<TaskPhaseFeedback> feedbacks;

    // 映射关系
    private final Map<Long, List<Long>> taskStudentMap; // taskId -> List<studentId>
    private final Map<Long, List<TaskPhase>> taskPhaseMap; // taskId -> List<TaskPhase>
    private final Map<Long, List<TaskPhase>> phaseTaskPhaseMap; // phaseId -> List<TaskPhase>
    private final Map<Long, List<TaskPhaseSubmission>> taskPhaseSubmissionMap; // taskPhaseId -> List<TaskPhaseSubmission>
    private final Map<Long, List<TaskPhaseFeedback>> taskPhaseFeedbackMap; // taskPhaseId -> List<TaskPhaseFeedback>
    private final Map<Long, UserDto> userMap; // userId -> UserDto

    public StatsContext(Long projectId) {
        this.projectId = projectId;
        // 通过 SpringUtils 获取所有需要的 mapper
        ProjectTaskMapper projectTaskMapper = SpringUtils.getBean(ProjectTaskMapper.class);
        ProjectPhaseMapper projectPhaseMapper = SpringUtils.getBean(ProjectPhaseMapper.class);
        ProjectMemberMapper projectMemberMapper = SpringUtils.getBean(ProjectMemberMapper.class);
        TaskPhaseMapper taskPhaseMapper = SpringUtils.getBean(TaskPhaseMapper.class);
        TaskPhaseSubmissionMapper taskPhaseSubmissionMapper = SpringUtils.getBean(TaskPhaseSubmissionMapper.class);
        TaskPhaseFeedbackMapper taskPhaseFeedbackMapper = SpringUtils.getBean(TaskPhaseFeedbackMapper.class);
        TaskMemberMapper taskMemberMapper = SpringUtils.getBean(TaskMemberMapper.class);
        AuthService authService = SpringUtils.getBean(AuthService.class);

        // 一次性加载所有基础数据
        this.tasks = projectTaskMapper.selectByProjectId(projectId);
        this.phases = projectPhaseMapper.selectByProjectId(projectId);
        this.students = projectMemberMapper.selectByMemberType(projectId, ProjectMemberTypeEnum.STUDENT);
        this.taskPhases = taskPhaseMapper.selectByProjectId(projectId);
        this.submissions = taskPhaseSubmissionMapper.selectByProjectId(projectId);
        this.feedbacks = taskPhaseFeedbackMapper.selectByProjectId(projectId);
        // 构建映射关系
        this.taskStudentMap = buildTaskStudentMap(taskMemberMapper);
        this.taskPhaseMap = buildTaskPhaseMap();
        this.phaseTaskPhaseMap = buildPhaseTaskPhaseMap();
        this.taskPhaseSubmissionMap = buildTaskPhaseSubmissionMap();
        this.taskPhaseFeedbackMap = buildTaskPhaseFeedbackMap();
        this.userMap = buildUserMap(authService);

    }

    private Map<Long, List<Long>> buildTaskStudentMap(TaskMemberMapper taskMemberMapper) {
        Map<Long, List<Long>> map = Maps.newHashMap();
        for (ProjectTask task : tasks) {
            List<Long> studentIds = taskMemberMapper.selectUserIdByProjectAndTask(projectId, task.getId());
            map.put(task.getId(), studentIds);
        }
        return map;
    }

    private Map<Long, List<TaskPhase>> buildTaskPhaseMap() {
        return taskPhases.stream()
                .collect(Collectors.groupingBy(TaskPhase::getTaskId));
    }

    private Map<Long, List<TaskPhase>> buildPhaseTaskPhaseMap() {
        return taskPhases.stream()
                .collect(Collectors.groupingBy(TaskPhase::getPhaseId));
    }

    private Map<Long, List<TaskPhaseSubmission>> buildTaskPhaseSubmissionMap() {
        return submissions.stream()
                .collect(Collectors.groupingBy(TaskPhaseSubmission::getTaskPhaseId));
    }

    private Map<Long, List<TaskPhaseFeedback>> buildTaskPhaseFeedbackMap() {
        return feedbacks.stream()
                .collect(Collectors.groupingBy(TaskPhaseFeedback::getTaskPhaseId));
    }

    private Map<Long, UserDto> buildUserMap(AuthService authService) {
        List<Long> userIds = students.stream()
                .map(ProjectMember::getUserId)
                .collect(Collectors.toList());
        return authService.getMapByUserIds(userIds);
    }

    public List<Long> getTaskStudents(Long taskId) {
        return taskStudentMap.getOrDefault(taskId, Lists.newArrayList());
    }

    public List<TaskPhase> getTaskPhases(Long taskId) {
        return taskPhaseMap.getOrDefault(taskId, Lists.newArrayList());
    }

    public List<TaskPhase> getPhaseTaskPhases(Long phaseId) {
        return phaseTaskPhaseMap.getOrDefault(phaseId, Lists.newArrayList());
    }

    public List<TaskPhaseSubmission> getTaskPhaseSubmissions(Long taskPhaseId) {
        return taskPhaseSubmissionMap.getOrDefault(taskPhaseId, Lists.newArrayList());
    }

    public List<TaskPhaseFeedback> getTaskPhaseFeedbacks(Long taskPhaseId) {
        return taskPhaseFeedbackMap.getOrDefault(taskPhaseId, Lists.newArrayList());
    }

    public UserDto getUser(Long userId) {
        return userMap.get(userId);
    }

    public List<UserDto> getUsersByIds(List<Long> userIds) {
        return userIds.stream()
                .map(userMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

}

