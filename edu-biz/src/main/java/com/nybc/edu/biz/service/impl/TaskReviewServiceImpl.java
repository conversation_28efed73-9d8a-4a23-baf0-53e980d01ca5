package com.nybc.edu.biz.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.nybc.edu.biz.dao.*;
import com.nybc.edu.biz.entity.*;
import com.nybc.edu.biz.enums.*;
import com.nybc.edu.biz.model.overview.dto.ReviewCompletionItemDto;
import com.nybc.edu.biz.model.review.*;
import com.nybc.edu.biz.model.review.dto.ReviewCriterionDto;
import com.nybc.edu.biz.model.review.dto.RoleReviewStatsDto;
import com.nybc.edu.biz.service.TaskReviewService;
import com.nybc.edu.biz.service.TaskReviewStatusService;
import com.nybc.edu.biz.util.ScoreCalculationUtil;
import com.nybc.edu.common.aop.LogRecord;
import com.nybc.edu.common.enums.LogRecordEnum;
import com.nybc.edu.common.enums.NotificationTypeEnum;
import com.nybc.edu.common.event.MessageEvent;
import com.nybc.edu.common.model.MessageMetadata;
import com.nybc.edu.common.model.MessageSendDto;
import com.nybc.edu.core.exception.BizException;
import com.nybc.user.context.UserHold;
import com.nybc.user.service.AuthService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 任务评审服务实现类
 */
@Slf4j
@Service
public class TaskReviewServiceImpl implements TaskReviewService {

    @Resource
    private ProjectTaskMapper projectTaskMapper;
    @Resource
    private TaskMemberMapper taskMemberMapper;
    @Resource
    private ProjectMemberMapper projectMemberMapper;
    @Resource
    private ProjectReviewSettingsMapper projectReviewSettingsMapper;
    @Resource
    private ProjectReviewRoleMapper projectReviewRoleMapper;
    @Resource
    private TaskReviewMapper taskReviewMapper;
    @Resource
    private TaskReviewScoreMapper taskReviewScoreMapper;
    @Resource
    private ProjectReviewCriterionMapper projectReviewCriterionMapper;
    @Resource
    private ReviewCriterionMapper reviewCriterionMapper;
    @Resource
    private AuthService authService;
    @Resource
    private TaskReviewStatusService taskReviewStatusService;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Override
    public List<ReviewTaskListItemDTO> getProjectTasks(Long projectId) {

        // 当前用户是否可以评审
        if (isReviewable(projectId)) {
            return Lists.newArrayList();
        }
        // 3. 查询项目下所有任务
        List<ProjectTask> taskList = projectTaskMapper.selectByProjectIdAndStatus(projectId, TaskStatusEnum.REVIEWING);
        if (CollectionUtils.isEmpty(taskList)) {
            return Collections.emptyList();
        }
        List<Long> taskIds = taskList.stream().map(ProjectTask::getId).toList();
        Map<Long, ReviewStatusEnum> reviewStatusEnumMap = getTaskReviewStatus(taskIds);
        // 5. 组装DTO
        List<ReviewTaskListItemDTO> result = new ArrayList<>();
        for (ProjectTask task : taskList) {
            ReviewTaskListItemDTO dto = new ReviewTaskListItemDTO();
            dto.setTaskId(task.getId());
            dto.setTitle(task.getName());
            dto.setTaskNumber("TASK-" + task.getId()); // 如有编号字段可替换
            // 当前人对于当前任务的评审状态
            dto.setReviewStatus(reviewStatusEnumMap.get(task.getId()));
            dto.setProjectId(projectId);
            result.add(dto);
        }
        return result;
    }


    @Override
    public Map<Long, ReviewStatusEnum> getTaskReviewStatus(List<Long> taskIdList, ProjectReviewSettings settings) {
        if (CollectionUtils.isEmpty(taskIdList)) {
            return Maps.newHashMap();
        }
        Map<Long, ReviewStatusEnum> reviewStatusEnumMap = Maps.newHashMap();
        if (LocalDate.now().isBefore(settings.getStartDate())) {
            return taskIdList.stream()
                    .collect(Collectors.toMap(taskId -> taskId, taskId -> ReviewStatusEnum.NOT_STARTED));
        }
        if (LocalDate.now().isAfter(settings.getEndDate())) {
            return taskIdList.stream()
                    .collect(Collectors.toMap(taskId -> taskId, taskId -> ReviewStatusEnum.COMPLETED));
        }
        return getTaskReviewStatus(taskIdList);
    }

    /**
     * 获取当前用户针对该任务的评审状态
     *
     * @Param taskIdList 可进行评审的任务id列表
     */
    @Override
    public Map<Long, ReviewStatusEnum> getTaskReviewStatus(List<Long> taskIdList) {
        Long userId = UserHold.getUserId();
        if (CollectionUtils.isEmpty(taskIdList)) {
            return Maps.newHashMap();
        }
        // 获取当前用户每个任务需要评审的数量，根据任务用户数和任务
        Map<Long, Integer> reviewMemberCountsGroupByTaskId = getReviewCountsGroupByTaskId(taskIdList, userId);
        if (reviewMemberCountsGroupByTaskId.isEmpty()) {
            return Maps.newHashMap();
        }
        // 获取当前用户每个任务已经评审的数量
        List<TaskReview> taskReviewList = taskReviewMapper.selectCountByTaskIdAndReviewId(reviewMemberCountsGroupByTaskId.keySet(), userId);
        // 根据taskId聚合
        Map<Long, List<TaskReview>> taskReviewListGroupByTaskId = taskReviewList.stream().collect(Collectors.groupingBy(TaskReview::getTaskId));
        Map<Long, ReviewStatusEnum> taskReviewStatusMap = new HashMap<>();
        for (Map.Entry<Long, Integer> entry : reviewMemberCountsGroupByTaskId.entrySet()) {
            Long taskId = entry.getKey();
            Integer expectReviewCount = entry.getValue();
            List<TaskReview> taskReviewListByTaskId = taskReviewListGroupByTaskId.get(taskId);
            Integer actualReviewCount = (taskReviewListByTaskId == null) ? 0 : taskReviewListByTaskId.size();
            taskReviewStatusMap.put(taskId, getTaskReviewIngStatus(expectReviewCount, actualReviewCount));
        }
        return taskReviewStatusMap;


    }


    @Override
    public TaskReviewDetailDTO getTaskReviewDetail(GetTaskReviewDetailRequest request) {
        // 1. 校验权限
        checkPermission(request.getProjectId());
        // 2. 查询任务基本信息
        ProjectTask task = getTaskBasicInfo(request.getTaskId());
        // 3. 查询评审项和标准
        List<ReviewCriterionDto> projectReviewCriteriaList = getProjectReviewCriteriaList(request.getProjectId());
        // 4. 查询团队评审信息
        List<ReviewCriteriaScoreDTO> groupReviewCriteria = getGroupReviewCriteria(request.getTaskId(), projectReviewCriteriaList);
        // 5. 查询成员评审信息
        List<MemberReviewDTO> memberReviewList = getMemberReviewList(task, projectReviewCriteriaList);

        String groupOverallComment = getGroupOverallComment(request);
        // 6. 组装DTO
        TaskReviewDetailDTO dto = new TaskReviewDetailDTO();
        dto.setId(task.getId());
        dto.setTitle(task.getName());
        dto.setTaskNumber("TASK-" + task.getId());
        dto.setStatus(task.getStatus());
        dto.setGroupReviewCriteria(groupReviewCriteria);
        dto.setGroupOverallComment(groupOverallComment);
        dto.setMembers(memberReviewList);
        return dto;
    }

    /**
     * 校验当前用户是否有权限查看该项目的评审详情
     *
     * @param projectId 项目ID
     * @throws BizException 无权限时抛出异常
     */
    private void checkPermission(Long projectId) {
        if (isReviewable(projectId)) {
            throw new BizException("无权限查看该任务评审详情");
        }
    }

    /**
     * 获取任务的基本信息，并校验任务是否属于当前项目
     */
    private ProjectTask getTaskBasicInfo(Long taskId) {
        ProjectTask task = projectTaskMapper.selectById(taskId);
        if (task == null) {
            throw new BizException("任务不存在");
        }
        return task;
    }

    /**
     * 获取项目下所有评审项及其标准，组装为Map
     *
     * @param projectId 项目ID
     * @return Map<评审项ID, 评审项实体>
     */
    private List<ReviewCriterionDto> getProjectReviewCriteriaList(Long projectId) {
        // 项目关联
        List<ProjectReviewCriterion> projectReviewCriterionList = projectReviewCriterionMapper.selectByReference(projectId, CriterionTypeEnum.PROJECT);
        List<Long> reviewCriterionIds = projectReviewCriterionList.stream().map(ProjectReviewCriterion::getCriterionId).toList();
        if (reviewCriterionIds.isEmpty()) {
            return Lists.newArrayList();
        }
        // 标准配置
        List<ReviewCriterion> reviewCriterionList = reviewCriterionMapper.selectByIds(reviewCriterionIds);
        Map<Long, ReviewCriterion> reviewCriterionMap = reviewCriterionList.stream().collect(Collectors.toMap(ReviewCriterion::getId, rc -> rc));

        List<ReviewCriterionDto> ReviewCriterionDtoList = Lists.newArrayList();
        for (ProjectReviewCriterion criterion : projectReviewCriterionList) {
            ReviewCriterionDto reviewCriterionDto = new ReviewCriterionDto();
            ReviewCriterion reviewCriterion = reviewCriterionMap.get(criterion.getCriterionId());
            if (reviewCriterion == null) {
                continue;
            }
            reviewCriterionDto.setId(reviewCriterion.getId());
            reviewCriterionDto.setIsSystem(reviewCriterion.getIsSystem());
            reviewCriterionDto.setLabel(reviewCriterion.getName());
            reviewCriterionDto.setDescription(reviewCriterion.getDescription());
            reviewCriterionDto.setName(reviewCriterion.getName());
            reviewCriterionDto.setWeight(criterion.getWeight());
            ReviewCriterionDtoList.add(reviewCriterionDto);
        }
        return ReviewCriterionDtoList;
    }

    /**
     * 查询团队评审维度与分数，组装为DTO列表
     *
     * @param taskId                    任务id
     * @param projectReviewCriteriaList 评审项lsit
     * @return 团队评审维度DTO列表
     */
    private List<ReviewCriteriaScoreDTO> getGroupReviewCriteria(Long taskId, List<ReviewCriterionDto> projectReviewCriteriaList) {
        // 查询团队评审记录（假设团队评审reviewType为GROUP）
        TaskReview groupReview = taskReviewMapper.selectGroupByTaskIdAndReviewId(taskId, UserHold.getUserId());
        if (groupReview == null) {
            return wrapReviewCriteriaScore(projectReviewCriteriaList, Lists.newArrayList());
        }
        List<TaskReviewScore> groupScores = taskReviewScoreMapper.selectByTaskReviewId(groupReview.getId());
        return wrapReviewCriteriaScore(projectReviewCriteriaList, groupScores);
    }

    /**
     * 组装评审项和评分
     *
     * @param reviewCriterionDtoList 评审项List
     * @param taskReviewScoreList    评审分数List
     * @return 评审项和评分DTO列表
     */
    private List<ReviewCriteriaScoreDTO> wrapReviewCriteriaScore(List<ReviewCriterionDto> reviewCriterionDtoList, List<TaskReviewScore> taskReviewScoreList) {
        List<ReviewCriteriaScoreDTO> result = new ArrayList<>();

        Map<Long, TaskReviewScore> taskReviewScoreMap = taskReviewScoreList.stream().collect(Collectors.toMap(TaskReviewScore::getCriterionId, rc -> rc));

        for (ReviewCriterionDto criterionDto : reviewCriterionDtoList) {
            ReviewCriteriaScoreDTO dto = new ReviewCriteriaScoreDTO();
            dto.setId(criterionDto.getId());
            dto.setLabel(criterionDto.getName());
            dto.setDescription(criterionDto.getDescription());
            dto.setLabel(criterionDto.getLabel());
            TaskReviewScore score = taskReviewScoreMap.get(criterionDto.getId());
            if (score != null) {
                dto.setScore(score.getScore());
            }
            result.add(dto);
        }
        return result;
    }

    /**
     * 查询团队总体评语
     *
     * @param request 评审详情请求
     * @return 团队总体评语
     */
    private String getGroupOverallComment(GetTaskReviewDetailRequest request) {
        // 查询团队评审记录
        TaskReview groupReview = taskReviewMapper.selectGroupByTaskIdAndReviewId(request.getTaskId(), UserHold.getUserId());
        if (groupReview == null) {
            return null;
        }
        return groupReview.getComment();
    }

    /**
     * 查询所有成员的评审信息，组装为DTO列表
     *
     * @param projectReviewCriteriaList 评审项List
     * @return 成员评审DTO列表
     */
    private List<MemberReviewDTO> getMemberReviewList(ProjectTask task, List<ReviewCriterionDto> projectReviewCriteriaList) {

        List<ProjectMember> revieeMembers = getReviewMembers(task.getProjectId(), task.getId(), UserHold.getUserId());
        Map<Long, ProjectMember> projectMemberMap = revieeMembers.stream().collect(Collectors.toMap(ProjectMember::getUserId, pm -> pm));

        List<MemberReviewDTO> result = Lists.newArrayList();

        List<TaskReview> individualReviewList = taskReviewMapper.selectIndividualByTaskIdAndReviewId(task.getId(), UserHold.getUserId());
        if (CollectionUtils.isEmpty(individualReviewList)) {
            // 没有任何评审记录，填充null标准
            revieeMembers
                    .forEach(taskMember -> {
                        MemberReviewDTO memberReviewDTO = new MemberReviewDTO();
                        memberReviewDTO.setUser(authService.getByUserId(taskMember.getUserId()));
                        memberReviewDTO.setReviewCriteria(wrapReviewCriteriaScore(projectReviewCriteriaList, Lists.newArrayList()));
                        ProjectMember projectMember = projectMemberMap.get(taskMember.getUserId());
                        memberReviewDTO.setProjectRole(projectMember == null ? null : projectMember.getRole());
                        result.add(memberReviewDTO);
                    });
            return result;
        }
        // 被评审人，对齐taskMember任务用户
        Map<Long, TaskReview> reviewMapByRevieweeId = individualReviewList.stream().collect(Collectors.toMap(TaskReview::getRevieweeId, r -> r));

        // 查询当前任务下所有的个人评分记录
        List<Long> individualReviewIds = individualReviewList.stream().map(TaskReview::getId).toList();
        List<TaskReviewScore> individualScoreList = taskReviewScoreMapper.selectByTaskReviewIds(individualReviewIds);

        Map<Long, List<TaskReviewScore>> individualScoreMap = individualScoreList.stream().collect(Collectors.groupingBy(TaskReviewScore::getTaskReviewId));

        // 当前评审人的评审记录的被评审记录，key是被评审人
        for (ProjectMember taskMember : revieeMembers) {
            if (taskMember.getUserId().equals(UserHold.getUserId())) {
                continue;
            }
            MemberReviewDTO memberReviewDTO = new MemberReviewDTO();
            memberReviewDTO.setUser(authService.getByUserId(taskMember.getUserId()));
            ProjectMember projectMember = projectMemberMap.get(taskMember.getUserId());
            memberReviewDTO.setProjectRole(projectMember == null ? null : projectMember.getRole());

            TaskReview taskReview = reviewMapByRevieweeId.get(taskMember.getUserId());
            if (taskReview == null) {
                memberReviewDTO.setReviewCriteria(wrapReviewCriteriaScore(projectReviewCriteriaList, Lists.newArrayList()));
                result.add(memberReviewDTO);
            } else {
                List<TaskReviewScore> individualScores = individualScoreMap.get(taskReview.getId());
                memberReviewDTO.setReviewCriteria(wrapReviewCriteriaScore(projectReviewCriteriaList, individualScores));
                memberReviewDTO.setIndividualComment(taskReview.getComment());
            }
            result.add(memberReviewDTO);
        }
        return result;
    }


    @LogRecord(
            type = LogRecordEnum.TASK_REVIEW,
            action = "提交评审",
            detail = "提交任务评审，任务ID：#{#request.taskId}，项目ID：#{#request.projectId}",
            targetId = "#request.taskId",
            parentId = "#request.projectId"
    )
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public SubmitReviewResponse submitReview(SubmitReviewRequest request) {
        Long projectId = request.getProjectId();
        Long taskId = request.getTaskId();

        // 1. 校验权限
        checkPermission(projectId);

        ProjectMember reviewMember = projectMemberMapper.selectByProjectIdAndUserId(projectId, UserHold.getUserId());

        // 2. 保存团队评审
        if (CollectionUtils.isNotEmpty(request.getGroupReviewCriteria())) {
            TaskReview review = saveGroupTaskReview(projectId, request.getTaskId(), request.getGroupOverallComment(), reviewMember);
            saveGroupReviewScore(review, request.getGroupReviewCriteria());
        }

        // 3. 保存成员评审
        if (CollectionUtils.isNotEmpty(request.getMembers())) {
            List<TaskReview> taskReviewList = taskReviewMapper.selectIndividualByTaskIdAndReviewId(taskId, reviewMember.getUserId());
            Map<Long, TaskReview> taskReviewMap = taskReviewList.stream().collect(Collectors.toMap(TaskReview::getRevieweeId, r -> r));
            for (SubmitReviewRequest.MemberReviewInfo memberReq : request.getMembers()) {
                TaskReview review = saveIndividualTaskReview(taskReviewMap.get(memberReq.getId()), projectId, taskId, memberReq, reviewMember);
                saveIndividualReviewScore(review, memberReq.getReviewCriteria());
            }
        }

        // 4. 提交当前评审人的评审后，更新任务的评审状态，并判断是否整体完成
        ProjectTask currentTask = projectTaskMapper.selectById(taskId);
        ProjectReviewSettings projectReviewSettings = projectReviewSettingsMapper.selectByProjectId(projectId);
        // 确保任务的评审状态字段被更新
        taskReviewStatusService.checkAndUpdateReviewStatus(currentTask, projectReviewSettings);
        // 重新从数据库加载任务，确保拿到最新的评审状态（可能在 checkAndUpdateReviewStatus 中已更新）
        currentTask = projectTaskMapper.selectById(taskId);

        // 5. 判断是否所有必要的评审都已完成
        boolean allReviewsCompleted = checkAllRequiredReviewsCompleted(currentTask, projectReviewSettings);
        if (allReviewsCompleted) {
            sendTaskReviewCompletedNotification(currentTask);
        }
        // 6. 返回响应
        SubmitReviewResponse response = new SubmitReviewResponse();
        response.setSuccess(true);
        response.setTaskStatus(currentTask != null ? currentTask.getStatus() : null);
        return response;
    }


    @Override
    public List<ReviewCompletionItemDto> calcReviewByRole(Long projectId, List<ReviewTypeEnum> reviewTypeEnums) {
        if (CollectionUtils.isEmpty(reviewTypeEnums)) {
            reviewTypeEnums = Arrays.asList(ReviewTypeEnum.GROUP, ReviewTypeEnum.INDIVIDUAL);
        }
        // 1. 获取项目评审角色配置
        Map<ProjectMemberTypeEnum, ProjectReviewRole> roleConfigMap = getProjectReviewRoleConfigMap(projectId);

        // 2. 获取项目评审标准配置
        Map<Long, ProjectReviewCriterion> criterionConfigMap = getProjectReviewCriterionConfigMap(projectId);
        if (criterionConfigMap.isEmpty()) {
            return createEmptyRoleCompletionItems(roleConfigMap);
        }

        // 3. 获取项目所有评审记录（按类型过滤）
        List<TaskReview> allTaskReviews = taskReviewMapper.selectByProjectAndReviewTypes(projectId, reviewTypeEnums);

        // 4. 获取评审分数映射
        Map<Long, List<TaskReviewScore>> taskReviewScoreMap = getTaskReviewScoreMapByReviews(allTaskReviews);

        List<Long> taskIds = projectTaskMapper.selectTaskIdByProjectIdAndStatusList(projectId, TaskStatusEnum.getNotDraftStatus());
        Map<Long, List<TaskMember>> taskMemberByTaskIds = getTaskMember(taskIds);
        List<ProjectMember> projectMemberList = projectMemberMapper.selectByProjectId(projectId);

        // 5. 计算各角色的统计信息
        List<ReviewCompletionItemDto> result = Lists.newArrayList();
        for (ProjectMemberTypeEnum roleType : ProjectMemberTypeEnum.values()) {
            if (isReviewable(roleConfigMap.get(roleType))) {
                result.add(ReviewCompletionItemDto.from(roleType));
                continue;
            }
            List<ProjectMember> projectMemberByRole = projectMemberList.stream()
                    .filter(p -> p.getRole().equals(roleType)).toList();
            // 3. 获取所有评审记录（筛选该角色的评审记录）
            List<TaskReview> roleReviews = allTaskReviews.stream()
                    .filter(review -> roleType.equals(review.getReviewRoleCode()))
                    .toList();

            ReviewCompletionItemDto roleCompletion = calculateRoleCompletion(
                    projectId, roleType, roleReviews, projectMemberByRole, taskReviewScoreMap, criterionConfigMap.keySet(), reviewTypeEnums, taskMemberByTaskIds);
            result.add(roleCompletion);
        }

        return result;
    }

    /**
     * 获取项目评审角色配置映射
     */
    private Map<ProjectMemberTypeEnum, ProjectReviewRole> getProjectReviewRoleConfigMap(Long projectId) {
        List<ProjectReviewRole> roleList = projectReviewRoleMapper.selectByProjectId(projectId);
        return roleList.stream().collect(Collectors.toMap(ProjectReviewRole::getReviewRoleCode, r -> r));
    }

    /**
     * 获取项目评审标准配置映射
     */
    private Map<Long, ProjectReviewCriterion> getProjectReviewCriterionConfigMap(Long projectId) {
        List<ProjectReviewCriterion> criterionList = projectReviewCriterionMapper.selectByReference(projectId, CriterionTypeEnum.PROJECT);
        return criterionList.stream().collect(Collectors.toMap(ProjectReviewCriterion::getCriterionId, c -> c));
    }

    /**
     * 根据评审记录获取评审分数映射
     */
    private Map<Long, List<TaskReviewScore>> getTaskReviewScoreMapByReviews(List<TaskReview> taskReviews) {
        if (CollectionUtils.isEmpty(taskReviews)) {
            return Maps.newHashMap();
        }
        List<Long> reviewIds = taskReviews.stream().map(TaskReview::getId).collect(Collectors.toList());
        List<TaskReviewScore> scores = taskReviewScoreMapper.selectByTaskReviewIds(reviewIds);
        return scores.stream().collect(Collectors.groupingBy(TaskReviewScore::getTaskReviewId));
    }

    /**
     * 创建空的角色完成度项列表
     */
    private List<ReviewCompletionItemDto> createEmptyRoleCompletionItems(Map<ProjectMemberTypeEnum, ProjectReviewRole> roleConfigMap) {
        List<ReviewCompletionItemDto> result = Lists.newArrayList();
        for (ProjectMemberTypeEnum roleType : ProjectMemberTypeEnum.values()) {
            ProjectReviewRole roleConfig = roleConfigMap.get(roleType);
            Boolean enabled = roleConfig != null ? roleConfig.getEnabled() : false;
            result.add(ReviewCompletionItemDto.from(roleType, enabled, 0, 0, BigDecimal.ZERO));
        }
        return result;
    }

    /**
     * 计算单个角色的完成度统计
     */
    private ReviewCompletionItemDto calculateRoleCompletion(
            Long projectId, ProjectMemberTypeEnum roleType,
            List<TaskReview> roleReviews,
            List<ProjectMember> projectMemberByRole,
            Map<Long, List<TaskReviewScore>> taskReviewScoreMap,
            Set<Long> criterionIds,
            List<ReviewTypeEnum> reviewTypeEnums, Map<Long, List<TaskMember>> taskMemberByTaskIds) {
        // 1. 获取当前项目下当前角色的所有用户，如当前roleType = teacher
        if (CollectionUtils.isEmpty(projectMemberByRole) || MapUtils.isEmpty(taskReviewScoreMap)) {
            return ReviewCompletionItemDto.from(roleType, true, 0, 0, BigDecimal.ZERO);
        }

        RoleReviewStatsDto stats = calculateRoleReviewStatsByMembers(projectMemberByRole, taskMemberByTaskIds, roleReviews, reviewTypeEnums);

        // 7. 已评审的平均分
        BigDecimal averageScore = calculateRoleAverageScore(roleReviews, taskReviewScoreMap, criterionIds);

        return ReviewCompletionItemDto.from(roleType, true, stats.getReviewedCount(), stats.getTotalCount(), averageScore);
    }

    /**
     * 计算角色评审统计数据（按成员聚合）
     */
    private RoleReviewStatsDto calculateRoleReviewStatsByMembers(List<ProjectMember> roleMembers, Map<Long, List<TaskMember>> taskMemberByTaskIds, List<TaskReview> roleReviews, List<ReviewTypeEnum> reviewTypeEnums) {
        int totalReviewCount = 0;
        int completedReviewCount = 0;

        // 任务数量，计算组
        int taskCount = taskMemberByTaskIds.keySet().size();
        // 评审人数量，计算人数
        int taskMemberCount = taskMemberByTaskIds.values()
                .stream()
                .mapToInt(List::size)
                .sum();
        // 每个人员的所属任务数
        List<TaskMember> taskMemberList = taskMemberByTaskIds.values().stream().flatMap(Collection::stream).toList();

        Map<Long, Integer> taskCountByMember = taskMemberList.stream()
                .collect(Collectors.groupingBy(TaskMember::getUserId, Collectors.summingInt(tm -> 1)));

        Map<Long, List<TaskReview>> reviewsByMember = roleReviews.stream()
                .collect(Collectors.groupingBy(TaskReview::getReviewerId));


        for (ProjectMember member : roleMembers) {
            Long memberId = member.getUserId();

            // 计算该成员需要评审的任务数量
            int memberTotalReviews = calculateMemberTotalReviews(taskCount, taskMemberCount, taskCountByMember.getOrDefault(memberId, 0), reviewTypeEnums);
            totalReviewCount += memberTotalReviews;

            // 计算该成员已完成的评审数量
            List<TaskReview> memberReviews = reviewsByMember.getOrDefault(memberId, Lists.newArrayList());
            int memberCompletedReviews = calculateMemberCompletedReviews(memberReviews, reviewTypeEnums);
            completedReviewCount += memberCompletedReviews;
        }
        RoleReviewStatsDto roleReviewStatsDto = new RoleReviewStatsDto();
        roleReviewStatsDto.setReviewedCount(completedReviewCount);
        roleReviewStatsDto.setTotalCount(totalReviewCount);

        return roleReviewStatsDto;
    }

    /**
     * 计算单个成员需要评审的总数量
     */
    private int calculateMemberTotalReviews(int taskCount, int taskMemberCount, int currentMemberTaskCount, List<ReviewTypeEnum> reviewTypeEnums) {
        int totalCount = 0;

        if (reviewTypeEnums.contains(ReviewTypeEnum.GROUP)) {
            // 需要GROUP评审
            totalCount += taskCount;
        }

        if (reviewTypeEnums.contains(ReviewTypeEnum.INDIVIDUAL)) {
            // 需要INDIVIDUAL评审
            totalCount += taskMemberCount - currentMemberTaskCount;
        }
        return totalCount;
    }

    /**
     * 计算单个成员已完成的评审数量
     */
    private int calculateMemberCompletedReviews(List<TaskReview> currentMemberReviews, List<ReviewTypeEnum> reviewTypeEnums) {
        int completedCount = 0;
        if (reviewTypeEnums.contains(ReviewTypeEnum.GROUP) && reviewTypeEnums.contains(ReviewTypeEnum.INDIVIDUAL)) {
            return currentMemberReviews.size();
        }
        if (reviewTypeEnums.contains(ReviewTypeEnum.GROUP)) {
            // 统计GROUP评审完成情况
            long groupCount = currentMemberReviews.stream()
                    .filter(review -> ReviewTypeEnum.GROUP.equals(review.getReviewType()))
                    .count();
            completedCount += (int) groupCount;
        }
        if (reviewTypeEnums.contains(ReviewTypeEnum.INDIVIDUAL)) {
            // 统计GROUP评审完成情况
            long individualCount = currentMemberReviews.stream()
                    .filter(review -> ReviewTypeEnum.INDIVIDUAL.equals(review.getReviewType()))
                    .count();
            completedCount += (int) individualCount;
        }

        return completedCount;
    }

    /**
     * 计算角色平均分
     */
    private BigDecimal calculateRoleAverageScore(
            List<TaskReview> roleReviews,
            Map<Long, List<TaskReviewScore>> taskReviewScoreMap,
            Set<Long> criterionIds) {

        if (CollectionUtils.isEmpty(roleReviews) || CollectionUtils.isEmpty(criterionIds)) {
            return BigDecimal.ZERO;
        }
        List<Integer> allScores = Lists.newArrayList();
        for (TaskReview review : roleReviews) {
            List<TaskReviewScore> reviewScores = taskReviewScoreMap.get(review.getId());
            if (CollectionUtils.isNotEmpty(reviewScores)) {
                reviewScores.stream()
                        .filter(score -> criterionIds.contains(score.getCriterionId()))
                        .map(TaskReviewScore::getScore)
                        .filter(Objects::nonNull)
                        .forEach(allScores::add);
            }
        }
        if (allScores.isEmpty()) {
            return BigDecimal.ZERO;
        }
        return ScoreCalculationUtil.calculateAverage(allScores);
    }


    // 当前用户是否可以评审
    private boolean isReviewable(Long projectId) {
        // 1. 校验用户是否为项目成员
        ProjectMember projectMember = projectMemberMapper.selectByProjectIdAndUserId(projectId, UserHold.getUserId());
        if (projectMember == null) {
            return true;
        }
        ProjectReviewRole reviewRole = projectReviewRoleMapper.selectByProjectIdAndRoleCode(projectId, projectMember.getRole());
        return reviewRole == null || !reviewRole.getEnabled();
    }

    private boolean isReviewable(ProjectReviewRole reviewRole) {
        return reviewRole == null || !reviewRole.getEnabled();
    }


    private boolean isReviewable(Long projectId, ProjectMemberTypeEnum memberTypeEnum) {
        ProjectReviewRole reviewRole = projectReviewRoleMapper.selectByProjectIdAndRoleCode(projectId, memberTypeEnum);
        return reviewRole == null || !reviewRole.getEnabled();
    }

    private ReviewStatusEnum getTaskReviewIngStatus(Integer expectReviewCount, Integer actualReviewCount) {
        // 查看当前人是否已经进行了评审
        if (actualReviewCount == 0) {
            return ReviewStatusEnum.PENDING;
        }
        if (actualReviewCount < expectReviewCount) {
            return ReviewStatusEnum.IN_PROGRESS;
        }
        return ReviewStatusEnum.REVIEWED;
    }

    /**
     * 获取任务评审记录
     *
     * @param projectId
     * @param taskId
     * @param reviewMember
     * @param reviewTypeEnum
     * @param comment
     * @param revieweeId
     * @return
     */
    private TaskReview getCreateTaskReview(Long projectId, Long taskId, ProjectMember reviewMember, ReviewTypeEnum reviewTypeEnum, String comment, Long revieweeId) {
        TaskReview review = new TaskReview();
        review.setProjectId(projectId);
        review.setTaskId(taskId);
        if (reviewTypeEnum.equals(ReviewTypeEnum.INDIVIDUAL)) {
            review.setRevieweeId(revieweeId);
        }
        review.setComment(comment);
        review.setReviewType(reviewTypeEnum);
        review.setReviewerId(UserHold.getUserId());
        review.setReviewRoleCode(reviewMember.getRole());
        review.setReviewTime(new Date());
        review.setCreatedAt(new Date());
        review.setCreatorId(UserHold.getUserId());
        review.setUpdatedAt(new Date());
        review.setTenantId(UserHold.getTenantId());
        review.setUpdaterId(UserHold.getUserId());
        return review;
    }


    private void getUpdateTaskReview(TaskReview review, String comment) {
        review.setComment(comment);
        review.setUpdatedAt(new Date());
        review.setUpdaterId(UserHold.getUserId());
        review.setReviewTime(new Date());
    }


    private List<TaskReviewScore> getTaskReviewScoreList(Long taskReviewId, List<SubmitReviewRequest.ReviewCriteriaScore> groupReviewCriteria) {
        List<TaskReviewScore> scoreList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(groupReviewCriteria)) {
            return scoreList;
        }
        for (SubmitReviewRequest.ReviewCriteriaScore scoreReq : groupReviewCriteria) {
            TaskReviewScore score = new TaskReviewScore();
            score.setTaskReviewId(taskReviewId);
            score.setCriterionId(scoreReq.getId());
            score.setScore(scoreReq.getScore());
            score.setCreatedAt(new Date());
            score.setCreatorId(UserHold.getUserId());
            score.setUpdatedAt(new Date());
            score.setUpdaterId(UserHold.getUserId());
            score.setTenantId(UserHold.getTenantId());
            scoreList.add(score);
        }
        return scoreList;
    }


    private TaskReview saveGroupTaskReview(Long projectId, Long taskId, String comment, ProjectMember reviewMember) {

        TaskReview review = taskReviewMapper.selectGroupByTaskIdAndReviewId(taskId, UserHold.getUserId());
        if (review == null) {
            review = getCreateTaskReview(projectId, taskId, reviewMember, ReviewTypeEnum.GROUP, comment, null);
            taskReviewMapper.insert(review);
        } else {
            getUpdateTaskReview(review, comment);
            taskReviewMapper.updateById(review);
        }
        return review;
    }

    private void saveGroupReviewScore(TaskReview review, List<SubmitReviewRequest.ReviewCriteriaScore> groupReviewCriteria) {
        taskReviewScoreMapper.deleteByTaskReviewId(review.getId());
        List<TaskReviewScore> scoreList = getTaskReviewScoreList(review.getId(), groupReviewCriteria);
        if (CollectionUtils.isNotEmpty(scoreList)) {
            taskReviewScoreMapper.batchInsert(scoreList);
        }
    }

    private TaskReview saveIndividualTaskReview(TaskReview review, Long projectId, Long taskId, SubmitReviewRequest.MemberReviewInfo memberReq, ProjectMember reviewMember) {
        if (review == null) {
            review = getCreateTaskReview(projectId, taskId, reviewMember, ReviewTypeEnum.INDIVIDUAL, memberReq.getIndividualComment(), memberReq.getId());
            taskReviewMapper.insert(review);
        } else {
            getUpdateTaskReview(review, memberReq.getIndividualComment());
            taskReviewMapper.updateById(review);
        }
        return review;
    }

    private void saveIndividualReviewScore(TaskReview review, List<SubmitReviewRequest.ReviewCriteriaScore> groupReviewCriteria) {
        taskReviewScoreMapper.deleteByTaskReviewId(review.getId());
        List<TaskReviewScore> scoreList = getTaskReviewScoreList(review.getId(), groupReviewCriteria);
        if (CollectionUtils.isNotEmpty(scoreList)) {
            taskReviewScoreMapper.batchInsert(scoreList);
        }
    }

    private List<ProjectMember> getReviewMembers(Long projectId, Long taskId, Long selfUserId) {
        List<TaskMember> taskMemberList = taskMemberMapper.selectByProjectAndTask(projectId, taskId);
        if (CollectionUtils.isEmpty(taskMemberList)) {
            return Lists.newArrayList();
        }
        // 过滤自己
        List<Long> userIds = taskMemberList.stream().map(TaskMember::getUserId).collect(Collectors.toList());
        userIds.remove(selfUserId);
        if (CollectionUtils.isEmpty(userIds)) {
            return Lists.newArrayList();
        }
        return projectMemberMapper.selectByProjectIdAndUserIdList(projectId, userIds);

    }


    private Map<Long, Integer> getReviewCountsGroupByTaskId(List<Long> taskIdList, Long selfUserId) {

        List<TaskMember> taskMemberList = taskMemberMapper.selectByTaskIdListAndExceptUserId(taskIdList, selfUserId);
        if (CollectionUtils.isEmpty(taskMemberList)) {
            return Maps.newHashMap();
        }
        Map<Long, List<Long>> reviewMemberCounts = taskMemberList.stream()
                .collect(Collectors.groupingBy(TaskMember::getTaskId, Collectors.mapping(TaskMember::getUserId, Collectors.toList())));

        Map<Long, Integer> reviewMemberCountsMap = Maps.newHashMap();
        for (Long taskId : taskIdList) {
            List<Long> count = reviewMemberCounts.get(taskId);
            if (CollectionUtils.isEmpty(count)) {
                reviewMemberCountsMap.put(taskId, 1);
            } else {
                reviewMemberCountsMap.put(taskId, count.size() + 1);
            }
        }
        return reviewMemberCountsMap;

    }

    /**
     * 辅助方法：检查所有必需的评审角色是否都已完成评审。
     *
     * @param task     任务实体。
     * @param settings 项目评审设置。
     * @return 如果所有必需的评审角色都已完成评审，则返回true。
     */
    private boolean checkAllRequiredReviewsCompleted(ProjectTask task, ProjectReviewSettings settings) {
        if (task == null || settings == null) {
            return false;
        }
        // 获取项目启用的评审角色配置
        List<ProjectReviewRole> reviewRoles = projectReviewRoleMapper.selectByProjectId(task.getProjectId());

        boolean studentReviewEnabled = reviewRoles.stream().anyMatch(r -> r.getReviewRoleCode() == ProjectMemberTypeEnum.STUDENT && Boolean.TRUE.equals(r.getEnabled()));
        boolean teacherReviewEnabled = reviewRoles.stream().anyMatch(r -> r.getReviewRoleCode() == ProjectMemberTypeEnum.TEACHER && Boolean.TRUE.equals(r.getEnabled()));
        boolean companyReviewEnabled = reviewRoles.stream().anyMatch(r -> r.getReviewRoleCode() == ProjectMemberTypeEnum.COMPANY && Boolean.TRUE.equals(r.getEnabled()));
        // 检查各自评审状态是否已完成
        boolean studentReviewDone = !studentReviewEnabled || task.getStudentReviewStatus() == ReviewStatusEnum.COMPLETED;
        boolean teacherReviewDone = !teacherReviewEnabled || task.getTeacherReviewStatus() == ReviewStatusEnum.COMPLETED;
        boolean companyReviewDone = !companyReviewEnabled || task.getCompanyReviewStatus() == ReviewStatusEnum.COMPLETED;
        return studentReviewDone && teacherReviewDone && companyReviewDone;
    }

    /**
     * 辅助方法：发送任务评审完成通知。
     *
     * @param task 任务实体。
     */
    private void sendTaskReviewCompletedNotification(ProjectTask task) {
        Set<Long> recipientUserIds = new HashSet<>();
        Project project = projectMapper.selectById(task.getProjectId());

        if (project == null) {
            log.warn("任务ID: {} 评审完成通知失败：关联项目信息缺失。", task.getId());
            return;
        }

        // 1. 获取任务负责人
        if (task.getAssigneeId() != null) {
            recipientUserIds.add(task.getAssigneeId());
        } else if (task.getCreatorId() != null) { // 如果任务没有负责人，通知创建者
            recipientUserIds.add(task.getCreatorId());
        }

        // 2. 获取任务所有成员 (通过 TaskMemberMapper 查询)
        List<TaskMember> taskMembers = taskMemberMapper.selectByProjectAndTask(task.getProjectId(), task.getId());
        if (CollectionUtils.isNotEmpty(taskMembers)) {
            taskMembers.stream().map(TaskMember::getUserId).forEach(recipientUserIds::add);
        }
        Long operatorId = UserHold.getUserId();
        if (operatorId != null) {
            recipientUserIds.remove(operatorId);
        }
        if (CollectionUtils.isEmpty(recipientUserIds)) {
            log.debug("任务ID: {} 评审已完成，但没有找到接收人，跳过通知。", task.getId());
            return;
        }
        String subject = String.format("【评审完成】任务'%s'的评审已完成！", task.getName());
        String contentHtml = String.format("<p>尊敬的用户，您参与的任务<strong>'%s'</strong> (所属项目：<strong>'%s'</strong>) 的评审已全部完成。</p>" +
                                                   "<p>请登录平台查看最终评审结果。</p>",
                                           task.getName(), project.getTitle());

        MessageMetadata metadata = new MessageMetadata()
                .setEntityType("task_review_completed")
                .setEntityId(task.getId())
                .setProjectId(project.getId())
                .setTaskId(task.getId())
                .setJumpUrl(String.format("/projects/%d/review?taskId=%d", project.getId(), task.getId()));
        MessageSendDto messageSendDto = new MessageSendDto()
                .setSendUserId(operatorId != null ? operatorId : 1L)
                .setRecipientUserIds(recipientUserIds.stream().toList())
                .setMessageType(NotificationTypeEnum.REVIEW_NOTIFICATION)
                .setSubject(subject)
                .setContentHtml(contentHtml)
                .setTenantId(task.getTenantId())
                .setMetadata(metadata);
        eventPublisher.publishEvent(new MessageEvent(this, messageSendDto));
        log.info("已为任务ID: {} 评审完成发布REVIEW_NOTIFICATION通知给{}位相关用户。", task.getId(), recipientUserIds.size());
    }


    private Map<Long, List<TaskMember>> getTaskMember(List<Long> taskIds) {
        if (CollectionUtils.isEmpty(taskIds)) {
            return Maps.newHashMap();
        }
        List<TaskMember> taskMemberList = taskMemberMapper.selectByTaskList(taskIds);
        Map<Long, List<TaskMember>> taskMemberMap = Maps.newHashMap();
        for (Long taskId : taskIds) {
            taskMemberMap.put(taskId, taskMemberList.stream().filter(t -> t.getTaskId().equals(taskId)).toList());
        }
        return taskMemberMap;

    }


}