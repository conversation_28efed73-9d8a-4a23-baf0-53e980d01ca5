package com.nybc.edu.biz.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * 任务状态枚举
 */
@Getter
@AllArgsConstructor
public enum TaskStatusEnum {

    DRAFT("DRAFT", "草稿"),

    NOT_STARTED("NOT_STARTED", "未开始"),

    IN_PROGRESS("IN_PROGRESS", "进行中"),

    PHASES_COMPLETED("PHASES_COMPLETED", "阶段已完成"),

    REVIEWING("REVIEWING", "评审中"),
    // 评审已结束
    REVIEW_COMPLETED("REVIEW_COMPLETED", "评审已结束"),

    COMPLETED("COMPLETED", "已完成");

    private final String code;

    private final String description;


    // 获取进行中的状态
    public static List<TaskStatusEnum> getInProgressStatus() {
        return Arrays.asList(TaskStatusEnum.IN_PROGRESS, TaskStatusEnum.PHASES_COMPLETED, TaskStatusEnum.REVIEWING);
    }

    // 获取非草稿的状态
    public static List<TaskStatusEnum> getNotDraftStatus() {
        return Arrays.asList(TaskStatusEnum.NOT_STARTED, TaskStatusEnum.IN_PROGRESS, TaskStatusEnum.PHASES_COMPLETED, TaskStatusEnum.REVIEWING, TaskStatusEnum.REVIEW_COMPLETED, TaskStatusEnum.COMPLETED);
    }

}