<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nybc.edu.course.dao.StudentCourseEnrollmentMapper">
    <resultMap id="BaseResultMap" type="com.nybc.edu.course.domain.StudentCourseEnrollment">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="student_user_id" jdbcType="BIGINT" property="studentUserId"/>
        <result column="course_id" jdbcType="BIGINT" property="courseId"/>
        <result column="enrollment_date" jdbcType="TIMESTAMP" property="enrollmentDate"/>
        <result column="enrollment_status" jdbcType="VARCHAR" property="enrollmentStatus"/>
        <result column="final_grade" jdbcType="NUMERIC" property="finalGrade"/>
        <result column="completion_date" jdbcType="TIMESTAMP" property="completionDate"/>
        <result column="semester" jdbcType="VARCHAR" property="semester"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user" jdbcType="BIGINT" property="createUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user" jdbcType="BIGINT" property="updateUser"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="deleted" jdbcType="INTEGER" property="deleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, student_user_id, course_id, enrollment_date, enrollment_status, final_grade, completion_date, semester, 
            create_time, create_user, update_time, update_user, tenant_id, deleted
    </sql>
    <!--查询单个-->
    <select id="getById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_student_course_enrollment
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="findByStudentAndCourse" resultType="com.nybc.edu.course.domain.StudentCourseEnrollment">
        select
        <include refid="Base_Column_List"/>
        from t_student_course_enrollment
        <where>
            <if test="studentUserId != null">
                and student_user_id = #{studentUserId,jdbcType=BIGINT}
            </if>
            <if test="courseId != null">
                and course_id = #{courseId,jdbcType=BIGINT}
            </if>
            <if test="semester != null">
                and semester = #{semester,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <select id="findActiveEnrollmentsByStudentAndCourseCode"
            resultType="com.nybc.edu.course.domain.StudentCourseEnrollment">
        SELECT sce.*
        FROM t_student_course_enrollment sce
                 INNER JOIN t_course_info c ON sce.course_id = c.id -- 通过 course_id 关联到课程表
        WHERE sce.student_user_id = #{studentUserId, jdbcType=BIGINT}
          AND c.course_code = #{courseCode, jdbcType=VARCHAR} -- 筛选特定课程代码
          AND sce.enrollment_status = 'FAILED'                -- 只选择挂科的记录 (假设 FAILED 是枚举的code)
          AND sce.deleted = 0                                 -- 只选择未被逻辑删除的记录
        ORDER BY sce.enrollment_date DESC -- 按选课日期降序，最新的挂科记录在前 (可选)

    </select>
    <select id="findByStudentId" resultType="com.nybc.edu.course.domain.StudentCourseEnrollment">
        select
        <include refid="Base_Column_List"/>
        from t_student_course_enrollment
        where student_user_id = #{studentUserId,jdbcType=BIGINT}
    </select>
    <select id="findByCourseId" resultType="com.nybc.edu.course.domain.StudentCourseEnrollment">
        select
        <include refid="Base_Column_List"/>
        from t_student_course_enrollment
        where course_id = #{courseId,jdbcType=BIGINT}
    </select>
    <select id="findByCourseIdAndSemester" resultType="com.nybc.edu.course.domain.StudentCourseEnrollment">
        select
        <include refid="Base_Column_List"/>
        from t_student_course_enrollment
        where course_id = #{courseId,jdbcType=BIGINT}
        and semester = #{semester,jdbcType=VARCHAR}
    </select>
    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from t_student_course_enrollment
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true"
            parameterType="com.nybc.edu.course.domain.StudentCourseEnrollment">
        insert into t_student_course_enrollment(student_user_id, course_id, enrollment_date, enrollment_status,
                                                final_grade, completion_date, semester, create_time, create_user,
                                                update_time, update_user, tenant_id, deleted)
        values (#{studentUserId,jdbcType=BIGINT}, #{courseId,jdbcType=BIGINT}, #{enrollmentDate,jdbcType=TIMESTAMP},
                #{enrollmentStatus,jdbcType=VARCHAR}, #{finalGrade,jdbcType=NUMERIC},
                #{completionDate,jdbcType=TIMESTAMP}, #{semester,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
                #{createUser,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=BIGINT},
                #{tenantId,jdbcType=BIGINT}, #{deleted,jdbcType=INTEGER})
    </insert>
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true"
            parameterType="com.nybc.edu.course.domain.StudentCourseEnrollment">
        insert into t_student_course_enrollment(
        student_user_id, course_id, enrollment_date, enrollment_status, final_grade, completion_date, semester,
        create_time, create_user, update_time, update_user, tenant_id, deleted ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.studentUserId,jdbcType=BIGINT} , #{item.courseId,jdbcType=BIGINT} ,
            #{item.enrollmentDate,jdbcType=TIMESTAMP} , #{item.enrollmentStatus,jdbcType=VARCHAR} ,
            #{item.finalGrade,jdbcType=NUMERIC} , #{item.completionDate,jdbcType=TIMESTAMP} ,
            #{item.semester,jdbcType=VARCHAR} , #{item.createTime,jdbcType=TIMESTAMP} ,
            #{item.createUser,jdbcType=BIGINT} , #{item.updateTime,jdbcType=TIMESTAMP} ,
            #{item.updateUser,jdbcType=BIGINT} , #{item.tenantId,jdbcType=BIGINT} , #{item.deleted,jdbcType=INTEGER} )
        </foreach>
    </insert>
    <!--通过主键修改数据-->
    <update id="updateById" parameterType="com.nybc.edu.course.domain.StudentCourseEnrollment">
        update t_student_course_enrollment
        <set>
            student_user_id = #{studentUserId,jdbcType=BIGINT},
            course_id = #{courseId,jdbcType=BIGINT},
            enrollment_date = #{enrollmentDate,jdbcType=TIMESTAMP},
            enrollment_status = #{enrollmentStatus,jdbcType=VARCHAR},
            final_grade = #{finalGrade,jdbcType=NUMERIC},
            completion_date = #{completionDate,jdbcType=TIMESTAMP},
            semester = #{semester,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            create_user = #{createUser,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            update_user = #{updateUser,jdbcType=BIGINT},
            tenant_id = #{tenantId,jdbcType=BIGINT},
            deleted = #{deleted,jdbcType=INTEGER},
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>

