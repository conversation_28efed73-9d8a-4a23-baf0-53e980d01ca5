package com.nybc.edu.ops.git;

import lombok.extern.slf4j.Slf4j;
import org.eclipse.jgit.lib.ProgressMonitor;

/**
 * 类描述：
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
public class LogProgressMonitor implements ProgressMonitor {

    private String title;
    private int totalWork;
    private int completedWork;
    private int lastPercent;

    @Override
    public void start(int totalTasks) {
        log.info("Starting tasks. Total tasks: {}", totalTasks);
    }

    @Override
    public void beginTask(String title, int totalWork) {
        this.title = title;
        this.totalWork = totalWork;
        this.completedWork = 0;
        this.lastPercent = 0;
        log.info("Beginning task: {} (Total work: {})", title, totalWork);
    }

    @Override
    public void update(int completed) {
        completedWork += completed;
        int percent = (totalWork > 0) ? (completedWork * 100) / totalWork : 0;
        if (percent > lastPercent) {
            log.info("{}: {}% completed", title, percent);
            lastPercent = percent;
        }
    }

    @Override
    public void endTask() {
        log.info("Task completed: {}", title);
    }

    @Override
    public boolean isCancelled() {
        return false;
    }

    /**
     * @param b
     */
    @Override
    public void showDuration(boolean b) {

    }

}