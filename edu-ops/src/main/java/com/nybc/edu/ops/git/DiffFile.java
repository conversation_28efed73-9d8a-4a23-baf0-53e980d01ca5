package com.nybc.edu.ops.git;

import lombok.Data;

/**
 * 差异文件类
 */
@Data
public class DiffFile {
    /**
     * 旧文件路径
     */
    private final String oldPath;

    /**
     * 新文件路径
     */
    private final String newPath;

    /**
     * 变更类型
     */
    private final ChangeType changeType;

    /**
     * 变更类型枚举
     */
    public enum ChangeType {
        ADD,
        MODIFY,
        DELETE,
        RENAME,
        COPY,
        UNKNOWN
    }

}
