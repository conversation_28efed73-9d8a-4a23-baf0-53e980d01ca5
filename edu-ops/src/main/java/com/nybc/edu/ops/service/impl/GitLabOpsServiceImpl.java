package com.nybc.edu.ops.service.impl;

import com.github.pagehelper.PageInfo;
import com.nybc.edu.common.page.PageResult;
import com.nybc.edu.common.aop.LogRecord;
import com.nybc.edu.common.enums.LogRecordEnum;
import com.nybc.edu.common.enums.ResultCodeEnum;
import com.nybc.edu.common.event.FileCommentEvent;
import com.nybc.edu.common.model.PermissionCheckRequestDto;
import com.nybc.edu.common.model.UserDto;
import com.nybc.edu.core.exception.BizException;
import com.nybc.edu.ops.controller.internal.ProjectTaskPermissionApiClient;
import com.nybc.edu.ops.dao.ProjectTaskGitlabCommentMapper;
import com.nybc.edu.ops.dao.ProjectTaskGitlabMapper;
import com.nybc.edu.ops.entity.ProjectTaskGitlab;
import com.nybc.edu.ops.entity.ProjectTaskGitlabComment;
import com.nybc.edu.ops.gitlab.GitProjectService;
import com.nybc.edu.ops.gitlab.GitUserService;
import com.nybc.edu.ops.gitlab.PinyinUtils;
import com.nybc.edu.ops.model.*;
import com.nybc.edu.ops.service.*;
import com.nybc.edu.ops.utils.TemplateFileLoader;
import com.nybc.user.context.UserHold;
import com.nybc.user.dao.UserInfoMapper;
import com.nybc.user.entity.UserInfo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.gitlab4j.api.*;
import org.gitlab4j.api.models.*;
import org.gitlab4j.models.Constants;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

import static com.nybc.edu.common.constant.NytcConstant.DELETE;
import static com.nybc.edu.common.constant.NytcConstant.NORMAL;
import static com.nybc.common.tool.NytcToolConstant.NYBC_EXECUTOR;


/**
 * 类描述：
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Service
@Slf4j
public class GitLabOpsServiceImpl implements GitLabOpsService {
    private static final AccessLevel DEFAULT_TEACHER_ACCESS_LEVEL = AccessLevel.MAINTAINER;
    private static final AccessLevel DEFAULT_STUDENT_ACCESS_LEVEL = AccessLevel.DEVELOPER;
    @Resource
    private GitStatsService gitStatsService;
    @Resource
    private ProjectTaskPermissionApiClient projectTaskPermissionApiClient;
    @Resource
    private GitLabApi gitLabApi;
    @Resource
    private GitLabRepositoryCacheService gitLabRepositoryCacheService;
    @Resource
    private GitProjectService gitProjectService;
    @Resource
    private ProjectTaskGitlabMapper projectTaskGitlabMapper;
    @Resource
    private UserInfoMapper userInfoMapper;
    @Resource
    private GitUserService gitUserService;
    @Resource
    private EmailService emailService;
    @Resource
    private ProjectTaskGitlabCommentMapper fileCommentMapper;
    @Resource
    private GitLabFileVersionService gitLabFileVersionService;
    @Resource
    private ApplicationEventPublisher eventPublisher;
    @Resource
    private ProjectTaskGitlabCommentMapper projectTaskGitlabCommentMapper;
    @Resource
    private TemplateFileLoader templateFileLoader;


    /**
     * 辅助方法：设置GitLab项目详细信息到映射实体
     * 将GitLab项目的详细信息缓存到数据库，避免频繁调用GitLab API
     *
     * @param mapping       GitLab项目映射实体
     * @param gitlabProject GitLab项目对象
     */
    private void setGitLabProjectDetails(ProjectTaskGitlab mapping, Project gitlabProject) {
        mapping.setGitlabProjectName(gitlabProject.getName());
        mapping.setGitlabProjectDescription(gitlabProject.getDescription());
        mapping.**********************(gitlabProject.getWebUrl());
        mapping.setGitlabProjectHttpUrlToRepo(gitlabProject.getHttpUrlToRepo());
        mapping.setGitlabProjectSshUrlToRepo(gitlabProject.getSshUrlToRepo());
        mapping.setGitlabProjectDefaultBranch(gitlabProject.getDefaultBranch());
        mapping.setGitlabProjectVisibility(gitlabProject.getVisibility() != null ?
                                           gitlabProject.getVisibility().toString().toLowerCase() : "private");
        mapping.setGitlabProjectCreatedAt(gitlabProject.getCreatedAt() != null ?
                                          LocalDateTime.ofInstant(gitlabProject.getCreatedAt().toInstant(), ZoneId.systemDefault()) : null);
        mapping.setGitlabProjectLastActivityAt(gitlabProject.getLastActivityAt() != null ?
                                               LocalDateTime.ofInstant(gitlabProject.getLastActivityAt().toInstant(), ZoneId.systemDefault()) : null);
    }

    /**
     * 创建GitLab项目仓库 (主仓库)，分配成员，并记录平台映射关系。
     * 该方法实现了完整的GitLab项目创建和用户管理工作流。
     *
     * @param request 包含所有必要信息的请求DTO
     * @return 包含创建结果信息的响应DTO
     * @throws BizException 如果发生业务逻辑错误
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public GitLabProjectCreateResponseDto createGitLabProjectWithWorkflow(GitLabProjectCreateRequestDto request) {
        log.info("开始为平台项目ID: {} (任务ID: {}) 创建主GitLab项目。", request.********************(), request.getPlatformTaskId());
        // 1. 验证GitLab父组是否存在，如果不存在则创建
        Group parentGroup = ensureParentGroupExists(request.********************(), request.getPlatformProjectName());
        log.info("GitLab父组 '{}' (ID: {}) 验证通过。", parentGroup.getName(), parentGroup.getId());
        // 2. 准备GitLab项目名称和描述
        String gitlabProjectName = "P" + request.********************() +
                (request.getPlatformTaskId() != null ? "_T" + request.getPlatformTaskId() : "");
        // 为任务主仓库添加拼音首字母，确保更具描述性且减少冲突
        if (request.getPlatformTaskId() != null && StringUtils.isNotBlank(request.getPlatformTaskName())) {
            gitlabProjectName += "_" + PinyinUtils.getPinyinInitials(request.getPlatformTaskName());
        } else if (StringUtils.isNotBlank(request.getPlatformProjectName())) {
            gitlabProjectName += "_" + PinyinUtils.getPinyinInitials(request.getPlatformProjectName());
        }
        gitlabProjectName = StringUtils.truncate(gitlabProjectName, 100).toLowerCase();
        String gitlabProjectDesc = StringUtils.isNotBlank(request.getGitlabProjectDescription()) ?
                                   request.getGitlabProjectDescription() :
                                   "平台项目: " + request.getPlatformProjectName() +
                                           (request.getPlatformTaskId() != null ? " - 任务: " + request.getPlatformTaskName() : "") +
                                           " 的主代码仓库";
        // 3. 创建或获取GitLab项目仓库
        Project createdGitLabProject = createOrGetGitLabProject(parentGroup, gitlabProjectName, gitlabProjectDesc, request.getInitialBranch());
        log.info("GitLab主项目已创建/获取: ID={}, Name={}, Path={}, WebUrl={}",
                 createdGitLabProject.getId(), createdGitLabProject.getName(),
                 createdGitLabProject.getPathWithNamespace(), createdGitLabProject.getWebUrl());
        // 4. 注入项目模板
        applyProjectTemplate(createdGitLabProject.getId(), request.getGitlabTemplateType().name(), createdGitLabProject.getDefaultBranch());

        // 6. 添加项目成员
        List<Long> actualTeacherUserIds = new ArrayList<>(request.getTeacherUserIds());
        List<Long> actualStudentUserIds = new ArrayList<>(request.getStudentUserIds());
        Long currentOperatorId = request.getTeacherUserIds().getFirst();
        if (currentOperatorId != null) {
            // 确保操作者作为创建者被添加到项目，且拥有足够权限
            try {
                // 默认将创建项目的用户添加为Maintainer
                addMemberToGitlabProject(createdGitLabProject.getId(), currentOperatorId, AccessLevel.MAINTAINER, createdGitLabProject, "操作者");
                log.info("操作者用户 {} 已作为Maintainer添加到项目ID: {}", currentOperatorId, createdGitLabProject.getId());
            } catch (Exception e) {
                log.warn("无法将操作者 {} 添加为项目ID: {} 的Maintainer: {}", currentOperatorId, createdGitLabProject.getId(), e.getMessage());
            }
            // 从列表中移除操作者，避免重复添加，如果操作者本身在教师或学生列表中
            actualTeacherUserIds.remove(currentOperatorId);
            actualStudentUserIds.remove(currentOperatorId);
        }
        addMembersToGitLabProject(createdGitLabProject, actualTeacherUserIds, DEFAULT_TEACHER_ACCESS_LEVEL, "教师");
        addMembersToGitLabProject(createdGitLabProject, actualStudentUserIds, DEFAULT_STUDENT_ACCESS_LEVEL, "学生");
        // 7. 在平台数据库中记录映射关系
        ProjectTaskGitlab projectTaskGitlab = new ProjectTaskGitlab();
        projectTaskGitlab.setProjectId(request.********************());
        projectTaskGitlab.setTaskId(request.getPlatformTaskId());
        projectTaskGitlab.setGitlabProjectId(createdGitLabProject.getId());
        projectTaskGitlab.********************(createdGitLabProject.getPathWithNamespace());
        // 设置GitLab项目详细信息 (缓存字段，避免频繁API调用)
        setGitLabProjectDetails(projectTaskGitlab, createdGitLabProject);
        projectTaskGitlab.setMappingType(request.getGitlabTemplateType());
        projectTaskGitlab.setCreateTime(LocalDateTime.now());
        projectTaskGitlab.setUpdateTime(LocalDateTime.now());
        projectTaskGitlab.setDeleted(NORMAL);
        projectTaskGitlab.setCreateUser(currentOperatorId != null ? currentOperatorId : 1L);
        projectTaskGitlab.setUpdateUser(currentOperatorId != null ? currentOperatorId : 1L);
        projectTaskGitlab.setTenantId(request.getTenantId());
        projectTaskGitlabMapper.insert(projectTaskGitlab);
        log.info("平台项目/任务与GitLab主项目 (ID: {}) 的映射关系已记录, 本地映射ID: {}", createdGitLabProject.getId(), projectTaskGitlab.getId());
        return new GitLabProjectCreateResponseDto()
                .setPlatformProjectId(request.********************())
                .setPlatformTaskId(request.getPlatformTaskId())
                .setGitlabProjectId(createdGitLabProject.getId())
                .********************(createdGitLabProject.getPathWithNamespace())
                .**********************(createdGitLabProject.getWebUrl())
                .setGitlabProjectHttpUrlToRepo(createdGitLabProject.getHttpUrlToRepo())
                .setGitlabProjectSshUrlToRepo(createdGitLabProject.getSshUrlToRepo())
                .setMappingId(projectTaskGitlab.getId());
    }

    /**
     * 创建额外GitLab项目仓库，分配成员，并记录平台映射关系。
     * 用于平台手动创建的、与任务关联的辅助代码仓库（如前端、文档、测试仓库）。
     *
     * @param request 包含所有必要信息的请求DTO，包括新的gitlabTemplateType
     * @return 包含创建结果信息的响应DTO
     * @throws BizException 如果业务逻辑校验失败
     */
    @LogRecord(
            type = LogRecordEnum.TASK_CREATE,
            action = "创建任务仓库",
            detail = "为任务创建GitLab仓库，任务ID：#{#request.platformTaskId}，模板类型：#{#request.gitlabTemplateType}",
            targetId = "#request.platformTaskId",
            parentId = "#request.platformProjectId"
    )
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public GitLabProjectCreateResponseDto createAdditionalGitLabProjectWithWorkflow(GitLabProjectCreateRequestDto request) {
        log.info("开始为平台项目ID: {} (任务ID: {}) 创建额外GitLab项目，模板类型: {}",
                 request.********************(), request.getPlatformTaskId(), request.getGitlabTemplateType());
        if (request.getPlatformTaskId() == null) {
            throw new BizException("创建额外GitLab仓库必须关联具体的平台任务ID。");
        }
        // 1. 确保父组存在 (与主仓库使用相同的父组)
        Group parentGroup = ensureParentGroupExists(request.********************(), request.getPlatformProjectName());
        log.info("GitLab父组 '{}' (ID: {}) 验证通过。", parentGroup.getName(), parentGroup.getId());
        // 2. 准备GitLab项目名称和描述 (根据额外仓库类型命名)
        String baseProjectName = StringUtils.isNotBlank(request.getGitlabProjectName()) ?
                                 request.getGitlabProjectName().toLowerCase() :
                                 (PinyinUtils.getPinyinInitials(request.getPlatformTaskName()) + "_" + request.getPlatformTaskId()).toLowerCase();
        // 确保额外仓库名称具有唯一性，例如加上类型前缀或后缀
        // 使用模板类型的名称作为后缀，确保唯一性，例如 my_task_frontend_vue_cli
        String additionalRepoName = StringUtils.truncate(baseProjectName + "_" + request.getGitlabTemplateType().name().toLowerCase(), 100);
        String gitlabProjectDesc = StringUtils.isNotBlank(request.getGitlabProjectDescription()) ?
                                   request.getGitlabProjectDescription() :
                                   "平台项目: " + request.getPlatformProjectName() + " - 任务: " + request.getPlatformTaskName() +
                                           " 的额外代码仓库 (" + request.getGitlabTemplateType().getDescription() + ")";
        // 3. 创建或获取GitLab项目仓库
        // initialBranch 可以在请求中指定，否则使用GitLab默认
        Project createdGitLabProject = createOrGetGitLabProject(parentGroup, additionalRepoName, gitlabProjectDesc, request.getInitialBranch());
        log.info("GitLab额外项目已创建/获取: ID={}, Name={}, Path={}, WebUrl={}",
                 createdGitLabProject.getId(), createdGitLabProject.getName(),
                 createdGitLabProject.getPathWithNamespace(), createdGitLabProject.getWebUrl());
        // 4. 注入项目模板
        applyProjectTemplate(createdGitLabProject.getId(), request.getGitlabTemplateType().name(), createdGitLabProject.getDefaultBranch());

        // 6. 添加教师和学生作为项目成员
        List<Long> actualTeacherUserIds = new ArrayList<>(request.getTeacherUserIds());
        List<Long> actualStudentUserIds = new ArrayList<>(request.getStudentUserIds());
        Long currentOperatorId = UserHold.getUserId();
        if (currentOperatorId != null) {
            // 确保操作者作为创建者被添加到项目，且拥有足够权限
            try {
                // 默认将创建项目的用户添加为Maintainer
                addMemberToGitlabProject(createdGitLabProject.getId(), currentOperatorId, AccessLevel.MAINTAINER, createdGitLabProject, "操作者");
                log.info("操作者用户 {} 已作为Maintainer添加到项目ID: {}", currentOperatorId, createdGitLabProject.getId());
            } catch (Exception e) {
                log.warn("无法将操作者 {} 添加为项目ID: {} 的Maintainer: {}", currentOperatorId, createdGitLabProject.getId(), e.getMessage());
            }
            // 从列表中移除操作者，避免重复添加，如果操作者本身在教师或学生列表中
            actualTeacherUserIds.remove(currentOperatorId);
            actualStudentUserIds.remove(currentOperatorId);
        }
        addMembersToGitLabProject(createdGitLabProject, actualTeacherUserIds, DEFAULT_TEACHER_ACCESS_LEVEL, "教师");
        addMembersToGitLabProject(createdGitLabProject, actualStudentUserIds, DEFAULT_STUDENT_ACCESS_LEVEL, "学生");
        // 7. 在平台数据库中记录映射关系
        ProjectTaskGitlab mapping = new ProjectTaskGitlab();
        mapping.setProjectId(request.********************());
        mapping.setTaskId(request.getPlatformTaskId());
        mapping.setGitlabProjectId(createdGitLabProject.getId());
        mapping.********************(createdGitLabProject.getPathWithNamespace());
        // 设置GitLab项目详细信息 (缓存字段，避免频繁API调用)
        setGitLabProjectDetails(mapping, createdGitLabProject);
        mapping.setMappingType(request.getGitlabTemplateType());
        mapping.setCreateTime(LocalDateTime.now());
        mapping.setUpdateTime(LocalDateTime.now());
        mapping.setDeleted(NORMAL);
        mapping.setCreateUser(currentOperatorId != null ? currentOperatorId : 1L);
        mapping.setUpdateUser(currentOperatorId != null ? currentOperatorId : 1L);
        projectTaskGitlabMapper.insert(mapping);
        log.info("平台项目/任务与GitLab额外项目 (ID: {}) 的映射关系已记录, 本地映射ID: {}", createdGitLabProject.getId(), mapping.getId());
        // 8. 构建并返回响应DTO
        return new GitLabProjectCreateResponseDto()
                .setPlatformProjectId(request.********************())
                .setPlatformTaskId(request.getPlatformTaskId())
                .setGitlabProjectId(createdGitLabProject.getId())
                .********************(createdGitLabProject.getPathWithNamespace())
                .**********************(createdGitLabProject.getWebUrl())
                .setGitlabProjectHttpUrlToRepo(createdGitLabProject.getHttpUrlToRepo())
                .setGitlabProjectSshUrlToRepo(createdGitLabProject.getSshUrlToRepo())
                .setMappingId(mapping.getId());
    }

    /**
     * 辅助方法：创建组命名空间
     * (从GitProjectService移动到此处，使其成为GitLabOpsServiceImpl的私有辅助方法)
     *
     * @param platformProjectId
     * @param platformProjectName
     * @return 创建或已存在的 Group 对象
     */
    private Group ensureParentGroupExists(Long platformProjectId, String platformProjectName) {
        String gitlabParentGroupName = "P_" + platformProjectId;
        String description = "项目：[" + platformProjectName + "] (ID:" + platformProjectId + ") 的代码仓库分组";
        // 检查组是否已存在
        try {
            List<Group> groups = gitLabApi.getGroupApi().getGroups(gitlabParentGroupName);
            Group targetGroup = null;
            for (Group group : groups) {
                if (group.getName().equals(gitlabParentGroupName)) {
                    targetGroup = group;
                    break;
                }
            }
            if (targetGroup == null) {
                GroupParams groupParams = new GroupParams();
                groupParams.withName(gitlabParentGroupName);
                groupParams.withPath(gitlabParentGroupName);
                groupParams.withDescription(description);
                groupParams.withVisibility(Visibility.PRIVATE.toString());
                Group createdGroup = gitLabApi.getGroupApi().createGroup(groupParams);
                log.info("创建新的组命名空间: {}", gitlabParentGroupName);
                return createdGroup;
            } else {
                log.info("组命名空间已存在: {}", gitlabParentGroupName);
                return targetGroup;
            }
        } catch (GitLabApiException e) {
            log.error("在创建或获取GitLab父组 {} 时发生GitLab API错误: {}", gitlabParentGroupName, e.getMessage(), e);
            throw new BizException("GitLab父组操作失败: " + e.getMessage(), e);
        }
    }

    /**
     * 辅助方法：创建或获取GitLab项目仓库。
     * (从GitProjectService移动到此处，使其成为GitLabOpsServiceImpl的私有辅助方法)
     *
     * @param parentGroup   GitLab父组
     * @param projectName   项目名称
     * @param description   项目描述（可选）
     * @param initialBranch 初始分支名称 (例如 "main", "master", "develop")
     * @return 创建或已存在的 Project 对象
     * @throws BizException 如果操作失败抛出异常
     */
    private Project createOrGetGitLabProject(Group parentGroup, String projectName, String description, String initialBranch) {
        try {
            // 检查项目是否已存在
            List<Project> projects = gitLabApi.getProjectApi().getProjects(projectName);
            Project targetProject = null;
            for (Project project : projects) {
                if (project.getName().equals(projectName) && project.getNamespace().getId().equals(parentGroup.getId())) {
                    targetProject = project;
                    break;
                }
            }
            if (targetProject == null) {
                // 项目不存在，创建新项目
                Project newProject = new Project();
                newProject.setName(projectName);
                newProject.withNamespaceId(parentGroup.getId());
                newProject.setDescription(description);
                newProject.setVisibility(Visibility.PRIVATE);
                newProject.setInitializeWithReadme(false);
                newProject.withIssuesEnabled(true);
                newProject.withMergeRequestsEnabled(true);
                newProject.withWikiEnabled(true);
                newProject.withSnippetsEnabled(true);
                if (StringUtils.isNotBlank(initialBranch)) {
                    newProject.setDefaultBranch(initialBranch);
                } else {
                    newProject.setDefaultBranch("main");
                }
                Project createdProject = gitLabApi.getProjectApi().createProject(newProject);
                log.info("创建新的项目: {}", projectName);
                // 确保 develop 和 release 分支存在，如果需要
                createBranchIfNotExists(createdProject.getId(), "develop", createdProject.getDefaultBranch());
                createBranchIfNotExists(createdProject.getId(), "release", createdProject.getDefaultBranch());
                return createdProject;
            } else {
                log.info("项目已存在: {}", projectName);
                return targetProject;
            }
        } catch (GitLabApiException e) {
            log.error("在创建或获取GitLab项目 {} (父组ID: {}) 时发生GitLab API错误: {}", projectName, parentGroup.getId(), e.getMessage(), e);
            throw new BizException("GitLab项目创建/获取失败: " + e.getMessage(), e);
        }
    }

    /**
     * 辅助方法：如果分支不存在则创建。
     *
     * @param projectId    项目ID
     * @param branchName   待创建分支名称
     * @param sourceBranch 源分支名称
     * @return 创建或已存在的 Branch 对象
     */
    private Branch createBranchIfNotExists(Long projectId, String branchName, String sourceBranch) {
        try {
            // 检查分支是否已存在
            List<Branch> branches = gitLabApi.getRepositoryApi().getBranches(projectId);
            Optional<Branch> existingBranch = branches.stream()
                    .filter(b -> b.getName().equals(branchName))
                    .findFirst();
            if (existingBranch.isEmpty()) {
                Branch newBranch = gitLabApi.getRepositoryApi().createBranch(projectId, branchName, sourceBranch);
                log.info("为项目ID {} 创建分支 '{}' (源分支: '{}')。", projectId, branchName, sourceBranch);
                return newBranch;
            } else {
                log.info("项目ID {} 的分支 '{}' 已存在，无需创建。", projectId, branchName);
                return existingBranch.get();
            }
        } catch (GitLabApiException e) {
            log.error("为项目ID {} 创建分支 '{}' 失败: {}", projectId, branchName, e.getMessage(), e);
            // 失败时可以根据业务需求选择抛出异常或返回null
            throw new BizException("创建GitLab分支失败: " + e.getMessage(), e);
        }
    }


    // 辅助方法：删除CI/CD变量 (如果需要)
    private void deleteCiVariable(ProjectApi projectApi, Long projectId, String key) {
        try {
            projectApi.deleteVariable(projectId, key);
            log.debug("已删除GitLab项目ID: {} 的CI/CD变量: {}", projectId, key);
        } catch (GitLabApiException e) {
            if (e.getHttpStatus() == 404) {
                log.warn("GitLab项目ID: {} 的CI/CD变量 '{}' 不存在，无需删除。", projectId, key);
            } else {
                log.error("删除GitLab项目ID: {} 的CI/CD变量 '{}' 失败: {}", projectId, key, e.getMessage());
            }
        }
    }

    /**
     * 辅助方法：将平台用户添加到GitLab项目。
     * 此方法现在统一处理GitLab用户的创建、解锁逻辑，并发送用户凭证邮件和项目访问邮件。
     *
     * @param gitlabProjectId GitLab项目ID
     * @param platformUserId  平台用户ID
     * @param accessLevel     GitLab访问级别
     * @param gitlabProject   创建的GitLab项目对象 (用于获取项目URL等信息)
     * @param roleDescription 用于日志的角色描述
     * @throws BizException 如果平台用户不存在或GitLab用户操作失败
     */
    private void addMemberToGitlabProject(Long gitlabProjectId, Long platformUserId, AccessLevel accessLevel, Project gitlabProject, String roleDescription) {
        // 1. 获取平台用户信息，包括用户名和邮箱
        UserInfo userInfo = userInfoMapper.getById(platformUserId);
        if (userInfo == null || StringUtils.isBlank(userInfo.getUserName()) || StringUtils.isBlank(userInfo.getEmail())) {
            log.warn("平台用户ID: {} 不存在，或其平台用户名/邮箱为空，无法添加到GitLab项目 {} 作为 {}",
                     platformUserId, gitlabProjectId, roleDescription);
            throw new BizException("平台用户ID: " + platformUserId + " 信息不完整，无法在GitLab中操作。");
        }
        String gitlabUsername = userInfo.getUserName();
        if ("admin".equals(gitlabUsername) || "root".equals(gitlabUsername)) {
            return;
        }
        try {
            // 2. 确保GitLab用户存在且处于活跃状态，并获取凭证信息（包含临时密码，如果新创建）
            GitLabUserCredentialsDto userCreds = gitUserService.ensureGitLabUserExists(gitlabUsername, userInfo.getEmail(), userInfo.getFullName());
            log.info("GitLab用户 {} (平台ID: {}) 已确认存在并处于可操作状态。", gitlabUsername, platformUserId);

            // 3. 如果是新用户（即ensureGitLabUserExists返回了临时密码），则发送包含临时密码的邮件
            if (userCreds.getTempPassword() != null) {
                emailService.sendGitLabUserCredentialsEmail(userInfo.getEmail(), userCreds, true);
                log.info("已为新创建的GitLab用户 {} 发送凭证邮件。", gitlabUsername);
            }
            // 4. 添加或更新GitLab项目成员的权限
            // gitProjectService.addProjectMember 内部已处理成员存在性检查和权限更新
            gitProjectService.addProjectMember(gitlabProjectId, gitlabUsername, accessLevel.value);
            log.info("已添加/更新平台用户 {} (GitLab用户名: {}) 为GitLab项目 {} 的 {}, 权限级别: {}",
                     userInfo.getFullName() != null ? userInfo.getFullName() : gitlabUsername,
                     gitlabUsername, gitlabProjectId, roleDescription, accessLevel);
            // 5. 发送项目访问信息邮件
            GitLabProjectAccessInfo accessInfo = new GitLabProjectAccessInfo()
                    .setGitlabProjectName(gitlabProject.getName())
                    .**********************(gitlabProject.getWebUrl())
                    .setGitlabProjectCloneUrl(gitlabProject.getHttpUrlToRepo())
                    .setGitlabUsername(gitlabUsername)
                    .setEmail(userInfo.getEmail());
            emailService.sendGitLabProjectAccessEmail(userInfo.getEmail(), accessInfo);
            log.info("已为GitLab用户 {} 发送项目访问邮件。", gitlabUsername);
        } catch (BizException e) {
            // BizException 已经封装了具体的业务错误信息，直接抛出
            log.error("为平台用户ID {} 分配到GitLab项目ID {} (角色: {}) 失败: Code={}, Message='{}'",
                      platformUserId, gitlabProjectId, roleDescription, e.getCode(), e.getMessage(), e);
            throw e;
        } catch (GitLabApiException e) {
            // 捕获可能从 gitProjectService.addProjectMember 抛出的 GitLabApiException
            log.error("GitLab API操作失败：为平台用户ID {} 分配到GitLab项目ID {} 失败: {}",
                      platformUserId, gitlabProjectId, e.getMessage(), e);
            throw new BizException("GitLab API操作失败: " + e.getMessage(), e);
        } catch (Exception e) {
            // 捕获其他未知系统异常
            log.error("处理平台用户ID {} (GitLab用户名: {}) 分配到GitLab项目ID {} 时发生未知系统错误: {}",
                      platformUserId, gitlabUsername, gitlabProjectId, e.getMessage(), e);
            throw new BizException("为GitLab项目分配成员时发生未知错误。", e);
        }
    }

    /**
     * 为指定任务关联的GitLab项目批量添加成员。
     *
     * @param assignRequest 包含任务ID、用户ID列表和访问级别
     * @throws BizException If mapping does not exist or GitLab user operation fails.
     */
    @Override
    public void assignMembersToTaskGitlabProject(TaskMemberAssignRequestDto assignRequest) {
        log.info("开始为任务ID: {} 关联的GitLab项目批量添加成员...", assignRequest.getPlatformTaskId());
        // 1. 根据 platformTaskId 找到关联的 gitlabProjectId
        List<ProjectTaskGitlab> mappings = projectTaskGitlabMapper.********************(assignRequest.getPlatformTaskId());
        if (CollectionUtils.isEmpty(mappings)) {
            throw new BizException("未找到平台任务ID: " + assignRequest.getPlatformTaskId() + " 对应的GitLab项目映射关系。");
        }
        List<Long> gitlabProjectIds = mappings.stream()
                .map(ProjectTaskGitlab::getGitlabProjectId)
                .filter(java.util.Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(gitlabProjectIds)) {
            throw new BizException("平台任务ID: " + assignRequest.getPlatformTaskId() + " 关联的GitLab项目ID为空。");
        }
        AccessLevel accessLevel = AccessLevel.forValue(assignRequest.getGitlabAccessLevelValue());
        if (accessLevel == null) {
            throw new BizException("无效的GitLab访问级别值: " + assignRequest.getGitlabAccessLevelValue());
        }
        String roleDesc = StringUtils.isNotBlank(assignRequest.getRoleDescriptionForLog()) ?
                          assignRequest.getRoleDescriptionForLog() : accessLevel.toString();
        // 优化：批量查询用户信息，避免N+1查询
        List<UserInfo> userInfos = userInfoMapper.findByIds(assignRequest.getPlatformUserIds());
        Map<Long, UserInfo> userInfoMap = userInfos.stream()
                .collect(Collectors.toMap(UserInfo::getId, Function.identity()));

        for (Long platformUserId : assignRequest.getPlatformUserIds()) {
            UserInfo userInfo = userInfoMap.get(platformUserId);
            if (userInfo == null || StringUtils.isBlank(userInfo.getUserName()) || StringUtils.isBlank(userInfo.getEmail())) {
                log.warn("平台用户ID: {} 不存在，或其平台用户名/邮箱为空，无法添加到GitLab项目。", platformUserId);
                continue;
            }
            if ("admin".equals(userInfo.getUserName()) || "root".equals(userInfo.getUserName())) {
                continue;
            }
            String gitlabUsername = userInfo.getUserName();
            // 确保GitLab用户存在且处于活跃状态
            GitLabUserCredentialsDto userCreds = gitUserService.ensureGitLabUserExists(gitlabUsername, userInfo.getEmail(), userInfo.getFullName());
            // 如果是新用户，发送凭证邮件
            if (userCreds.getTempPassword() != null) {
                emailService.sendGitLabUserCredentialsEmail(userInfo.getEmail(), userCreds, true);
            }
            for (Long gitlabProjectId : gitlabProjectIds) {
                try {
                    // 获取GitLab项目详情，以便在邮件中包含项目URL等信息
                    Project gitlabProject = gitLabApi.getProjectApi().getProject(gitlabProjectId);
                    gitProjectService.addProjectMember(gitlabProjectId, gitlabUsername, assignRequest.getGitlabAccessLevelValue());
                    log.info("已添加/更新平台用户 {} (GitLab用户名: {}) 为GitLab项目 {} 的 {}, 权限级别: {}",
                             userInfo.getFullName() != null ? userInfo.getFullName() : gitlabUsername,
                             gitlabUsername, gitlabProjectId, roleDesc, accessLevel);
                    // 发送项目访问信息邮件
                    GitLabProjectAccessInfo accessInfo = new GitLabProjectAccessInfo()
                            .setGitlabProjectName(gitlabProject.getName())
                            .**********************(gitlabProject.getWebUrl())
                            .setGitlabProjectCloneUrl(gitlabProject.getHttpUrlToRepo())
                            .setGitlabUsername(gitlabUsername)
                            .setEmail(userInfo.getEmail());
                    emailService.sendGitLabProjectAccessEmail(userInfo.getEmail(), accessInfo);
                } catch (GitLabApiException e) {
                    log.error("为平台用户ID {} 分配到GitLab项目ID {} 失败: {}", platformUserId, gitlabProjectId, e.getMessage(), e);
                    // 这里不中断，继续为其他项目添加，或者根据业务逻辑决定
                }
            }
        }
        log.info("为任务ID: {} 关联的GitLab项目ID: {} 的成员分配操作完成。", assignRequest.getPlatformTaskId(), gitlabProjectIds);
    }

    /**
     * 从指定任务关联的GitLab项目批量移除成员。
     *
     * @param removeRequest 包含任务ID和用户ID列表
     * @throws BizException If mapping does not exist.
     */
    @Override
    public void removeMembersFromTaskGitlabProject(TaskMemberRemoveRequestDto removeRequest) {
        log.info("开始从任务ID: {} 关联的GitLab项目批量移除成员...", removeRequest.getPlatformTaskId());
        List<ProjectTaskGitlab> mappings = projectTaskGitlabMapper.********************(removeRequest.getPlatformTaskId());
        if (CollectionUtils.isEmpty(mappings)) {
            throw new BizException("未找到平台任务ID: " + removeRequest.getPlatformTaskId() + " 对应的GitLab项目映射关系。");
        }
        List<Long> gitlabProjectIds = mappings.stream()
                .map(ProjectTaskGitlab::getGitlabProjectId)
                .filter(java.util.Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(gitlabProjectIds)) {
            throw new BizException("平台任务ID: " + removeRequest.getPlatformTaskId() + " 关联的GitLab项目ID为空。");
        }
        // 优化：批量查询用户信息，避免N+1查询
        List<UserInfo> userInfos = userInfoMapper.findByIds(removeRequest.getPlatformUserIds());
        Map<Long, UserInfo> userInfoMap = userInfos.stream()
                .collect(Collectors.toMap(UserInfo::getId, Function.identity()));

        for (Long userId : removeRequest.getPlatformUserIds()) {
            UserInfo userInfo = userInfoMap.get(userId);
            if (userInfo == null || StringUtils.isBlank(userInfo.getUserName())) {
                log.warn("平台用户ID: {} 不存在或其平台用户名为空，无法从GitLab项目 {} (任务ID {}) 移除。",
                         userId, gitlabProjectIds, removeRequest.getPlatformTaskId());
                continue;
            }
            String gitlabUsername = userInfo.getUserName();
            for (Long gitlabProjectId : gitlabProjectIds) {
                try {
                    gitProjectService.removeProjectMember(gitlabProjectId, gitlabUsername);
                    log.info("已从GitLab项目ID: {} (任务ID: {}) 移除成员: {} (平台用户ID: {})",
                             gitlabProjectId, removeRequest.getPlatformTaskId(), gitlabUsername, userId);
                } catch (GitLabApiException e) {
                    if (e.getHttpStatus() == 404) {
                        log.warn("尝试从GitLab项目 {} 移除用户 {} 时，用户未找到或不是成员: {}",
                                 gitlabProjectId, gitlabUsername, e.getMessage());
                    } else {
                        log.error("从GitLab项目ID: {} (任务ID: {}) 移除成员: {} 失败: {}",
                                  gitlabProjectId, removeRequest.getPlatformTaskId(), gitlabUsername, e.getMessage(), e);
                        throw new BizException("从GitLab项目移除成员失败: " + e.getMessage(), e);
                    }
                }
            }
        }
        log.info("从任务ID: {} 关联的GitLab项目ID: {} 的成员移除操作完成。", removeRequest.getPlatformTaskId(), gitlabProjectIds);
    }

    /**
     * 根据平台任务ID查询其关联的GitLab项目仓库信息。
     * 如果一个任务可能关联多个GitLab项目，此方法将返回一个列表。
     * 此接口将用于前端展示任务关联的所有仓库，包括主仓库和额外仓库。
     *
     * @param platformTaskId 平台任务ID
     * @return 关联的GitLab项目信息列表，如果未找到映射或GitLab项目则返回空列表
     */
    @Override
    public List<GitLabProjectInfoDto> getGitLabProjectsByPlatformTaskId(Long platformTaskId) {
        log.info("开始根据平台任务ID: {} 查询关联的GitLab项目信息...", platformTaskId);
        List<ProjectTaskGitlab> taskGitlabList = projectTaskGitlabMapper.********************(platformTaskId);
        if (CollectionUtils.isEmpty(taskGitlabList)) {
            log.info("未找到平台任务ID: {} 对应的GitLab项目映射关系。", platformTaskId);
            return Collections.emptyList();
        }
        // 收集所有需要查询的 GitLab Project ID
        Set<Long> gitlabProjectIds = taskGitlabList.stream()
                .map(ProjectTaskGitlab::getGitlabProjectId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(gitlabProjectIds)) {
            log.info("平台任务ID: {} 关联的GitLab项目ID为空，无法查询项目详情。", platformTaskId);
            return Collections.emptyList();
        }
        // 并行获取 GitLab 项目详情
        Map<Long, Project> gitlabProjectDetails = new ConcurrentHashMap<>();
        List<CompletableFuture<Void>> futures = gitlabProjectIds.stream()
                .map(pId -> CompletableFuture.runAsync(() -> {
                    try {
                        Project project = gitLabApi.getProjectApi().getProject(pId);
                        gitlabProjectDetails.put(pId, project);
                    } catch (GitLabApiException e) {
                        if (e.getHttpStatus() == 404) {
                            log.warn("GitLab项目ID: {} 未找到: {}", pId, e.getMessage());
                        } else {
                            log.error("获取GitLab项目ID: {} 详情失败: {}", pId, e.getMessage(), e);
                        }
                    }
                }, NYBC_EXECUTOR))
                .toList();

        // 等待所有并行任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        List<GitLabProjectInfoDto> resultList = new ArrayList<>();
        for (ProjectTaskGitlab projectTaskGitlab : taskGitlabList) {
            Long currentGitlabProjectId = projectTaskGitlab.getGitlabProjectId();
            Project gitlabProject = gitlabProjectDetails.get(currentGitlabProjectId);
            if (gitlabProject != null) {
                GitLabProjectInfoDto dto = new GitLabProjectInfoDto();
                dto.setGitlabProjectId(gitlabProject.getId());
                dto.setName(gitlabProject.getName());
                dto.setPathWithNamespace(gitlabProject.getPathWithNamespace());
                dto.setDescription(gitlabProject.getDescription());
                dto.setWebUrl(gitlabProject.getWebUrl());
                dto.setHttpUrlToRepo(gitlabProject.getHttpUrlToRepo());
                dto.setSshUrlToRepo(gitlabProject.getSshUrlToRepo());
                dto.setDefaultBranch(gitlabProject.getDefaultBranch());
                if (gitlabProject.getCreatedAt() != null) {
                    dto.setGitlabCreatedAt(LocalDateTime.ofInstant(gitlabProject.getCreatedAt().toInstant(), ZoneId.systemDefault()));
                }
                if (gitlabProject.getLastActivityAt() != null) {
                    dto.setGitlabLastActivityAt(LocalDateTime.ofInstant(gitlabProject.getLastActivityAt().toInstant(), ZoneId.systemDefault()));
                }
                dto.setPlatformProjectId(projectTaskGitlab.getProjectId());
                dto.setPlatformTaskId(projectTaskGitlab.getTaskId());
                // 这里将枚举转换为字符串进行传输
                dto.setMappingType(projectTaskGitlab.getMappingType() != null ? projectTaskGitlab.getMappingType().name() : null);
                resultList.add(dto);
                log.debug("成功获取任务ID: {} 关联的GitLab项目详情: {}", platformTaskId, gitlabProject.getName());
            } else {
                log.warn("GitLab项目ID: {} (映射ID: {}, 任务ID: {}) 未能获取到详情，可能在GitLab中不存在或查询失败。",
                         currentGitlabProjectId, projectTaskGitlab.getId(), platformTaskId);
            }
        }
        log.info("为平台任务ID: {} 查询到 {} 个关联的GitLab项目信息。", platformTaskId, resultList.size());
        return resultList;
    }

    /**
     * 根据平台任务ID和关联的GitLab项目ID，查询仓库指定路径下的文件/目录树。
     *
     * @param platformTaskId  平台任务ID
     * @param gitlabProjectId GitLab项目ID (用于校验映射)
     * @param treeRequest     包含路径、分支/引用、是否递归等查询参数的DTO
     * @return 仓库树节点列表
     * @throws GitLabApiException If GitLab API operation fails.
     * @throws BizException       If business logic validation fails.
     */
    @Override
    public List<GitLabRepositoryNodeDto> getRepositoryTree(Long platformTaskId, Long gitlabProjectId, GitLabRepositoryTreeRequestDto treeRequest) throws GitLabApiException {
        validateTaskGitLabMapping(platformTaskId, gitlabProjectId);
        log.info("查询GitLab项目ID: {} (关联任务ID: {}) 的目录树。路径: '{}', 分支/引用: '{}', 是否递归: {}",
                 gitlabProjectId, platformTaskId, treeRequest.getPath(), treeRequest.getRef(), treeRequest.getRecursive());

        // 优化：使用缓存服务获取目录树
        Project project = gitLabApi.getProjectApi().getProject(gitlabProjectId);
        String actualRef = StringUtils.isNotBlank(treeRequest.getRef()) ? treeRequest.getRef() : project.getDefaultBranch();
        if (StringUtils.isBlank(actualRef)) {
            actualRef = "main";
            log.warn("GitLab项目ID: {} 默认分支为空，尝试使用 '{}' 作为引用。", gitlabProjectId, actualRef);
        }

        List<GitLabRepositoryNodeDto> resultNodes = gitLabRepositoryCacheService.getRepositoryTreeCached(
                gitlabProjectId,
                treeRequest.getPath(),
                actualRef,
                treeRequest.getRecursive()
                                                                                                        );

        log.info("为GitLab项目ID: {} (关联任务ID: {}) 查询目录树完成，共 {} 个节点。", gitlabProjectId, platformTaskId, resultNodes.size());
        return resultNodes;
    }

    /**
     * 根据平台任务ID、关联的GitLab项目ID和文件路径，查询文件内容。
     *
     * @param platformTaskId  平台任务ID
     * @param gitlabProjectId GitLab项目ID (用于校验映射)
     * @param filePath        要查询内容的文件在仓库中的完整路径
     * @param ref             文件所在的分支、标签或提交SHA
     * @return 包含文件内容信息的DTO
     * @throws GitLabApiException If GitLab API operation fails.
     */
    @Override
    public GitLabFileContentDto getRepositoryFileContent(Long platformTaskId, Long gitlabProjectId, String filePath, String ref) throws GitLabApiException {
        validateTaskGitLabMapping(platformTaskId, gitlabProjectId);
        log.info("查询GitLab项目ID: {} (关联任务ID: {}) 中文件 '{}' (分支/引用: '{}') 的内容。",
                 gitlabProjectId, platformTaskId, filePath, ref);

        // 优化：使用缓存服务获取文件内容
        Project project = gitLabApi.getProjectApi().getProject(gitlabProjectId);
        String actualRef = StringUtils.isNotBlank(ref) ? ref : project.getDefaultBranch();
        if (StringUtils.isBlank(actualRef)) {
            throw new BizException("无法确定文件的引用(分支/标签/提交)，项目默认分支也未设置。");
        }

        GitLabFileContentDto contentDto = gitLabRepositoryCacheService.getFileContentCached(
                gitlabProjectId,
                filePath,
                actualRef
                                                                                           );

        return contentDto;
    }

    /**
     * 根据平台任务ID查询其关联的GitLab项目仓库信息。
     * 如果一个任务可能关联多个GitLab项目，此方法将返回一个列表。
     * 此接口将用于前端展示任务关联的所有仓库，包括主仓库和额外仓库。
     *
     * @param platformTaskId 平台任务ID
     * @return 关联的GitLab项目信息列表，如果未找到映射或GitLab项目则返回空列表
     */
    @Override
    public List<AssociatedGitlabRepoDto> getAssociatedGitLabProjects(Long platformTaskId) {
        log.info("开始根据平台任务ID: {} 查询关联的所有GitLab项目信息（使用数据库缓存）...", platformTaskId);
        List<ProjectTaskGitlab> taskGitlabList = projectTaskGitlabMapper.********************(platformTaskId);
        if (CollectionUtils.isEmpty(taskGitlabList)) {
            log.info("未找到平台任务ID: {} 对应的GitLab项目映射关系。", platformTaskId);
            return Collections.emptyList();
        }

        List<AssociatedGitlabRepoDto> resultList = new ArrayList<>();
        for (ProjectTaskGitlab mapping : taskGitlabList) {
            // 直接从数据库缓存字段构建返回对象，避免调用GitLab API
            AssociatedGitlabRepoDto dto = new AssociatedGitlabRepoDto();
            dto.setId(mapping.getId());
            dto.setPlatformProjectId(mapping.getProjectId());
            dto.setPlatformTaskId(mapping.getTaskId());
            dto.setGitlabProjectId(mapping.getGitlabProjectId());
            dto.********************(mapping.getGitlabProjectPath());
            dto.setGitlabProjectName(mapping.getGitlabProjectName());
            dto.setGitlabProjectDescription(mapping.getGitlabProjectDescription());
            dto.**********************(mapping.getGitlabProjectWebUrl());
            dto.setGitlabProjectHttpUrlToRepo(mapping.getGitlabProjectHttpUrlToRepo());
            dto.setGitlabProjectSshUrlToRepo(mapping.getGitlabProjectSshUrlToRepo());
            dto.setDefaultBranch(mapping.getGitlabProjectDefaultBranch());
            dto.setVisibility(mapping.getGitlabProjectVisibility());
            dto.setCreatedAt(mapping.getGitlabProjectCreatedAt());
            dto.setLastActivityAt(mapping.getGitlabProjectLastActivityAt());
            dto.setGitlabTemplateType(mapping.getMappingType() != null ? mapping.getMappingType().name() : null);
            dto.setMappingTypeDescription(mapping.getMappingType() != null ? mapping.getMappingType().getDescription() : null);
            dto.setCreateTime(mapping.getCreateTime());
            resultList.add(dto);
            log.debug("从数据库缓存获取任务ID: {} 关联的GitLab项目: {}", platformTaskId, mapping.getGitlabProjectName());
        }

        log.info("成功从数据库缓存获取平台任务ID: {} 关联的 {} 个GitLab项目信息", platformTaskId, resultList.size());
        return resultList;
    }

    /**
     * START 修改：获取指定GitLab项目的分支列表。
     *
     * @param query 包含平台任务ID (用于权限校验) 和 GitLab项目ID
     * @return 分支名称列表
     * @throws BizException 如果GitLab项目不存在或操作失败
     */
    @Override
    public List<String> getGitLabBranches(GitLogQuery query) {
        validateTaskGitLabMapping(query.getPlatformTaskId(), query.getGitlabProjectId());
        log.info("获取GitLab项目ID: {} (关联任务ID: {}) 的分支列表。", query.getGitlabProjectId(), query.getPlatformTaskId());
        try {
            List<Branch> branches = gitLabApi.getRepositoryApi().getBranches(query.getGitlabProjectId());
            return branches.stream()
                    .map(Branch::getName)
                    .collect(Collectors.toList());
        } catch (GitLabApiException e) {
            log.error("获取GitLab项目ID: {} 的分支列表失败: {}", query.getGitlabProjectId(), e.getMessage(), e);
            throw new BizException("获取分支列表失败。", e);
        }
    }

    /**
     * 辅助方法：将模板文件内容注入到新创建的GitLab项目。
     *
     * @param gitlabProjectId 新创建的GitLab项目ID。
     * @param templateType    模板类型（字符串名称），用于确定加载哪个目录下的模板文件。
     * @param targetBranch    目标分支名称，模板文件将提交到此分支。
     */
    private void applyProjectTemplate(Long gitlabProjectId, String templateType, String targetBranch) {
        log.info("开始为GitLab项目ID: {} 注入模板。模板类型: '{}', 目标分支: '{}'", gitlabProjectId, templateType, targetBranch);
        try {
            Map<String, String> templateFiles = templateFileLoader.loadTemplateFiles(templateType);

            if (templateFiles.isEmpty()) {
                log.warn("模板类型 '{}' 未找到任何文件，跳过模板注入。", templateType);
                return;
            }
            // 为所有的文件创建一个单独的提交，而不是每个文件一个提交，提高效率并保持Git历史清晰
            List<CommitAction> commitActions = new ArrayList<>();
            for (Map.Entry<String, String> entry : templateFiles.entrySet()) {
                String filePath = entry.getKey();
                String fileContent = entry.getValue();
                // 使用 CREATE 动作，如果文件不存在则创建
                commitActions.add(new CommitAction()
                                          .withAction(CommitAction.Action.CREATE)
                                          .withFilePath(filePath)
                                          .withContent(fileContent)
                                          .withEncoding(Constants.Encoding.TEXT));
                log.debug("准备添加模板文件到提交: {}", filePath);
            }

            gitLabApi.getCommitsApi().createCommit(
                    gitlabProjectId,
                    targetBranch,
                    "feat: 初始化项目模版文件数据[ " + templateType + " ]", null, null, null,
                    commitActions);
            log.info("GitLab项目ID: {} 模板注入完成，共提交 {} 个文件到分支 '{}'。", gitlabProjectId, commitActions.size(), targetBranch);
        } catch (IOException e) {
            log.error("加载模板文件时发生IO错误，无法注入模板到项目ID {}: {}", gitlabProjectId, e.getMessage(), e);
            throw new BizException("加载模板文件失败，无法完成项目初始化。", e);
        } catch (GitLabApiException e) {
            log.error("通过GitLab API注入模板文件到项目ID {} 失败: {}", gitlabProjectId, e.getMessage(), e);
            throw new BizException("注入模板文件失败：GitLab API错误。" + e.getMessage(), e);
        } catch (Exception e) {
            log.error("注入模板到项目ID {} 时发生未知错误: {}", gitlabProjectId, e.getMessage(), e);
            throw new BizException("项目模板注入失败。", e);
        }
    }

    /**
     * 获取指定GitLab项目的提交历史。
     * 此方法将用于前端展示代码仓库的提交记录。
     *
     * @param query
     * @return 分页的提交历史列表
     * @throws BizException 如果GitLab项目不存在或操作失败
     */
    @Override
    public PageResult<CommitHistoryDto> getCommitHistory(GitLogQuery query) {
        validateTaskGitLabMapping(query.getPlatformTaskId(), query.getGitlabProjectId());
        log.info("获取GitLab项目ID: {} (关联任务ID: {}) 的提交历史。分支/引用: '{}', 页码: {}, 每页大小: {}",
                 query.getGitlabProjectId(), query.getPlatformTaskId(), query.getRef(), query.getPageNum(), query.getPageSize());

        // 获取平台用户邮箱映射缓存
        Map<String, UserInfo> platformUserEmailMapping = gitStatsService.getPlatformUserEmailMapping();

        try {
            int page = query.getPageNum() != null ? query.getPageNum() : 1;
            int pageSize = query.getPageSize() != null ? query.getPageSize() : 30;
            String ref = query.getRef();
            List<Commit> allCommits = getCommitsStreamViaPager(
                    query.getGitlabProjectId(),
                    ref, query.getSince(), query.getUntil(), null).toList();
            long totalCommits = allCommits.size();
            int totalPages = (int) Math.ceil((double) totalCommits / pageSize);

            // 计算当前页的起始和结束索引
            int startIndex = (page - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, (int) totalCommits);

            List<CommitHistoryDto> commitHistoryList;
            if (startIndex < totalCommits) {
                commitHistoryList = allCommits.subList(startIndex, endIndex).stream()
                        .map(commit -> {
                            CommitHistoryDto dto = new CommitHistoryDto();
                            dto.setId(commit.getId());
                            dto.setShortId(commit.getShortId());
                            dto.setMessage(commit.getMessage());

                            // **关键修改：映射作者姓名**
                            String authorEmail = commit.getAuthorEmail();
                            UserInfo authorInfo = platformUserEmailMapping.get(authorEmail);
                            dto.setAuthorName((authorInfo != null && StringUtils.isNotBlank(authorInfo.getNickName())) ? authorInfo.getNickName() : commit.getAuthorName()); // 优先平台昵称，其次 GitLab 作者名
                            dto.setAuthorEmail(authorEmail); // 保留邮箱

                            // **关键修改：映射提交者姓名**
                            String committerEmail = commit.getCommitterEmail();
                            UserInfo committerInfo = platformUserEmailMapping.get(committerEmail);
                            dto.setCommitterName((committerInfo != null && StringUtils.isNotBlank(committerInfo.getNickName())) ? committerInfo.getNickName() : commit.getCommitterName()); // 优先平台昵称，其次 GitLab 提交者名
                            dto.setCommitterEmail(committerEmail); // 保留邮箱

                            dto.setCommittedDate(commit.getCommittedDate() != null ? LocalDateTime.ofInstant(commit.getCommittedDate().toInstant(), ZoneId.systemDefault()) : null);
                            dto.setAuthoredDate(commit.getAuthoredDate() != null ? LocalDateTime.ofInstant(commit.getAuthoredDate().toInstant(), ZoneId.systemDefault()) : null);
                            return dto;
                        }).collect(Collectors.toList());
            } else {
                commitHistoryList = Collections.emptyList();
            }

            PageInfo<CommitHistoryDto> pageInfo = new PageInfo<>(commitHistoryList);
            pageInfo.setPageNum(page);
            pageInfo.setPageSize(pageSize);
            pageInfo.setTotal(totalCommits); // 设置总条数
            pageInfo.setPages(totalPages);   // 设置总页数
            return PageResult.result(pageInfo);
        } catch (GitLabApiException e) {
            log.error("获取GitLab项目ID: {} 的提交历史失败: {}", query.getGitlabProjectId(), e.getMessage(), e);
            throw new BizException("获取提交历史失败。", e);
        }
    }


    /**
     * 辅助方法：批量添加成员到GitLab项目。
     *
     * @param gitlabProject   GitLab项目对象
     * @param platformUserIds 平台用户ID列表
     * @param accessLevel     GitLab访问级别
     * @param roleDescription 角色描述（用于日志）
     * @throws BizException 如果平台用户不存在或GitLab用户操作失败
     */
    private void addMembersToGitLabProject(Project gitlabProject, List<Long> platformUserIds, AccessLevel accessLevel, String roleDescription) {
        if (CollectionUtils.isEmpty(platformUserIds)) {
            return;
        }
        // 优化：批量查询用户信息，避免N+1查询
        List<UserInfo> userInfos = userInfoMapper.findByIds(platformUserIds);
        Map<Long, UserInfo> userInfoMap = userInfos.stream()
                .collect(Collectors.toMap(UserInfo::getId, Function.identity()));

        for (Long platformUserId : platformUserIds) {
            UserInfo userInfo = userInfoMap.get(platformUserId);
            if (userInfo == null || StringUtils.isBlank(userInfo.getUserName()) || StringUtils.isBlank(userInfo.getEmail())) {
                log.warn("平台用户ID: {} 不存在，或其平台用户名/邮箱为空，无法添加到GitLab项目 {} 作为 {}",
                         platformUserId, gitlabProject.getId(), roleDescription);
                continue;
            }
            String gitlabUsername = userInfo.getUserName();
            if (!"admin".equals(gitlabUsername) && !userInfo.getEmail().startsWith("admin@")) {
                try {
                    // 确保GitLab用户存在且处于活跃状态，并获取凭证信息（包含临时密码，如果新创建）
                    GitLabUserCredentialsDto userCreds = gitUserService.ensureGitLabUserExists(gitlabUsername, userInfo.getEmail(), userInfo.getFullName());
                    // 如果是新用户（即ensureGitLabUserExists返回了临时密码），则发送包含临时密码的邮件
                    if (userCreds.getTempPassword() != null) {
                        emailService.sendGitLabUserCredentialsEmail(userInfo.getEmail(), userCreds, true);
                    }
                    // 添加或更新GitLab项目成员的权限
                    gitProjectService.addProjectMember(gitlabProject.getId(), gitlabUsername, accessLevel.value);
                    log.info("已添加/更新平台用户 {} (GitLab用户名: {}) 为GitLab项目 {} 的 {}, 权限级别: {}",
                             userInfo.getFullName() != null ? userInfo.getFullName() : gitlabUsername,
                             gitlabUsername, gitlabProject.getId(), roleDescription, accessLevel);

                    // 发送项目访问信息邮件
                    GitLabProjectAccessInfo accessInfo = new GitLabProjectAccessInfo()
                            .setGitlabProjectName(gitlabProject.getName())
                            .**********************(gitlabProject.getWebUrl())
                            .setGitlabProjectCloneUrl(gitlabProject.getHttpUrlToRepo())
                            .setGitlabUsername(gitlabUsername)
                            .setEmail(userInfo.getEmail());
                    emailService.sendGitLabProjectAccessEmail(userInfo.getEmail(), accessInfo);
                } catch (BizException e) {
                    log.error("为平台用户ID {} 分配到GitLab项目ID {} (角色: {}) 失败: {}",
                              platformUserId, gitlabProject.getId(), roleDescription, e.getMessage(), e);
                    throw e;
                } catch (GitLabApiException e) {
                    log.error("GitLab API操作失败：为平台用户ID {} 分配到GitLab项目ID {} 失败: {}",
                              platformUserId, gitlabProject.getId(), e.getMessage(), e);
                    throw new BizException("GitLab API操作失败: " + e.getMessage(), e);
                } catch (Exception e) {
                    log.error("处理平台用户ID {} (GitLab用户名: {}) 分配到GitLab项目ID {} 时发生未知系统错误: {}",
                              platformUserId, gitlabUsername, gitlabProject.getId(), e.getMessage(), e);
                    throw new BizException("为GitLab项目分配成员时发生未知错误。", e);
                }
            }

        }
    }


    /**
     * 校验任务与GitLab项目的映射关系是否存在且有效。
     *
     * @param platformTaskId  平台任务ID
     * @param gitlabProjectId 期望关联的GitLab项目ID
     * @return 有效的ProjectTaskGitlab映射对象
     * @throws BizException 如果映射不存在或与传入的gitlabProjectId不符
     */
    private ProjectTaskGitlab validateTaskGitLabMapping(Long platformTaskId, Long gitlabProjectId) {
        if (platformTaskId == null || gitlabProjectId == null) {
            throw new BizException("平台任务ID和GitLab项目ID都不能为空。");
        }
        List<ProjectTaskGitlab> mappings = projectTaskGitlabMapper.********************(platformTaskId);
        if (CollectionUtils.isEmpty(mappings)) {
            throw new BizException("未找到平台任务ID: " + platformTaskId + " 对应的GitLab项目映射关系。");
        }
        ProjectTaskGitlab mapping = mappings.stream()
                .filter(m -> gitlabProjectId.equals(m.getGitlabProjectId()))
                .findFirst()
                .orElse(null);
        if (mapping == null) {
            throw new BizException("平台任务ID: " + platformTaskId + " 与 GitLab项目ID: " + gitlabProjectId + " 的映射关系不存在或无效。");
        }
        if (mapping.getDeleted() != null && mapping.getDeleted() == DELETE) {
            throw new BizException("平台任务ID: " + platformTaskId + " 与 GitLab项目ID: " + gitlabProjectId + " 的映射关系已失效。");
        }
        return mapping;
    }

    /**
     * 获取指定项目在给定条件下所有提交的流式迭代器。
     * 此方法设计用于需要处理所有（或大量）提交的场景，例如数据统计、分析等，
     * 通过返回 Stream，避免一次性将所有 Commit 加载到内存，从而减少内存压力。
     *
     * @param projectIdOrPath 项目ID或路径。
     * @param refName         分支、标签或提交SHA，如果为null则使用默认分支。
     * @param since           只返回在此日期之后或此日期上的提交。
     * @param until           只返回在此日期之前或此日期上的提交。
     * @param filePath        只返回影响此文件路径的提交。
     * @return 包含提交的Stream。
     * @throws GitLabApiException GitLab API异常。
     */
    private Stream<Commit> getCommitsStreamViaPager(Object projectIdOrPath, String refName, Date since, Date until, String filePath) throws GitLabApiException {
        CommitsApi commitsApi = gitLabApi.getCommitsApi();
        int itemsPerPage = 100;
        Pager<Commit> pager;
        try {
            pager = commitsApi.getCommits(projectIdOrPath, refName, since, until, filePath, false, false, false, itemsPerPage);
        } catch (GitLabApiException e) {
            log.error("获取项目ID {} 的Commits Pager失败: {}", projectIdOrPath, e.getMessage(), e);
            return Stream.empty();
        }
        return StreamSupport.stream(pager.stream().spliterator(), false);
    }

    /**
     * 获取指定GitLab项目下，特定文件路径的提交历史（作为文件版本）。
     *
     * @param query 包含平台任务ID、GitLab项目ID、文件路径、分支等查询参数的DTO。
     * @return 分页的Commit历史列表，每个提交可视为文件的一个版本。
     * @throws BizException 如果GitLab项目不存在或操作失败。
     */
    @Override
    public PageResult<CommitHistoryDto> getCommitHistoryForFile(GitLogQuery query) {
        validateTaskGitLabMapping(query.getPlatformTaskId(), query.getGitlabProjectId());
        if (StringUtils.isBlank(query.getFilePath())) {
            throw new BizException("文件路径不能为空，无法查询文件版本历史。");
        }
        log.info("正在获取文件 '{}' 在GitLab项目ID: {} (关联任务ID: {}) 的版本历史。引用: '{}', 页码: {}, 每页大小: {}",
                 query.getFilePath(), query.getGitlabProjectId(), query.getPlatformTaskId(), query.getRef(), query.getPageNum(), query.getPageSize());

        try {
            int page = query.getPageNum() != null ? query.getPageNum() : 1;
            int pageSize = query.getPageSize() != null ? query.getPageSize() : 100;
            String ref = query.getRef();
            String filePath = query.getFilePath();
            Pager<Commit> pager = gitLabApi.getCommitsApi().getCommits(
                    query.getGitlabProjectId(),
                    ref,
                    query.getSince(),
                    query.getUntil(),
                    filePath,
                    false,
                    null,
                    null,
                    pageSize);
            List<Commit> currentPageCommits = pager.current();
            List<CommitHistoryDto> commitHistoryList = currentPageCommits.stream()
                    .map(commit -> new CommitHistoryDto()
                            .setId(commit.getId())
                            .setShortId(commit.getShortId())
                            .setMessage(commit.getMessage())
                            .setAuthorName(commit.getAuthorName())
                            .setAuthorEmail(commit.getAuthorEmail())
                            .setCommittedDate(commit.getCommittedDate() != null ? LocalDateTime.ofInstant(commit.getCommittedDate().toInstant(), ZoneId.systemDefault()) : null)
                            .setCommitterName(commit.getCommitterName())
                            .setCommitterEmail(commit.getCommitterEmail())
                            .setAuthoredDate(commit.getAuthoredDate() != null ? LocalDateTime.ofInstant(commit.getAuthoredDate().toInstant(), ZoneId.systemDefault()) : null)
                        ).collect(Collectors.toList());
            PageInfo<CommitHistoryDto> pageInfo = new PageInfo<>(commitHistoryList);
            pageInfo.setPageNum(page);
            pageInfo.setPageSize(pageSize);
            pageInfo.setTotal(pager.getTotalItems());
            pageInfo.setPages(pager.getTotalPages());
            return PageResult.result(pageInfo);
        } catch (GitLabApiException e) {
            log.error("获取文件 '{}' 在GitLab项目ID {} 的版本历史失败: {}", query.getFilePath(), query.getGitlabProjectId(), e.getMessage(), e);
            throw new BizException("获取文件版本历史失败。", e);
        }
    }

    /**
     * 获取指定Git Commit下特定文件路径的原始文件内容流。
     *
     * @param platformTaskId  平台任务ID。
     * @param gitlabProjectId GitLab项目ID。
     * @param filePath        文件在仓库中的完整路径。
     * @param commitId        目标Commit的完整ID (SHA)。
     * @return 文件的InputStream。如果文件不存在或无法获取内容，则返回null。
     * @throws GitLabApiException 如果GitLab API操作失败（例如文件不存在于该Commit，或权限问题）。
     * @throws BizException       如果文件在指定Commit下不存在或无法访问，或映射无效。
     */
    @Override
    public InputStream getRawFileContentByCommit(Long platformTaskId, Long gitlabProjectId, String filePath, String commitId) throws GitLabApiException {
        validateTaskGitLabMapping(platformTaskId, gitlabProjectId);
        if (StringUtils.isBlank(filePath) || StringUtils.isBlank(commitId)) {
            throw new BizException("文件路径和Commit ID不能为空，无法获取文件内容。");
        }
        log.info("正在从GitLab项目ID: {} 的Commit '{}' 中获取文件 '{}' 的原始内容。", gitlabProjectId, commitId, filePath);
        try {
            InputStream rawStream = gitProjectService.getRawFileContentByCommit(gitlabProjectId, filePath, commitId);
            if (rawStream == null) {
                log.warn("GitProjectService 返回的原始文件内容流为空。文件 '{}' 在Commit '{}' 中可能不存在或无法访问。", filePath, commitId);
                throw new BizException("文件在指定Commit下未找到或内容为空。");
            }
            return rawStream;
        } catch (GitLabApiException e) {
            log.error("从GitLab获取文件 '{}' 在Commit '{}' 中的原始内容失败: {}", filePath, commitId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 恢复文件到指定Commit的状态（创建一个新的提交）。
     * 该操作将获取目标Commit下指定文件的内容，并将其作为新的内容提交到当前项目
     * 的默认分支，从而实现文件内容的回滚。GitLab4J v6+ 中，`createCommit` 接受 `CommitAction` 列表，
     * 且内容需要以 `Base64` 编码形式提供（当使用 `Encoding.BASE64` 时），或者直接文本编码（当使用 `Encoding.TEXT` 时）。
     *
     * @param platformTaskId    平台任务ID。
     * @param gitlabProjectId   GitLab项目ID。
     * @param filePath          文件在仓库中的完整路径。
     * @param commitId          目标Commit的完整ID (SHA)。
     * @param commitMessage     新的提交信息。
     * @param committerUsername 提交者用户名（GitLab用户名）。
     * @param committerEmail    提交者邮箱（GitLab用户邮箱）。
     * @throws GitLabApiException 如果GitLab API操作失败。
     * @throws BizException       如果文件无法恢复或映射无效。
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void restoreFileToCommit(Long platformTaskId, Long gitlabProjectId, String filePath, String commitId, String commitMessage, String committerUsername, String committerEmail) throws GitLabApiException {
        validateTaskGitLabMapping(platformTaskId, gitlabProjectId);
        if (StringUtils.isBlank(filePath) || StringUtils.isBlank(commitId) || StringUtils.isBlank(commitMessage)) {
            throw new BizException("文件路径、Commit ID和提交信息不能为空。");
        }
        log.info("正在将文件 '{}' 恢复到Commit '{}' 的状态，在GitLab项目ID: {} (关联任务ID: {})。",
                 filePath, commitId, gitlabProjectId, platformTaskId);
        try {
            gitProjectService.restoreFileToCommit(
                    gitlabProjectId,
                    filePath,
                    commitId,
                    commitMessage,
                    committerUsername,
                    committerEmail
                                                 );
            log.info("文件 '{}' 已成功恢复到Commit '{}' 的状态，并在GitLab项目ID {} 中创建了新的提交。", filePath, commitId, gitlabProjectId);
        } catch (GitLabApiException e) {
            log.error("恢复文件 '{}' 到Commit '{}' 的状态失败，在GitLab项目ID {}: {}", filePath, commitId, gitlabProjectId, e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("恢复文件 '{}' 到Commit '{}' 的状态时发生未知错误，在GitLab项目ID {}: {}", filePath, commitId, gitlabProjectId, e.getMessage(), e);
            throw new BizException("恢复文件版本时发生系统错误。", e);
        }
    }

    /**
     * 添加一条文件评论。
     * 该方法将根据GitLab文件版本信息，在平台内部生成或获取一个唯一的fileId，然后保存评论。
     *
     * @param request       评论请求DTO，包含GitLab文件版本信息（不含fileId）。
     * @param currentUserId 当前操作用户ID。
     * @return 添加成功后的评论DTO。
     * @throws com.nybc.edu.core.exception.BizException 如果操作失败。
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public FileCommentDto addFileComment(AddFileCommentRequestDto request, Long currentUserId) {
        // 1. 校验评论关联的平台任务与GitLab项目映射关系是否存在且有效
        validateTaskGitLabMapping(request.getPlatformTaskId(), request.getGitlabProjectId());

        // 2. 权限校验：用户是否有权限添加评论
        PermissionCheckRequestDto permissionCheckRequest = new PermissionCheckRequestDto()
                .setUserId(currentUserId)
                .setPlatformProjectId(request.********************())
                .setPlatformTaskId(request.getPlatformTaskId())
                .setGitlabProjectId(request.getGitlabProjectId())
                .setAction("file:add_comment");
        if (!projectTaskPermissionApiClient.hasPermission(permissionCheckRequest)) {
            throw new BizException(ResultCodeEnum.DATA_AUTH_ERROR.getCode(), "您没有权限添加评论。");
        }
        String gitlabBlobId;
        try {
            // 获取项目信息以确定默认分支
            Project gitlabProject = gitLabApi.getProjectApi().getProject(request.getGitlabProjectId());
            // 确定要查询的引用 (Commit ID 或默认分支)
            String ref = StringUtils.isNotBlank(request.getCommitId()) ? request.getCommitId() : gitlabProject.getDefaultBranch();
            if (StringUtils.isBlank(ref)) {
                ref = "main";
                log.warn("GitLab项目ID: {} 默认分支为空，且未指定Commit ID，使用 '{}' 作为引用获取文件Blob ID。", request.getGitlabProjectId(), ref);
            }
            // 从完整文件路径中提取父目录路径和文件名
            String filePathFromRequest = request.getGitlabFilePath();
            String parentPath;
            String fileName;
            int lastSlash = filePathFromRequest.lastIndexOf('/');
            if (lastSlash != -1) {
                parentPath = filePathFromRequest.substring(0, lastSlash);
                fileName = filePathFromRequest.substring(lastSlash + 1);
            } else {
                parentPath = "";
                fileName = filePathFromRequest;
            }
            Pager<TreeItem> treeItemsPager = gitLabApi.getRepositoryApi().getTree(request.getGitlabProjectId(), parentPath, ref, false, 100);
            List<TreeItem> returnedTreeItems = treeItemsPager.stream().toList();
            String finalRef = ref;
            TreeItem fileTreeItem = returnedTreeItems.stream()
                    .filter(item -> item.getName().equals(fileName) && "blob".equalsIgnoreCase(item.getType().toString()))
                    .findFirst()
                    .orElseThrow(() -> new BizException("未能获取文件元数据 (Blob ID)。文件 '" + fileName + "' 在路径 '" + parentPath + "' (引用: '" + finalRef + "') 下不存在或不是文件类型。"));

            if (!"blob".equalsIgnoreCase(fileTreeItem.getType().toString())) {
                throw new BizException("评论对象不是文件类型，无法生成文件评论ID。");
            }
            gitlabBlobId = fileTreeItem.getId();

        } catch (GitLabApiException e) {
            log.error("获取文件 '{}' (GitLab项目ID: {}) 的Blob ID失败：{}",
                      request.getGitlabFilePath(), request.getGitlabProjectId(), e.getMessage(), e);
            throw new BizException("无法获取GitLab文件Blob ID，评论添加失败：GitLab API错误。", e);
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error("在处理文件 '{}' (GitLab项目ID: {}) 的Blob ID时发生未知错误：{}",
                      request.getGitlabFilePath(), request.getGitlabProjectId(), e.getMessage(), e);
            throw new BizException("无法获取GitLab文件Blob ID，评论添加失败：系统错误。", e);
        }

        Long fileId = gitLabFileVersionService.ensureGitLabFileVersionId(
                request.********************(),
                request.getPlatformTaskId(),
                request.getGitlabProjectId(),
                request.getGitlabFilePath(),
                request.getCommitId(),
                gitlabBlobId,
                request.getGitlabFilePath().substring(request.getGitlabFilePath().lastIndexOf('/') + 1),
                null);

        // 4. 将 DTO 转换为实体，准备插入数据库
        ProjectTaskGitlabComment fileComment = new ProjectTaskGitlabComment();
        fileComment.setFileId(fileId);
        fileComment.setPlatformProjectId(request.********************());
        fileComment.setPlatformTaskId(request.getPlatformTaskId());
        fileComment.setGitlabProjectId(request.getGitlabProjectId());
        fileComment.setGitlabFilePath(request.getGitlabFilePath());
        fileComment.setCommentContent(request.getCommentContent());
        fileComment.setParentCommentId(request.getParentCommentId() != null && request.getParentCommentId() != 1L ? request.getParentCommentId() : null);
        fileComment.setCommentType(request.getCommentType());
        fileComment.setLineNumber(request.getLineNumber());
        fileComment.setCommitId(request.getCommitId());
        fileComment.setCreateTime(LocalDateTime.now());
        fileComment.setCreateUser(currentUserId);
        fileComment.setUpdateTime(LocalDateTime.now());
        fileComment.setUpdateUser(currentUserId);
        fileComment.setTenantId(UserHold.getTenantId());
        fileComment.setDeleted(NORMAL);
        // 5. 插入数据库
        if (fileCommentMapper.insert(fileComment) <= 0) {
            log.error("用户 {} 添加文件评论失败，文件ID: {}, GitLab路径: {}", currentUserId, fileId, request.getGitlabFilePath());
            throw new BizException("添加文件评论失败。");
        }
        // 6. 将实体转换为 DTO 返回，并填充评论人信息
        FileCommentDto fileCommentDto = FileCommentDto.fromEntity(fileComment);
        UserInfo creatorInfo = userInfoMapper.getById(currentUserId);
        UserDto commenterUserDto = null;
        if (creatorInfo != null) {
            commenterUserDto = new UserDto()
                    .setId(creatorInfo.getId())
                    .setUserName(creatorInfo.getUserName())
                    .setNickName(creatorInfo.getNickName())
                    .setAvatar(creatorInfo.getAvatarUrl());
            fileCommentDto.setCreatorInfo(commenterUserDto);
        }
        // 收集通知接收人
        Set<Long> recipientUserIds = new HashSet<>();
        // A. 如果是回复，通知父评论的作者
        if (fileComment.getParentCommentId() != null) {
            ProjectTaskGitlabComment parentComment = projectTaskGitlabCommentMapper.getById(fileComment.getParentCommentId());
            if (parentComment != null && !parentComment.getCreateUser().equals(currentUserId)) {
                recipientUserIds.add(parentComment.getCreateUser());
            }
        }
        eventPublisher.publishEvent(new FileCommentEvent(
                this,
                fileComment.getId(),
                currentUserId,
                commenterUserDto,
                request.********************(),
                request.getPlatformTaskId(),
                request.getGitlabFilePath(),
                request.getCommentContent(),
                request.getParentCommentId(),
                request.getCommentType(),
                recipientUserIds,
                UserHold.getTenantId()
        ));
        return fileCommentDto;
    }

    /**
     * 获取文件评论列表。
     *
     * @param request 请求DTO，包含GitLab文件唯一标识信息。
     * @return List of FileCommentDto (includes tree structure of replies).
     * @throws com.nybc.edu.core.exception.BizException If the operation fails.
     */
    @Override
    public List<FileCommentDto> getFileComments(GetFileCommentsRequestDto request) {
        if (request.getGitlabProjectId() == null || StringUtils.isBlank(request.getGitlabFilePath()) ||
                request.********************() == null || request.getPlatformTaskId() == null) {
            throw new BizException("GitLab项目ID、文件路径、平台项目ID和任务ID是获取评论的必需参数。");
        }
        List<ProjectTaskGitlabComment> allComments = fileCommentMapper.findAllCommentsForFile(
                request.getGitlabProjectId(), request.getGitlabFilePath());

        // 如果没有找到任何评论，直接返回空列表，避免不必要的数据库或缓存操作
        if (CollectionUtils.isEmpty(allComments)) {
            return Collections.emptyList();
        }
        // 2. 批量获取所有评论涉及的用户信息，并缓存起来，避免N+1查询
        Set<Long> userIds = allComments.stream().map(ProjectTaskGitlabComment::getCreateUser).collect(Collectors.toSet());
        Map<Long, UserInfo> userInfoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(userIds)) {
            List<UserInfo> users = userInfoMapper.findByIds(new ArrayList<>(userIds));
            users.forEach(user -> userInfoMap.put(user.getId(), user));
        }
        // 3. 将实体转换为DTO，并构建评论ID到DTO的映射，方便构建树
        Map<Long, FileCommentDto> commentDtoMap = new HashMap<>();
        for (ProjectTaskGitlabComment comment : allComments) {
            FileCommentDto dto = FileCommentDto.fromEntity(comment);
            UserInfo creatorInfo = userInfoMap.get(comment.getCreateUser());
            if (creatorInfo != null) {
                dto.setCreatorInfo(new UserDto()
                                           .setId(creatorInfo.getId())
                                           .setUserName(creatorInfo.getUserName())
                                           .setNickName(creatorInfo.getNickName())
                                           .setAvatar(creatorInfo.getAvatarUrl()));
            }
            commentDtoMap.put(dto.getId(), dto);
        }

        List<FileCommentDto> topLevelComments = new ArrayList<>();
        for (FileCommentDto dto : commentDtoMap.values()) {
            if (dto.getParentCommentId() == null) {
                topLevelComments.add(dto);
            } else {
                FileCommentDto parentDto = commentDtoMap.get(dto.getParentCommentId());
                if (parentDto != null) {
                    if (parentDto.getReplies() == null) {
                        parentDto.setReplies(new ArrayList<>());
                    }
                    parentDto.getReplies().add(dto);
                } else {
                    topLevelComments.add(dto);
                }
            }
        }
        topLevelComments.sort(Comparator.comparing(FileCommentDto::getCreateTime));
        sortRepliesRecursively(topLevelComments);
        return topLevelComments;
    }

    // 辅助方法：递归排序评论回复
    private void sortRepliesRecursively(List<FileCommentDto> comments) {
        if (CollectionUtils.isEmpty(comments)) {
            return;
        }
        for (FileCommentDto comment : comments) {
            if (CollectionUtils.isNotEmpty(comment.getReplies())) {
                comment.getReplies().sort(Comparator.comparing(FileCommentDto::getCreateTime));
                sortRepliesRecursively(comment.getReplies());
            }
        }
    }


    /**
     * 逻辑删除一条文件评论及其所有回复。
     *
     * @param request       删除评论请求DTO。
     * @param currentUserId 当前操作用户ID。
     * @throws com.nybc.edu.core.exception.BizException 如果操作失败或无权限。
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public int deleteFileComment(DeleteFileCommentRequestDto request, Long currentUserId) {
        // 1. 获取评论详情，并校验评论是否存在且未被删除
        ProjectTaskGitlabComment commentToDelete = fileCommentMapper.getById(request.getCommentId());
        if (commentToDelete == null || commentToDelete.getDeleted() == DELETE) {
            log.warn("尝试删除的评论ID: {} 不存在或已被删除。", request.getCommentId());
            return ResultCodeEnum.DATA_NOT_FOUND.getCode();
        }
        // 2. 权限校验：只有评论创建者或平台管理员可以删除评论
        if (!commentToDelete.getCreateUser().equals(currentUserId) && UserHold.isAdmin()) {
            log.warn("用户 {} 无权限删除评论ID: {}，该评论由用户 {} 创建。", currentUserId, request.getCommentId(), commentToDelete.getCreateUser());
            return ResultCodeEnum.DATA_AUTH_ERROR.getCode();
        }
        // 3. 收集所有需要删除的评论ID (包括当前评论及其所有后代评论)
        Set<Long> idsToDelete = new HashSet<>();
        idsToDelete.add(request.getCommentId());
        collectReplyIdsRecursively(request.getCommentId(), idsToDelete);
        int rowsAffected = fileCommentMapper.logicalDeleteBatch(new ArrayList<>(idsToDelete), LocalDateTime.now(), currentUserId);
        if (rowsAffected == 0) {
            log.warn("批量逻辑删除评论（ID: {} 及其后代）失败，可能记录不存在或已被删除。", request.getCommentId());
            return ResultCodeEnum.DATA_OPERATION_FAILED.getCode();
        }
        return ResultCodeEnum.SUCCESS.getCode();
    }

    // 辅助方法：递归收集所有子评论的ID
    private void collectReplyIdsRecursively(Long parentCommentId, Set<Long> idsToDelete) {
        List<ProjectTaskGitlabComment> replies = fileCommentMapper.findRepliesByParentCommentId(parentCommentId);
        if (CollectionUtils.isNotEmpty(replies)) {
            for (ProjectTaskGitlabComment reply : replies) {
                if (idsToDelete.add(reply.getId())) {
                    collectReplyIdsRecursively(reply.getId(), idsToDelete);
                }
            }
        }
    }

    /**
     * 根据GitLab文件唯一标识（项目ID、文件路径、可选Commit ID）统计评论数量。
     * 用于前端在文件列表或文件内容详情页展示评论数量。
     *
     * @param request 包含GitLab文件唯一标识信息的请求DTO。
     * @return 符合条件的评论数量。
     */
    @Override
    public Long countFileComments(GetFileCommentsRequestDto request) {
        if (request.getGitlabProjectId() == null || StringUtils.isBlank(request.getGitlabFilePath())) {
            throw new BizException("GitLab项目ID和文件路径是统计评论数量的必需参数。");
        }
        log.info("统计评论数量：GitLab项目ID: {}, 文件路径: '{}', Commit ID: {}",
                 request.getGitlabProjectId(), request.getGitlabFilePath(), request.getCommitId());
        return fileCommentMapper.countCommentsForFileWithoutCommit(
                request.getGitlabProjectId(),
                request.getGitlabFilePath());
    }

    /**
     * 批量根据GitLab文件唯一标识统计评论数量。
     * 用于文件列表页，一次性获取多个文件的评论数量。
     *
     * @param fileIdentifiers 包含 gitlabProjectId, gitlabFilePath, commitId (可选) 的Map列表。
     * @return Map，键为 `gitlabFilePath` (或 `gitlabProjectId:gitlabFilePath:commitId` 的组合)，值为评论数量。
     */
    @Override
    public Map<String, Long> countFileCommentsBatch(List<Map<String, Object>> fileIdentifiers) {
        if (CollectionUtils.isEmpty(fileIdentifiers)) {
            return Collections.emptyMap();
        }
        List<Map<String, Object>> rawCounts = fileCommentMapper.countCommentsByGitLabFileIdentifiersBatch(fileIdentifiers);
        // 将结果Map转换为前端更易于使用的格式：key通常是 gitlabFilePath 或其组合
        Map<String, Long> result = new HashMap<>();
        for (Map<String, Object> rawCount : rawCounts) {
            // 根据 Mapper XML 中定义的别名 'filePath' 和 'commentCount' 来获取
            String filePath = (String) rawCount.get("filePath");
            Long count = (Long) rawCount.get("commentCount");
            if (StringUtils.isNotBlank(filePath) && count != null) {
                result.put(filePath, count);
            }
        }
        return result;
    }

}
