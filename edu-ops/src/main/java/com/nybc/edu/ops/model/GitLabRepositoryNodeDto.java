package com.nybc.edu.ops.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.gitlab4j.api.models.TreeItem;

import java.time.LocalDateTime;

/**
 * 类描述：
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Accessors(chain = true)
@Schema(description = "GitLab仓库树节点信息DTO")
public class GitLabRepositoryNodeDto {
    @Schema(description = "节点ID (通常是SHA)")
    private String id;
    @Schema(description = "节点名称 (文件名或目录名)")
    private String name;
    @Schema(description = "节点类型 ('tree' 表示目录, 'blob' 表示文件)")
    private String type;
    @Schema(description = "节点的路径")
    private String path;
    @Schema(description = "文件模式 (例如 '100644')")
    private String mode;
    @Schema(description = "文件大小 (字节, 仅对文件有效)")
    private Integer size;
    @Schema(description = "最后一次提交的ID (完整SHA, 仅对文件有效)")
    private String lastCommitId;
    @Schema(description = "最后一次提交的时间, 仅对文件有效")
    private LocalDateTime lastCommitTime;
    @Schema(description = "最后一次提交的作者姓名, 仅对文件有效")
    private String lastCommitAuthor;

    public static GitLabRepositoryNodeDto fromTreeItem(TreeItem item) {
        if (item == null) {
            return null;
        }
        return new GitLabRepositoryNodeDto()
                .setId(item.getId())
                .setName(item.getName())
                .setType(item.getType().toString().toLowerCase())
                .setPath(item.getPath())
                .setMode(item.getMode());
    }


}
