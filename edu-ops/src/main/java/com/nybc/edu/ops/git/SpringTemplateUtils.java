package com.nybc.edu.ops.git;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

/**
 * 类描述：
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
public class SpringTemplateUtils {
    private static final List<String> CONFIG_FILE_PATTERNS = Arrays.asList(
            "application.yml",
            "application.yaml",
            "application.properties",
            "bootstrap.yml",
            "bootstrap.yaml",
            "bootstrap.properties",
            "application-*.yml"
                                                                          );

    /**
     * 递归处理指定目录下的 Spring Boot 配置文件，替换占位符。
     *
     * @param directory      要处理的根目录 (通常是仓库的工作目录)
     * @param replacementMap 包含占位符键和替换值的映射 (键不包含 ${})
     * @throws IOException 如果文件读写或查找出错
     */
    public static void processConfigurationFiles(File directory, Map<String, String> replacementMap) throws IOException {
        if (replacementMap == null || replacementMap.isEmpty()) {
            log.info("未提供占位符替换映射，跳过配置文件处理。");
            return;
        }
        if (directory == null || !directory.isDirectory()) {
            log.warn("提供的目录无效，无法处理配置文件: {}", directory);
            return;
        }
        log.info("开始处理目录 [{}] 中的配置文件...", directory.getAbsolutePath());
        Path startPath = directory.toPath();
        // 使用 Files.walk 查找配置文件
        try (Stream<Path> walk = Files.walk(startPath)) {
            walk.filter(Files::isRegularFile)
                    .filter(path -> {
                        String fileName = path.getFileName().toString().toLowerCase();
                        return CONFIG_FILE_PATTERNS.stream().anyMatch(pattern ->
                                                                              fileName.equals(pattern) ||
                                                                                      (pattern.contains("*") && matchesProfilePattern(fileName, pattern)) //
                                                                     ) || isProfileSpecificFile(fileName);
                    })
                    .forEach(path -> {
                        try {
                            log.debug("找到配置文件，准备处理: {}", path);
                            String content = Files.readString(path, StandardCharsets.UTF_8);
                            boolean modified = false;
                            for (Map.Entry<String, String> entry : replacementMap.entrySet()) {
                                String placeholder = "${" + entry.getKey() + "}";
                                String value = entry.getValue();
                                if (content.contains(placeholder)) {
                                    content = content.replace(placeholder, value);
                                    log.info("在文件 [{}] 中替换占位符 [{}]", path.getFileName(), placeholder);
                                    modified = true;
                                }
                            }
                            if (modified) {
                                Files.writeString(path, content, StandardCharsets.UTF_8, StandardOpenOption.TRUNCATE_EXISTING);
                                log.info("成功更新配置文件: {}", path);
                            } else {
                                log.debug("文件 [{}] 未包含需要替换的占位符，未修改。", path.getFileName());
                            }

                        } catch (IOException e) {
                            log.error("处理配置文件 [{}] 时出错: {}", path, e.getMessage(), e);
                        }
                    });
        }
        log.info("配置文件处理完成。");
    }

    // Helper to check profile-specific files more robustly
    private static boolean isProfileSpecificFile(String fileName) {
        return (fileName.startsWith("application-") || fileName.startsWith("bootstrap-")) &&
                (fileName.endsWith(".yml") || fileName.endsWith(".yaml") || fileName.endsWith(".properties"));
    }

    // Basic wildcard matching (can be made more sophisticated if needed)
    private static boolean matchesProfilePattern(String fileName, String pattern) {
        if (!pattern.contains("*")) {
            return fileName.equals(pattern);
        }
        String prefix = pattern.substring(0, pattern.indexOf('*'));
        String suffix = pattern.substring(pattern.indexOf('*') + 1);
        return fileName.startsWith(prefix) && fileName.endsWith(suffix);
    }

}
