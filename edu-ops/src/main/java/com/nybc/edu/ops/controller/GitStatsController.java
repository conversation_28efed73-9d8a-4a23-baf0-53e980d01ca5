package com.nybc.edu.ops.controller;

import com.nybc.edu.common.enums.ResultCodeEnum;
import com.nybc.edu.common.model.ResultInfo;
import com.nybc.edu.core.exception.BizException;
import com.nybc.edu.ops.dto.request.CreateBranchRequestDto;
import com.nybc.edu.ops.dto.request.GitLabRepoStatsRequestDto;
import com.nybc.edu.ops.dto.response.CreateBranchResponseDto;
import com.nybc.edu.ops.model.*;
import com.nybc.edu.ops.service.GitStatsService;
import com.nybc.user.context.UserHold;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;


/**
 * 类描述：Git统计数据查询控制器。
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/api/ops/git/stats")
@Tag(name = "Git统计数据查询", description = "提供项目和任务维度的Git统计数据接口")
public class GitStatsController {
    @Resource
    private GitStatsService gitStatsService;

    /**
     * 校验平台项目ID
     */
    private ResultInfo<Void> validatePlatformProjectId(Long platformProjectId) {
        if (platformProjectId == null) {
            return ResultInfo.error(ResultCodeEnum.BAD_REQUEST.getCode(), "平台项目ID不能为空。");
        }
        return null;
    }

    /**
     * 校验GitLab项目参数
     */
    private ResultInfo<Void> validateGitLabParams(Long platformTaskId, String gitlabProjectId) {
        if (platformTaskId == null) {
            return ResultInfo.error(ResultCodeEnum.BAD_REQUEST.getCode(), "平台任务ID不能为空。");
        }
        if (StringUtils.isBlank(gitlabProjectId)) {
            return ResultInfo.error(ResultCodeEnum.BAD_REQUEST.getCode(), "GitLab项目ID不能为空。");
        }
        return null;
    }


    /**
     * 获取项目整体Git统计总览。
     */
    @PostMapping("/project/overall")
    @Operation(summary = "项目：获取整体Git统计总览", description = "获取项目在指定时间范围内的核心Git指标总览。")
    public ResultInfo<OverallGitStatsResponseDto> getProjectOverallGitStats(@Valid @RequestBody GitStatsRequestDto request) {
        request.setPlatformTaskId(null);

        return handleServiceCallWithTimeout(() -> gitStatsService.getOverallGitStats(request), "获取项目整体Git统计总览", 30000);
    }

    /**
     * 获取项目用户提交次数。
     */
    @PostMapping("/project/user-commit-counts")
    @Operation(summary = "项目：获取用户提交次数", description = "获取项目范围内各用户提交次数。")
    public ResultInfo<GitCommitStatsResponseDto> getProjectUserCommitCounts(@Valid @RequestBody GitStatsRequestDto request) {
        request.setPlatformTaskId(null);
        return handleServiceCall(() -> gitStatsService.getProjectCommitStats(request), "获取项目用户提交次数");
    }

    /**
     * 获取项目用户代码变更行数。
     */
    @PostMapping("/project/user-code-changes")
    @Operation(summary = "项目：获取用户代码变更行数", description = "获取项目范围内各用户代码变更行数。")
    public ResultInfo<GitCodeStatsResponseDto> getProjectUserCodeChanges(@Valid @RequestBody GitStatsRequestDto request) {
        request.setPlatformTaskId(null);
        return handleServiceCall(() -> gitStatsService.getProjectCodeStats(request), "获取项目用户代码变更行数");
    }

    /**
     * 获取项目用户提交文件数量。
     */
    @PostMapping("/project/user-changed-files-count")
    @Operation(summary = "项目：获取用户提交文件数量", description = "获取项目范围内各用户提交文件数量。")
    public ResultInfo<GitCodeStatsResponseDto> getProjectUserChangedFilesCount(@Valid @RequestBody GitStatsRequestDto request) {
        request.setPlatformTaskId(null);
        return handleServiceCall(() -> gitStatsService.getProjectCodeStats(request), "获取项目用户提交文件数量");
    }

    /**
     * 获取项目每日总提交次数。
     */
    @PostMapping("/project/total-commits-by-day")
    @Operation(summary = "项目：获取每日总提交次数", description = "获取项目范围内每日总提交次数。")
    public ResultInfo<ProjectDailyTrendsResponseDto> getProjectTotalCommitsByDay(@Valid @RequestBody GitStatsRequestDto request) {
        request.setPlatformTaskId(null);
        return handleServiceCall(() -> gitStatsService.getProjectDailyTrends(request), "获取项目每日总提交次数");
    }

    /**
     * 获取项目中时间条件内参与的所有的任务的所有的gitlab仓库的行数，包括新增行数，删除行数，修改行数。
     * 对应项目维度需求中的 (6)。
     *
     * @param request 包含平台项目ID、时间范围的请求DTO。platformTaskId应为null。
     * @return 每日趋势统计数据响应DTO，其中包含 totalCodeChangesByDay。
     */
    @PostMapping("/project/total-code-changes-by-day")
    @Operation(summary = "项目：获取每日总代码变更行数", description = "获取项目范围内每日总代码变更行数。")
    public ResultInfo<ProjectDailyTrendsResponseDto> getProjectTotalCodeChangesByDay(@Valid @RequestBody GitStatsRequestDto request) {
        request.setPlatformTaskId(null);
        return handleServiceCall(() -> gitStatsService.getProjectDailyTrends(request), "获取项目每日总代码变更行数");
    }

    /**
     * 获取项目中时间条件内参与的所有的任务的所有的gitlab仓库的提交文件数量。
     * 对应项目维度需求中的 (7)。
     *
     * @param request 包含平台项目ID、时间范围的请求DTO。platformTaskId应为null。
     * @return 每日趋势统计数据响应DTO，其中包含 changedFilesByDay。
     */
    @PostMapping("/project/total-changed-files-by-day")
    @Operation(summary = "项目：获取每日总修改文件数", description = "获取项目范围内每日总修改文件数。")
    public ResultInfo<ProjectDailyTrendsResponseDto> getProjectTotalChangedFilesByDay(@Valid @RequestBody GitStatsRequestDto request) {
        request.setPlatformTaskId(null);
        return handleServiceCall(() -> gitStatsService.getProjectDailyTrends(request), "获取项目每日总修改文件数");
    }

    /**
     * 获取项目中时间条件内参与的所有的任务的所有的gitlab仓库的文件类型统计信息。
     * 对应项目维度需求中的 (9)。
     *
     * @param request 包含平台项目ID、时间范围的请求DTO。platformTaskId应为null。
     * @return 文件类型统计Map。
     */
    @PostMapping("/project/file-type-stats")
    @Operation(summary = "项目：获取文件类型统计", description = "获取项目范围内不同文件类型的统计信息。")
    public ResultInfo<Map<String, Map<String, Integer>>> getProjectFileTypeStats(@Valid @RequestBody GitStatsRequestDto request) {
        request.setPlatformTaskId(null);
        return handleServiceCall(() -> gitStatsService.getFileTypeStats(request), "获取项目文件类型统计");
    }

    /**
     * 获取项目中新增代码行最多的前十个用户。
     * 对应项目维度需求中的 (10)。
     *
     * @param request 包含平台项目ID、时间范围和topN的请求DTO。platformTaskId应为null。
     * @return 用户新增代码行数排名列表。
     */
    @PostMapping("/project/user-additions-topn")
    @Operation(summary = "项目：获取新增代码行TopN用户", description = "获取项目范围内新增代码行数最多的TopN用户。")
    public ResultInfo<List<UserTopNStatsEntry>> getProjectUserAdditionsTopN(@Valid @RequestBody GitStatsRequestDto request) {
        request.setPlatformTaskId(null);
        return handleServiceCall(() -> gitStatsService.getUserAdditionsTopN(request, request.getTopN()), "获取项目用户新增TopN");
    }

    /**
     * 获取项目中修改代码行最多的前十个用户。
     * 对应项目维度需求中的 (11)。
     *
     * @param request 包含平台项目ID、时间范围和topN的请求DTO。platformTaskId应为null。
     * @return 用户修改代码行数排名列表。
     */
    @PostMapping("/project/user-modifications-topn")
    @Operation(summary = "项目：获取修改代码行TopN用户", description = "获取项目范围内修改代码行数最多的TopN用户。")
    public ResultInfo<List<UserTopNStatsEntry>> getProjectUserModificationsTopN(@Valid @RequestBody GitStatsRequestDto request) {
        request.setPlatformTaskId(null);
        return handleServiceCall(() -> gitStatsService.getUserModificationsTopN(request, request.getTopN()), "获取项目用户修改TopN");
    }

    /**
     * 获取项目中修改次数最多的前N个文件。
     * 对应项目维度需求中的最多修改文件统计。
     *
     * @param request 包含平台项目ID、时间范围和topN的请求DTO。platformTaskId应为null。
     * @return 文件修改次数排名列表。
     */
    @PostMapping("/project/top-modified-files")
    @Operation(summary = "项目：获取TopN修改文件", description = "获取项目范围内修改次数最多的TopN文件。")
    public ResultInfo<List<GitCommitStatsResponseDto.StringIntegerEntry>> getProjectTopModifiedFiles(@Valid @RequestBody GitStatsRequestDto request) {
        request.setPlatformTaskId(null);
        return handleServiceCall(() -> gitStatsService.getTopModifiedFilesForProject(
                request.getPlatformProjectId(),
                request.getStartDate(),
                request.getEndDate(),
                request.getTopN()), "获取项目TopN修改文件");
    }


    // -----------------------------------------------------------
    // 任务统计维度接口
    // -----------------------------------------------------------

    /**
     * 获取任务整体Git统计总览。
     * 对应任务维度需求中的 (1) (2) (3) (5) 的总览数据。
     *
     * @param request 包含平台项目ID、任务ID、时间范围的请求DTO。
     * @return 整体统计数据响应DTO。
     */
    @PostMapping("/task/overall")
    @Operation(summary = "任务：获取整体Git统计总览", description = "获取任务在指定时间范围内的核心Git指标总览。")
    public ResultInfo<?> getTaskOverallGitStats(@Valid @RequestBody GitStatsRequestDto request) {
        ResultInfo<Void> validation = validatePlatformProjectId(request.getPlatformProjectId());
        if (validation != null) {
            return  validation;
        }
        return handleServiceCall(() -> gitStatsService.getOverallGitStats(request), "获取任务整体Git统计总览");
    }

    /**
     * 获取项目中每个任务在时间条件内参与的所有用户的所有的gitlab仓库的提交次数。
     * 对应任务维度需求中的 (1)。
     *
     * @param request 包含平台项目ID、任务ID、时间范围的请求DTO。
     * @return 代码活跃度统计数据响应DTO，其中包含 commitCountsByUser。
     */
    @PostMapping("/task/user-commit-counts")
    @Operation(summary = "任务：获取用户提交次数", description = "获取任务范围内各用户提交次数。")
    public ResultInfo<GitCommitStatsResponseDto> getTaskUserCommitCounts(@Valid @RequestBody GitStatsRequestDto request) {
        if (request.getPlatformProjectId() == null) {
            return ResultInfo.error(ResultCodeEnum.BAD_REQUEST.getCode(), "平台项目ID不能为空。");
        }
        return handleServiceCall(() -> gitStatsService.getProjectCommitStats(request), "获取任务用户提交次数");
    }

    /**
     * 获取项目中每个任务在时间条件内参与的所有用户的的所有的gitlab仓库的行数，包括新增行数，删除行数，修改行数。
     * 对应任务维度需求中的 (2)。
     *
     * @param request 包含平台项目ID、任务ID、时间范围的请求DTO。
     * @return 任务代码量统计数据响应DTO，其中包含 totalAdditions, totalDeletions, totalChanges。
     */
    @PostMapping("/task/code-changes")
    @Operation(summary = "任务：获取总代码变更行数", description = "获取任务范围内总代码变更行数。")
    public ResultInfo<List<TaskCodeStatsResponseDto>> getTaskCodeChanges(@Valid @RequestBody GitStatsRequestDto request) {
        if (request.getPlatformProjectId() == null) {
            return ResultInfo.error(ResultCodeEnum.BAD_REQUEST.getCode(), "平台项目ID不能为空。");
        }

        return handleServiceCallWithTimeout(() -> gitStatsService.getTaskCodeStats(request), "获取任务总代码变更行数", 25000);
    }

    /**
     * 获取项目中每个任务在时间条件内参与的所有用户的的所有的gitlab仓库的提交文件数量。
     * 对应任务维度需求中的 (3)。
     *
     * @param request 包含平台项目ID、任务ID、时间范围的请求DTO。
     * @return 任务代码量统计数据响应DTO，其中包含 totalChangedFilesCount。
     */
    @PostMapping("/task/changed-files-count")
    @Operation(summary = "任务：获取总修改文件数量", description = "获取任务范围内总修改文件数量。")
    public ResultInfo<List<TaskCodeStatsResponseDto>> getTaskChangedFilesCount(@Valid @RequestBody GitStatsRequestDto request) {
        if (request.getPlatformProjectId() == null) {
            return ResultInfo.error(ResultCodeEnum.BAD_REQUEST.getCode(), "平台项目ID不能为空。");
        }
        return handleServiceCall(() -> gitStatsService.getTaskCodeStats(request), "获取任务总修改文件数量");
    }

    /**
     * 获取项目中每个任务的所有仓库的文件类型统计信息。
     * 对应任务维度需求中的 (5)。
     *
     * @param request 包含平台项目ID、任务ID、时间范围的请求DTO。
     * @return 文件类型统计Map。
     */
    @PostMapping("/task/file-type-stats")
    @Operation(summary = "任务：获取文件类型统计", description = "获取任务范围内不同文件类型的统计信息。")
    public ResultInfo<Map<String, Map<String, Integer>>> getTaskFileTypeStats(@Valid @RequestBody GitStatsRequestDto request) {
        if (request.getPlatformProjectId() == null) {
            return ResultInfo.error(ResultCodeEnum.BAD_REQUEST.getCode(), "平台项目ID不能为空。");
        }
        return handleServiceCall(() -> gitStatsService.getFileTypeStats(request), "获取任务文件类型统计");
    }

    /**
     * 获取项目中每个任务的修改次数最多的前十个文件，按照仓库分别统计数据。
     * 对应任务维度需求中的 (6) 和 (7)。
     *
     * @param request 包含平台项目ID、任务ID、时间范围和topN的请求DTO。
     * @return 文件修改次数排名列表。
     */
    @PostMapping("/task/top-modified-files")
    @Operation(summary = "任务：获取TopN修改文件", description = "获取指定任务在时间范围内修改次数最多的TopN文件，包含详细信息。")
    public ResultInfo<List<TopModifiedFileDetailDto>> getTaskTopModifiedFiles(@Valid @RequestBody GitStatsRequestDto request) {
        if (request.getPlatformProjectId() == null) {
            return ResultInfo.error(ResultCodeEnum.BAD_REQUEST.getCode(), "平台项目ID不能为空。");
        }

        // 根据是否有任务ID来决定调用哪个方法
        if (request.getPlatformTaskId() == null) {
            // 项目级别：统计项目下所有任务的TopN修改文件
            return handleServiceCall(() -> gitStatsService.getTopModifiedFilesDetailForProject(
                    request.getPlatformProjectId(),
                    request.getStartDate(),
                    request.getEndDate(),
                    request.getTopN()), "获取项目所有任务TopN修改文件");
        } else {
            // 任务级别：统计指定任务的TopN修改文件
            return handleServiceCall(() -> gitStatsService.getTopModifiedFilesDetailForTask(
                    request.getPlatformTaskId(),
                    request.getStartDate(),
                    request.getEndDate(),
                    request.getTopN()), "获取任务TopN修改文件");
        }
    }

    /**
     * 获取GitLab仓库统计信息
     * 包含提交数、分支数、文件数、仓库大小、贡献者等信息
     *
     * @param request 包含平台任务ID、GitLab项目ID、时间范围的请求DTO
     * @return 仓库统计信息
     */
    @PostMapping("/repository/stats")
    @Operation(summary = "获取GitLab仓库统计信息", description = "获取指定GitLab仓库的详细统计信息，包含提交数、分支数、文件数、仓库大小、贡献者等。")
    public ResultInfo<com.nybc.edu.ops.dto.response.GitLabRepoStatsDto> getRepositoryStats(@Valid @RequestBody GitLabRepoStatsRequestDto request) {
        if (request.getPlatformTaskId() == null) {
            return ResultInfo.error(ResultCodeEnum.BAD_REQUEST.getCode(), "平台任务ID不能为空。");
        }
        if (StringUtils.isBlank(request.getGitlabProjectId())) {
            return ResultInfo.error(ResultCodeEnum.BAD_REQUEST.getCode(), "GitLab项目ID不能为空。");
        }

        return handleServiceCall(() -> gitStatsService.getRepositoryStats(
                request.getPlatformTaskId(),
                request.getGitlabProjectId(),
                request.getStartDate(),
                request.getEndDate()), "获取GitLab仓库统计信息");
    }

    /**
     * 创建新分支
     *
     * @param request 包含平台任务ID、GitLab项目ID、分支名称、源分支等信息的请求DTO
     * @return 创建分支的结果信息
     */
    @PostMapping("/repository/create-branch")
    @Operation(summary = "创建新分支", description = "在指定的GitLab仓库中创建新分支。")
    public ResultInfo<CreateBranchResponseDto> createBranch(@Valid @RequestBody CreateBranchRequestDto request) {
        if (request.getPlatformTaskId() == null) {
            return ResultInfo.error(ResultCodeEnum.BAD_REQUEST.getCode(), "平台任务ID不能为空。");
        }
        if (StringUtils.isBlank(request.getGitlabProjectId())) {
            return ResultInfo.error(ResultCodeEnum.BAD_REQUEST.getCode(), "GitLab项目ID不能为空。");
        }
        if (StringUtils.isBlank(request.getBranchName())) {
            return ResultInfo.error(ResultCodeEnum.BAD_REQUEST.getCode(), "分支名称不能为空。");
        }
        if (StringUtils.isBlank(request.getSourceBranch())) {
            return ResultInfo.error(ResultCodeEnum.BAD_REQUEST.getCode(), "源分支不能为空。");
        }

        return handleServiceCall(() -> gitStatsService.createBranch(request), "创建新分支");
    }


    /**
     * 通用服务调用处理器，用于减少重复的try-catch和日志代码。
     *
     * @param serviceCall   Service层方法的Supplier
     * @param operationName 操作名称，用于日志
     * @param <T>           响应数据类型
     * @return 统一封装的ResultInfo
     */
    private <T> ResultInfo<T> handleServiceCall(SupplierWithException<T> serviceCall, String operationName) {
        // 移除超时限制，直接调用服务
        return handleServiceCallWithoutTimeout(serviceCall, operationName);
    }

    /**
     * 无超时限制的服务调用处理器
     */
    private <T> ResultInfo<T> handleServiceCallWithoutTimeout(SupplierWithException<T> serviceCall, String operationName) {
        Long userId = UserHold.getUserId();
        try {
            log.info("用户 {} 开始{}", userId, operationName);
            long startTime = System.currentTimeMillis();

            T data = serviceCall.get();

            long duration = System.currentTimeMillis() - startTime;
            log.info("用户 {} {} 成功，耗时: {}ms", userId, operationName, duration);
            return ResultInfo.success(data);

        } catch (IllegalArgumentException e) {
            log.warn("用户 {} {} 失败：参数校验失败：{}", userId, operationName, e.getMessage());
            return ResultInfo.error(ResultCodeEnum.BAD_REQUEST.getCode(), e.getMessage());

        } catch (BizException e) {
            log.error("用户 {} {} 失败：业务异常：Code={}, Message='{}'", userId, operationName, e.getCode(), e.getMessage(), e);
            return ResultInfo.error(e.getCode(), e.getMessage());

        } catch (Exception e) {
            log.error("用户 {} {} 失败：系统异常：{}", userId, operationName, e.getMessage(), e);
            return ResultInfo.error(ResultCodeEnum.INTERNAL_SERVER_ERROR.getCode(), operationName + "失败：" + e.getMessage());
        }
    }

    /**
     * 带超时控制的服务调用处理器
     *
     * @param serviceCall   Service层方法的Supplier
     * @param operationName 操作名称，用于日志
     * @param timeoutMs     超时时间（毫秒）
     * @param <T>           响应数据类型
     * @return 统一封装的ResultInfo
     */
    private <T> ResultInfo<T> handleServiceCallWithTimeout(SupplierWithException<T> serviceCall, String operationName, long timeoutMs) {
        Long userId = UserHold.getUserId();
        try {
            log.info("用户 {} 开始{}，超时设置: {}ms", userId, operationName, timeoutMs);
            long startTime = System.currentTimeMillis();

            // 使用CompletableFuture实现超时控制
            CompletableFuture<T> future = CompletableFuture.supplyAsync(() -> {
                try {
                    return serviceCall.get();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            T data = future.get(timeoutMs, TimeUnit.MILLISECONDS);

            long duration = System.currentTimeMillis() - startTime;
            log.info("用户 {} {} 成功，耗时: {}ms", userId, operationName, duration);
            return ResultInfo.success(data);

        } catch (TimeoutException e) {
            log.warn("用户 {} {} 超时：超过{}ms", userId, operationName, timeoutMs);
            return ResultInfo.error(ResultCodeEnum.REQUEST_TIMEOUT.getCode(),
                                    operationName + "超时，请稍后重试或联系管理员");

        } catch (IllegalArgumentException e) {
            log.warn("用户 {} {} 失败：参数校验失败：{}", userId, operationName, e.getMessage());
            return ResultInfo.error(ResultCodeEnum.BAD_REQUEST.getCode(), e.getMessage());

        } catch (BizException e) {
            log.error("用户 {} {} 失败：业务异常：Code={}, Message='{}'", userId, operationName, e.getCode(), e.getMessage(), e);
            return ResultInfo.error(e.getCode(), e.getMessage());

        } catch (Exception e) {
            log.error("用户 {} {} 失败：系统异常：{}", userId, operationName, e.getMessage(), e);
            return ResultInfo.error(ResultCodeEnum.INTERNAL_SERVER_ERROR.getCode(), operationName + "失败：" + e.getMessage());
        }
    }

    // 辅助接口，用于Lambda表达式抛出受检异常
    @FunctionalInterface
    private interface SupplierWithException<T> {
        T get() throws Exception;

    }

}