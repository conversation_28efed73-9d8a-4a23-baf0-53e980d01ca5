package com.nybc.edu.ops.git;

import com.nybc.common.tool.file.FileUtil;
import com.nybc.common.tool.file.ZipUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.jgit.api.*;
import org.eclipse.jgit.api.errors.GitAPIException;
import org.eclipse.jgit.diff.DiffEntry;
import org.eclipse.jgit.diff.DiffFormatter;
import org.eclipse.jgit.errors.ConfigInvalidException;
import org.eclipse.jgit.lib.*;
import org.eclipse.jgit.revwalk.*;
import org.eclipse.jgit.storage.file.FileRepositoryBuilder;
import org.eclipse.jgit.transport.CredentialsProvider;
import org.eclipse.jgit.transport.PushResult;
import org.eclipse.jgit.transport.RemoteRefUpdate;
import org.eclipse.jgit.transport.UsernamePasswordCredentialsProvider;
import org.eclipse.jgit.treewalk.AbstractTreeIterator;
import org.eclipse.jgit.treewalk.CanonicalTreeParser;
import org.eclipse.jgit.treewalk.TreeWalk;
import org.eclipse.jgit.treewalk.filter.PathFilter;
import org.eclipse.jgit.util.SystemReader;
import org.eclipse.jgit.util.io.DisabledOutputStream;

import javax.net.ssl.*;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.cert.X509Certificate;
import java.util.*;

import static com.nybc.common.tool.NytcToolConstant.GIT_HOME;

/**
 * 类描述：https://github.com/centic9/jgit
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
public class GitUtils {
    public final static String REF_HEADS = "refs/heads/";
    public final static String REF_REMOTE_HEADS = "refs/remotes/origin/";
    public final static String REF_REMOTE_TAG = "refs/tags/";

    // 新增辅助方法
    private static boolean isGitRepository(File dir) {
        return new File(dir, ".git").exists();
    }

    private static String getRemoteOriginUrl(File dir) {
        try (Git git = Git.open(dir)) {
            return git.getRepository().getConfig().getString("remote", "origin", "url");
        } catch (Exception e) {
            log.warn("获取现有仓库URL失败: {}", e.getMessage());
            return "";
        }
    }

    // URL标准化处理（解决HTTPS/SSH格式差异）
    private static String normalizeGitUrl(String url) {
        return getRepositoryName(url)
                .replace(".git", "")
                .toLowerCase();
    }

    private static void deleteDirectory(File dir) {
        File[] allContents = dir.listFiles();
        if (allContents != null) {
            for (File file : allContents) {
                deleteDirectory(file);
            }
        }
        FileUtil.forceDelete(dir);
    }

    private static boolean isDirEmpty(File dir) {
        return dir.isDirectory() && (dir.list() == null || Objects.requireNonNull(dir.list()).length == 0);
    }

    public static Git cloneHttps(String url, String branch, String user, String password, String token) throws Exception {
        log.info("开始拉取代码");
        String path = GIT_HOME + getRepositoryName(url);
        File dir = new File(path);
        CredentialsProvider cp;
        if (StringUtils.isNotBlank(token)) {
            cp = new UsernamePasswordCredentialsProvider("PRIVATE-TOKEN", token);
        } else {
            cp = new UsernamePasswordCredentialsProvider(user, password);
        }
        if (StringUtils.isBlank(branch)) {
            branch = "master";
        }
        if (dir.exists()) {
            boolean isGitRepo = isGitRepository(dir);
            if (isGitRepo) {
                String existingUrl = getRemoteOriginUrl(dir);
                String normalizedUrl = normalizeGitUrl(url);
                String normalizedExisting = normalizeGitUrl(existingUrl);
                if (!normalizedUrl.equals(normalizedExisting)) {
                    log.warn("检测到仓库地址变更：原地址[{}] => 新地址[{}]", existingUrl, url);
                    deleteDirectory(dir);
                } else {
                    log.info("复用现有Git仓库：{}", path);
                    Git git = openExistingRepository(path);
                    checkoutBranch(git, branch, cp);
                    return git;
                }
            } else if (!isDirEmpty(dir)) {
                log.warn("清理非Git目录：{}", path);
                deleteDirectory(dir);
            }
        } else {
            dir.mkdirs();
        }
        CloneCommand cloneCommand = Git.cloneRepository();
        cloneCommand.setBranch(branch);
        // 设置传输配置
        cloneCommand.setURI(url).setCredentialsProvider(cp)
                .setDirectory(new File(path))
                .setCloneAllBranches(false)
                .setCloneSubmodules(false)
                .setDepth(20)
                .setTimeout(300)
                .setProgressMonitor(new LogProgressMonitor())
                .setTransportConfigCallback(transport -> configureTrustAllCertificates());
        configureTrustAllCertificates();
        try (Git git = cloneCommand.call()) {
            // 克隆完成后，设置一些本地配置
            SystemReader.getInstance().getUserConfig().setBoolean("http", null, "followRedirects", true);
            StoredConfig config = git.getRepository().getConfig();
            config.setBoolean("core", null, "autocrlf", false);
            config.setInt("core", null, "compression", 0);
            config.setInt("pack", null, "threads", Runtime.getRuntime().availableProcessors());
            config.setInt("core", null, "packedGitLimit", 512 * 1024 * 1024);
            config.setInt("core", null, "packedGitWindowSize", 512 * 1024 * 1024);
            config.setBoolean("core", null, "useBuiltinFSMonitor", false);
            config.setBoolean("http", null, "followRedirects", true);
            config.save();
            log.info("仓库克隆完成: {}", path);
            return git;
        } catch (IOException | ConfigInvalidException e) {
            log.error("Git Clone仓库异常--->{}", e.getMessage());
        }
        return null;
    }

    /**
     * 根据 commitId 拉取代码
     *
     * @param url      仓库地址
     * @param commitId 提交ID
     * @param user     用户名
     * @param password 密码
     * @param token    认证令牌
     * @return Git 对象
     */
    public static Git cloneByCommitId(String url, String branch, String commitId, String user, String password, String token) {
        try {
            // 克隆仓库
            Git git = cloneHttps(url, branch, user, password, token);
            if (git != null) {
                // 切换到指定提交
                git.checkout().setName(commitId).call();
                log.info("已切换到提交: {}", commitId);
                return git;
            }
        } catch (Exception e) {
            log.error("根据 commitId 拉取代码失败--->{}, 仓库地址：{}, 用户名：{}, 密码：{}", e.getMessage(), url, user, password);
        }
        return null;
    }

    public static String getRepositoryName(String url) {
        if (url == null || url.isEmpty()) {
            return null;
        }
        // 移除末尾的 .git（如果存在）
        url = url.endsWith(".git") ? url.substring(0, url.length() - 4) : url;
        // 处理 SSH 格式的 URL
        if (url.startsWith("git@")) {
            // 格式: **************:username/repo.git
            String[] parts = url.split(":");
            if (parts.length > 1) {
                return parts[1].substring(parts[1].lastIndexOf('/') + 1);
            }
        }
        // 处理 HTTPS 格式的 URL
        else if (url.startsWith("https://") || url.startsWith("http://")) {
            // 格式: https://github.com/username/repo.git
            String[] parts = url.split("/");
            if (parts.length > 0) {
                return parts[parts.length - 1];
            }
        }
        // 如果无法解析，返回整个 URL 作为仓库名
        return url.substring(url.lastIndexOf('/') + 1);
    }

    private static Git openExistingRepository(String path) throws IOException {
        FileRepositoryBuilder builder = new FileRepositoryBuilder();
        Repository repository = builder.setGitDir(new File(path, ".git"))
                .readEnvironment()
                .findGitDir()
                .build();
        return new Git(repository);
    }

    /**
     * 拉取切换分支
     *
     * @param branchName 分支
     * @param branchName 分支
     */
    public static void checkoutBranch(Git git, String branchName, CredentialsProvider cp) throws Exception {
        configureTrustAllCertificates();
        Repository repository = git.getRepository();
        String branch = repository.getBranch();
        boolean create = false;
        if (!branch.equals(branchName)) {
            Ref ref = git.getRepository().findRef(REF_HEADS + branchName);
            if (null == ref) {
                create = true;
            } else {
                git.checkout().setCreateBranch(false).setName(branchName).call();
                Ref localRef = git.getRepository().exactRef(REF_HEADS + branchName);
                if (checkVersion(git, localRef, cp)) {
                    return;
                }
            }
        } else {
            Ref localRef = git.getRepository().exactRef(REF_HEADS + branchName);
            if (checkVersion(git, localRef, cp)) {
                return;
            }
        }
        // 得到仓库本地分支
        git.checkout().setCreateBranch(create).setForceRefUpdate(true)
                .setName(branchName)
                .setUpstreamMode(CreateBranchCommand.SetupUpstreamMode.SET_UPSTREAM)
                .setStartPoint("origin/" + branchName).call();
        git.pull().setCredentialsProvider(cp).setRemoteBranchName(branchName).setRebase(true).call();

    }

    /**
     * 获取指定分支的指定文件内容
     *
     * @param branchName 分支名称
     * @param path       文件路径
     * @return java类
     * @throws IOException
     */
    public static String getBranchFile(Git git, String branchName, String commitId, String path) throws IOException {
        Repository repository = git.getRepository();
        // 优先使用commitId，如果未提供则使用分支名
        ObjectId objId;
        if (commitId != null && !commitId.isEmpty()) {
            objId = repository.resolve(commitId);
        } else if (branchName != null && !branchName.isEmpty()) {
            Ref branch = repository.exactRef("refs/heads/" + branchName);
            objId = branch.getObjectId();
        } else {
            throw new IllegalArgumentException("Either branchName or commitId must be provided");
        }
        RevWalk walk = new RevWalk(repository);
        RevTree tree = walk.parseTree(objId);
        return getFileContent(repository, path, tree, walk);
    }

    /**
     * 获取指定分支指定的指定文件内容
     *
     * @param javaPath 件路径
     * @param tree     git RevTree
     * @param walk     git RevWalk
     * @return java类
     * @throws IOException
     */
    private static String getFileContent(Repository repository, String javaPath, RevTree tree, RevWalk walk) throws IOException {
        TreeWalk treeWalk = TreeWalk.forPath(repository, javaPath, tree);
        ObjectId blobId = treeWalk.getObjectId(0);
        ObjectLoader loader = repository.open(blobId);
        byte[] bytes = loader.getBytes();
        walk.dispose();
        return new String(bytes, StandardCharsets.UTF_8);

    }

    /**
     * 判断本地分支是否是最新版本。目前不考虑分支在远程仓库不存在，本地存在
     *
     * @param localRef 本地分支
     * @return boolean
     * @throws GitAPIException GitAPIException
     */
    private static boolean checkVersion(Git git, Ref localRef, CredentialsProvider cp) throws GitAPIException {
        String localRefName = localRef.getName();
        String localRefObjectId = localRef.getObjectId().getName();
        //  获取远程所有分支
        Collection<Ref> remoteRefs = git.lsRemote().setCredentialsProvider(cp).setHeads(true).call();
        for (Ref remoteRef : remoteRefs) {
            String remoteRefName = remoteRef.getName();
            String remoteRefObjectId = remoteRef.getObjectId().getName();
            if (remoteRefName.equals(localRefName)) {
                return remoteRefObjectId.equals(localRefObjectId);
            }
        }
        return false;
    }


    public static void configureTrustAllCertificates() {
        try {
            // Create a trust manager that does not validate certificate chains
            TrustManager[] trustAllCerts = new TrustManager[]{
                    new X509TrustManager() {
                        @Override
                        public X509Certificate[] getAcceptedIssuers() {
                            return null;
                        }
                        @Override
                        public void checkClientTrusted(X509Certificate[] certs, String authType) {
                        }
                        @Override
                        public void checkServerTrusted(X509Certificate[] certs, String authType) {
                        }
                    }
            };
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
            HostnameVerifier allHostsValid = (hostname, session) -> true;
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 克隆或更新仓库，解压ZIP文件覆盖工作目录，替换配置文件占位符，然后提交并推送代码。
     *
     * @param url            仓库地址
     * @param branch         分支名称
     * @param user           用户名 (用于 HTTP Basic Auth 或 密码为空时的占位符)
     * @param password       密码 (用于 HTTP Basic Auth)
     * @param token          认证令牌 (例如 GitLab Private Token, 优先于 user/password)
     * @param zipFilePath    要解压的本地 ZIP 文件路径
     * @param replacementMap 包含占位符键和替换值的映射 (键不包含 ${})
     * @param commitMessage  提交信息
     * @return 是否成功完成所有步骤
     */
    public static boolean cloneUnzipReplaceAndPush(
            String url, String branch, String user, String password, String token,
            String zipFilePath, Map<String, String> replacementMap, String commitMessage) {

        Git git = null;
        CredentialsProvider cp;
        if (StringUtils.isNotBlank(token)) {
            cp = new UsernamePasswordCredentialsProvider("PRIVATE-TOKEN", token);
        } else {
            cp = new UsernamePasswordCredentialsProvider(user, "Yang@8890");
        }
        String repoPath = null;
        try {
            // 1. 克隆或更新仓库，并检出到指定分支
            git = cloneHttps(url, branch, user, password, token);
            if (git == null) {
                log.error("仓库克隆或更新失败，操作中止。");
                return false;
            }
            repoPath = git.getRepository().getWorkTree().getAbsolutePath();
            log.info("仓库操作完成，工作目录: {}", repoPath);

            // 2. 解压ZIP文件到仓库目录 (会覆盖现有文件)
            log.info("开始解压文件 [{}] 到仓库目录 [{}]...", zipFilePath, repoPath);
            File zipFile = new File(zipFilePath);
            if (!zipFile.exists() || !zipFile.isFile()) {
                log.error("ZIP 文件不存在或不是有效文件: {}", zipFilePath);
                return false;
            }
            // Assuming ZipUtils.unzip handles existing files (overwrites)
            ZipUtils.unzip(zipFilePath, repoPath);
            log.info("文件解压完成。");

            // 3. 处理配置文件，替换占位符
            SpringTemplateUtils.processConfigurationFiles(new File(repoPath), replacementMap);

            // 4. 添加所有更改到 Git 暂存区
            // '.' 表示添加工作目录下的所有更改 (包括新增、修改、删除)
            log.info("将所有更改添加到 Git 暂存区...");
            git.add().addFilepattern(".").call();
            log.info("更改已添加到暂存区。");

            // 5. 检查是否有实际更改需要提交
            Status status = git.status().call();
            // Check for untracked, modified, added, removed, changed files
            if (!status.isClean()) {
                log.info("检测到文件更改，准备提交...");
                log.debug("Git status details: Added={}, Changed={}, Modified={}, Removed={}, Untracked={}",
                          status.getAdded(), status.getChanged(), status.getModified(), status.getRemoved(), status.getUntracked());

                // 6. 提交更改
                if (StringUtils.isBlank(commitMessage)) {
                    commitMessage = "Automated update from zip: " + zipFile.getName();
                    log.warn("未提供提交信息，使用默认信息: {}", commitMessage);
                }
                log.info("执行提交操作，提交信息: '{}'", commitMessage);
                RevCommit commit = git.commit().setMessage(commitMessage).call();
                log.info("提交成功，Commit ID: {}", commit.getName());

                // 7. 推送到远程仓库
                log.info("准备推送到远程仓库 origin/{}...", branch);
                Iterable<PushResult> pushResults = git.push()
                        .setCredentialsProvider(cp)
                        .setProgressMonitor(new LogProgressMonitor())
                        .call();
                // Check push results for errors
                boolean pushSuccessful = true;
                for (PushResult result : pushResults) {
                    log.info("推送结果: {}", result.getMessages());
                    for (RemoteRefUpdate update : result.getRemoteUpdates()) {
                        RemoteRefUpdate.Status updateStatus = update.getStatus();
                        log.info("  更新状态 for {}: {}", update.getRemoteName(), updateStatus);
                        if (updateStatus != RemoteRefUpdate.Status.OK && updateStatus != RemoteRefUpdate.Status.UP_TO_DATE && updateStatus != RemoteRefUpdate.Status.NON_EXISTING) {
                            pushSuccessful = false;
                            log.error("推送更新失败: {} - {}", update.getRemoteName(), updateStatus);
                        }
                    }
                }
                if (!pushSuccessful) {
                    log.error("部分或全部推送操作失败。");
                    // Decide if this is a fatal error for the method's return value
                    return false;
                }
                log.info("代码已成功推送到远程仓库。");
            } else {
                log.info("没有检测到文件更改 (相对于上次提交)，无需提交或推送。");
            }
            return true;

        } catch (Exception e) {
            // Log the exception with stack trace for debugging
            log.error("执行 cloneUnzipReplaceAndPush 操作时失败: {}", e.getMessage(), e);
            return false;
        } finally {
            if (git != null) {
                git.close();
                log.debug("Git 实例已关闭。");
            }
        }
    }
    /**
     * 获取两个提交之间的差异文件
     *
     * @param git          Git实例
     * @param oldCommitId  旧提交ID
     * @param newCommitId  新提交ID
     * @param filePath     文件路径过滤器（可为null）
     * @return 差异文件列表
     */
    public List<DiffFile> getDiffFiles(Git git, String oldCommitId, String newCommitId, String filePath) {
        List<DiffFile> diffFiles = new ArrayList<>();

        try {
            Repository repository = git.getRepository();
            // 解析提交ID
            ObjectId oldHead = repository.resolve(oldCommitId);
            ObjectId newHead = repository.resolve(newCommitId);

            if (oldHead == null || newHead == null) {
                log.error("无法解析提交ID: old={}, new={}", oldCommitId, newCommitId);
                return diffFiles;
            }
            // 获取树迭代器
            AbstractTreeIterator oldTreeParser = prepareTreeParser(repository, oldHead);
            AbstractTreeIterator newTreeParser = prepareTreeParser(repository, newHead);
            // 创建差异格式化器
            DiffFormatter diffFormatter = new DiffFormatter(DisabledOutputStream.INSTANCE);
            diffFormatter.setRepository(repository);
            // 设置文件路径过滤器
            if (filePath != null && !filePath.isEmpty()) {
                diffFormatter.setPathFilter(PathFilter.create(filePath));
            }
            // 获取差异条目
            List<DiffEntry> diffs = diffFormatter.scan(oldTreeParser, newTreeParser);
            // 转换为差异文件
            for (DiffEntry diff : diffs) {
                DiffFile.ChangeType changeType = switch (diff.getChangeType()) {
                    case ADD -> DiffFile.ChangeType.ADD;
                    case MODIFY -> DiffFile.ChangeType.MODIFY;
                    case DELETE -> DiffFile.ChangeType.DELETE;
                    case RENAME -> DiffFile.ChangeType.RENAME;
                    case COPY -> DiffFile.ChangeType.COPY;
                };
                DiffFile diffFile = new DiffFile(
                        diff.getOldPath(),
                        diff.getNewPath(),
                        changeType
                );
                diffFiles.add(diffFile);
            }

        } catch (IOException e) {
            log.error("获取差异文件失败", e);
        }
        return diffFiles;
    }
    /**
     * 准备树解析器
     *
     * @param repository 仓库
     * @param objectId   对象ID
     * @return 树迭代器
     */
    private AbstractTreeIterator prepareTreeParser(Repository repository, ObjectId objectId) throws IOException {
        try (RevWalk walk = new RevWalk(repository)) {
            RevCommit commit = walk.parseCommit(objectId);
            RevTree tree = walk.parseTree(commit.getTree().getId());
            CanonicalTreeParser treeParser = new CanonicalTreeParser();
            try (ObjectReader reader = repository.newObjectReader()) {
                treeParser.reset(reader, tree.getId());
            }
            walk.dispose();
            return treeParser;
        }
    }


    /**
     * 获取文件内容
     *
     * @param git      Git实例
     * @param commitId 提交ID
     * @param filePath 文件路径
     * @return 文件内容
     */
    public String getFileContent(Git git, String commitId, String filePath) {
        try {
            Repository repository = git.getRepository();
            // 解析提交ID
            ObjectId objectId = repository.resolve(commitId);
            if (objectId == null) {
                log.error("无法解析提交ID: {}", commitId);
                return null;
            }
            // 获取提交
            try (RevWalk revWalk = new RevWalk(repository)) {
                RevCommit commit = revWalk.parseCommit(objectId);
                RevTree tree = commit.getTree();
                // 获取文件内容
                return new String(git.getRepository().open(tree, filePath).getBytes());
            }

        } catch (IOException e) {
            log.error("获取文件内容失败: {}", filePath, e);
            return null;
        }
    }
}
