package com.nybc.edu.ops.maven;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.maven.repository.internal.MavenRepositorySystemUtils;
import org.apache.maven.settings.Mirror;
import org.apache.maven.settings.Server;
import org.apache.maven.settings.Settings;
import org.apache.maven.settings.building.*;
import org.eclipse.aether.DefaultRepositorySystemSession;
import org.eclipse.aether.RepositorySystem;
import org.eclipse.aether.RepositorySystemSession;
import org.eclipse.aether.artifact.Artifact;
import org.eclipse.aether.artifact.DefaultArtifact;
import org.eclipse.aether.collection.CollectRequest;
import org.eclipse.aether.collection.CollectResult;
import org.eclipse.aether.collection.DependencyCollectionException;
import org.eclipse.aether.connector.basic.BasicRepositoryConnectorFactory;
import org.eclipse.aether.graph.Dependency;
import org.eclipse.aether.graph.DependencyNode;
import org.eclipse.aether.impl.DefaultServiceLocator;
import org.eclipse.aether.repository.Authentication;
import org.eclipse.aether.repository.LocalRepository;
import org.eclipse.aether.repository.RemoteRepository;
import org.eclipse.aether.repository.RepositoryPolicy;
import org.eclipse.aether.resolution.*;
import org.eclipse.aether.spi.connector.RepositoryConnectorFactory;
import org.eclipse.aether.spi.connector.transport.TransporterFactory;
import org.eclipse.aether.transport.file.FileTransporterFactory;
import org.eclipse.aether.transport.http.HttpTransporterFactory;
import org.eclipse.aether.util.graph.visitor.PreorderNodeListGenerator;
import org.eclipse.aether.util.repository.AuthenticationBuilder;
import org.eclipse.aether.version.Version;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Maven依赖管理工具类
 * 基于Maven Resolver实现
 */
@Slf4j
public class MavenResolverUtils {

    /**
     * 默认本地仓库路径
     */
    private static final String DEFAULT_LOCAL_REPOSITORY = System.getProperty("user.home") + "/.m2/repository";
    
    /**
     * 临时目录
     */
    private static final String TEMP_DIR = System.getProperty("java.io.tmpdir") + "/maven-temp";

    /**
     * 创建RepositorySystem实例
     *
     * @return RepositorySystem实例
     */
    public static RepositorySystem newRepositorySystem() {
        DefaultServiceLocator locator = MavenRepositorySystemUtils.newServiceLocator();
        locator.addService(RepositoryConnectorFactory.class, BasicRepositoryConnectorFactory.class);
        locator.addService(TransporterFactory.class, FileTransporterFactory.class);
        locator.addService(TransporterFactory.class, HttpTransporterFactory.class);

        locator.setErrorHandler(new DefaultServiceLocator.ErrorHandler() {
            @Override
            public void serviceCreationFailed(Class<?> type, Class<?> impl, Throwable exception) {
                log.error("Service creation failed for {} with implementation {}",
                          type, impl, exception);
            }
        });
        
        return locator.getService(RepositorySystem.class);
    }

    /**
     * 创建RepositorySystemSession
     *
     * @param system         RepositorySystem实例
     * @param localRepoPath  本地仓库路径
     * @param settingsPath   Maven settings.xml路径
     * @return 配置好的DefaultRepositorySystemSession对象
     */
    public static DefaultRepositorySystemSession newSession(RepositorySystem system, String localRepoPath, String settingsPath) {
        DefaultRepositorySystemSession session = MavenRepositorySystemUtils.newSession();
        // 加载settings.xml
        Settings settings = loadSettings(settingsPath);
        // 设置本地仓库
        String repoPath = StringUtils.isNotBlank(localRepoPath) ? localRepoPath : 
                          (StringUtils.isNotBlank(settings.getLocalRepository()) ? settings.getLocalRepository() : DEFAULT_LOCAL_REPOSITORY);
        LocalRepository localRepo = new LocalRepository(repoPath);
        session.setLocalRepositoryManager(system.newLocalRepositoryManager(session, localRepo));
        // 设置其他属性
        session.setOffline(false);
        session.setTransferListener(new ConsoleTransferListener());
        session.setRepositoryListener(new ConsoleRepositoryListener());
        // 默认每日更新
        session.setUpdatePolicy(RepositoryPolicy.UPDATE_POLICY_DAILY);
        // 默认校验和警告
        session.setChecksumPolicy(RepositoryPolicy.CHECKSUM_POLICY_WARN);
        return session;
    }

    /**
     * 加载Maven settings.xml文件
     *
     * @param settingsPath settings.xml文件的路径
     * @return Settings对象
     */
    public static Settings loadSettings(String settingsPath) {
        // 如果未指定settings路径，使用默认路径
        if (StringUtils.isBlank(settingsPath)) {
            settingsPath = System.getProperty("user.home") + "/.m2/settings.xml";
        }
        
        File settingsFile = new File(settingsPath);
        if (!settingsFile.exists()) {
            log.warn("Maven settings.xml文件不存在: {}, 将使用默认设置", settingsPath);
            return new Settings();
        }
        
        SettingsBuilder settingsBuilder = new DefaultSettingsBuilderFactory().newInstance();
        DefaultSettingsBuildingRequest request = new DefaultSettingsBuildingRequest();
        request.setUserSettingsFile(settingsFile);
        
        try {
            SettingsBuildingResult result = settingsBuilder.build(request);
            return result.getEffectiveSettings();
        } catch (SettingsBuildingException e) {
            log.error("加载settings.xml失败: " + e.getMessage(), e);
            return new Settings();
        }
    }

    /**
     * 创建远程仓库列表
     *
     * @param settings           Maven settings对象
     * @param customRepositoryUrl 自定义仓库URL
     * @return 远程仓库列表
     */
    public static List<RemoteRepository> getRemoteRepositories(Settings settings, String customRepositoryUrl) {
        List<RemoteRepository> remoteRepos = new ArrayList<>();
        // 添加阿里云仓库
        remoteRepos.add(new RemoteRepository.Builder("aliyun", "default", "https://maven.aliyun.com/repository/public").build());
        // 如果提供了自定义仓库URL，添加它
        if (StringUtils.isNotBlank(customRepositoryUrl)) {
            remoteRepos.add(new RemoteRepository.Builder("custom", "default", customRepositoryUrl).build());
        }
        // 添加Maven中央仓库
        remoteRepos.add(new RemoteRepository.Builder("central", "default", "https://repo.maven.apache.org/maven2/").build());
        // 添加settings中配置的镜像
        if (settings != null && CollectionUtils.isNotEmpty(settings.getMirrors())) {
            for (Mirror mirror : settings.getMirrors()) {
                RemoteRepository.Builder builder = new RemoteRepository.Builder(mirror.getId(), "default", mirror.getUrl());
                Server server = settings.getServer(mirror.getId());
                if (server != null) {
                    Authentication auth = new AuthenticationBuilder()
                            .addUsername(server.getUsername())
                            .addPassword(server.getPassword())
                            .build();
                    builder.setAuthentication(auth);
                }
                remoteRepos.add(builder.build());
            }
        }
        
        return remoteRepos;
    }

    /**
     * 获取构件的所有可用版本
     *
     * @param groupId       组ID
     * @param artifactId    构件ID
     * @param settingsPath  Maven settings.xml路径
     * @param repositoryUrl 自定义仓库URL
     * @return 版本列表，按时间降序排列
     */
    public static List<String> getAllVersions(String groupId, String artifactId, String settingsPath, String repositoryUrl) {
        RepositorySystem repositorySystem = newRepositorySystem();
        Settings settings = loadSettings(settingsPath);
        DefaultRepositorySystemSession session = newSession(repositorySystem, null, settingsPath);
        
        Artifact artifact = new DefaultArtifact(groupId, artifactId, null, null, "[0,)");
        VersionRangeRequest rangeRequest = new VersionRangeRequest();
        rangeRequest.setArtifact(artifact);
        
        List<RemoteRepository> remoteRepos = getRemoteRepositories(settings, repositoryUrl);
        rangeRequest.setRepositories(remoteRepos);
        
        List<String> versionList = new ArrayList<>();
        try {
            VersionRangeResult rangeResult = repositorySystem.resolveVersionRange(session, rangeRequest);
            List<Version> versions = rangeResult.getVersions();
            if (CollectionUtils.isNotEmpty(versions)) {
                versionList = new ArrayList<>(versions.size());
                for (Version version : versions) {
                    versionList.add(version.toString());
                }
                Collections.reverse(versionList); // 按时间降序排列
            }
        } catch (VersionRangeResolutionException e) {
            log.error("获取Maven版本[{}:{}]执行异常: {}", groupId, artifactId, e.getMessage());
        }
        
        return versionList;
    }

    /**
     * 下载构件
     *
     * @param groupId       组ID
     * @param artifactId    构件ID
     * @param version       版本
     * @param settingsPath  Maven settings.xml路径
     * @param repositoryUrl 自定义仓库URL
     * @param targetDir     下载目标目录
     * @return 下载的构件文件
     */
    public static File downloadArtifact(String groupId, String artifactId, String version, String settingsPath, String repositoryUrl, String targetDir) {
        RepositorySystem repositorySystem = newRepositorySystem();
        Settings settings = loadSettings(settingsPath);
        DefaultRepositorySystemSession session = newSession(repositorySystem, null, settingsPath);
        
        Artifact artifact = new DefaultArtifact(groupId, artifactId, "", "jar", version);
        ArtifactRequest artifactRequest = new ArtifactRequest();
        artifactRequest.setArtifact(artifact);
        
        List<RemoteRepository> remoteRepos = getRemoteRepositories(settings, repositoryUrl);
        artifactRequest.setRepositories(remoteRepos);
        
        try {
            ArtifactResult artifactResult = repositorySystem.resolveArtifact(session, artifactRequest);
            File file = artifactResult.getArtifact().getFile();
            
            // 如果指定了目标目录，复制文件
            if (StringUtils.isNotBlank(targetDir)) {
                File targetDirFile = new File(targetDir);
                if (!targetDirFile.exists()) {
                    targetDirFile.mkdirs();
                }
                
                File targetFile = new File(targetDirFile, file.getName());
                FileUtils.copyFile(file, targetFile);
                return targetFile;
            }
            return file;
        } catch (ArtifactResolutionException | IOException e) {
            log.error("下载构件[{}:{}:{}]失败: {}", groupId, artifactId, version, e.getMessage());
            return null;
        }
    }

    /**
     * 下载源码包
     *
     * @param groupId       组ID
     * @param artifactId    构件ID
     * @param version       版本
     * @param settingsPath  Maven settings.xml路径
     * @param repositoryUrl 自定义仓库URL
     * @param targetDir     下载目标目录
     * @return 下载的源码包文件
     */
    public static File downloadSourceJar(String groupId, String artifactId, String version, String settingsPath, String repositoryUrl, String targetDir) {
        RepositorySystem repositorySystem = newRepositorySystem();
        Settings settings = loadSettings(settingsPath);
        DefaultRepositorySystemSession session = newSession(repositorySystem, null, settingsPath);
        
        Artifact artifact = new DefaultArtifact(groupId, artifactId, "sources", "jar", version);
        ArtifactRequest artifactRequest = new ArtifactRequest();
        artifactRequest.setArtifact(artifact);
        
        List<RemoteRepository> remoteRepos = getRemoteRepositories(settings, repositoryUrl);
        artifactRequest.setRepositories(remoteRepos);
        
        try {
            ArtifactResult artifactResult = repositorySystem.resolveArtifact(session, artifactRequest);
            File file = artifactResult.getArtifact().getFile();
            
            // 如果指定了目标目录，复制文件
            if (StringUtils.isNotBlank(targetDir)) {
                File targetDirFile = new File(targetDir);
                if (!targetDirFile.exists()) {
                    targetDirFile.mkdirs();
                }
                
                File targetFile = new File(targetDirFile, file.getName());
                FileUtils.copyFile(file, targetFile);
                return targetFile;
            }
            
            return file;
        } catch (ArtifactResolutionException | IOException e) {
            log.error("下载源码包[{}:{}:{}]失败: {}", groupId, artifactId, version, e.getMessage());
            return null;
        }
    }

    /**
     * 解析依赖
     *
     * @param groupId       组ID
     * @param artifactId    构件ID
     * @param version       版本
     * @param settingsPath  Maven settings.xml路径
     * @param repositoryUrl 自定义仓库URL
     * @return 依赖节点列表
     */
    public static List<DependencyNode> resolveDependencies(String groupId, String artifactId, String version, String settingsPath, String repositoryUrl) {
        RepositorySystem repositorySystem = newRepositorySystem();
        Settings settings = loadSettings(settingsPath);
        DefaultRepositorySystemSession session = newSession(repositorySystem, null, settingsPath);
        
        Artifact artifact = new DefaultArtifact(groupId, artifactId, "", "jar", version);
        
        CollectRequest collectRequest = new CollectRequest();
        collectRequest.setRoot(new Dependency(artifact, "compile"));
        
        List<RemoteRepository> remoteRepos = getRemoteRepositories(settings, repositoryUrl);
        collectRequest.setRepositories(remoteRepos);
        
        try {
            CollectResult collectResult = repositorySystem.collectDependencies(session, collectRequest);
            DependencyNode root = collectResult.getRoot();
            
            // 解析依赖
            DependencyRequest dependencyRequest = new DependencyRequest();
            dependencyRequest.setRoot(root);
            repositorySystem.resolveDependencies(session, dependencyRequest);
            
            // 生成依赖节点列表
            PreorderNodeListGenerator nlg = new PreorderNodeListGenerator();
            root.accept(nlg);
            
            return nlg.getNodes();
        } catch (DependencyCollectionException | DependencyResolutionException e) {
            log.error("解析依赖[{}:{}:{}]失败: {}", groupId, artifactId, version, e.getMessage());
            return Collections.emptyList();
        }
    }

    /**
     * 下载构件及其依赖
     *
     * @param groupId       组ID
     * @param artifactId    构件ID
     * @param version       版本
     * @param settingsPath  Maven settings.xml路径
     * @param repositoryUrl 自定义仓库URL
     * @param targetDir     下载目标目录
     * @return 下载的文件列表
     */
    public static List<File> downloadWithDependencies(String groupId, String artifactId, String version, String settingsPath, String repositoryUrl, String targetDir) {
        RepositorySystem repositorySystem = newRepositorySystem();
        Settings settings = loadSettings(settingsPath);
        DefaultRepositorySystemSession session = newSession(repositorySystem, null, settingsPath);
        
        Artifact artifact = new DefaultArtifact(groupId, artifactId, "", "jar", version);
        
        CollectRequest collectRequest = new CollectRequest();
        collectRequest.setRoot(new Dependency(artifact, "compile"));
        
        List<RemoteRepository> remoteRepos = getRemoteRepositories(settings, repositoryUrl);
        collectRequest.setRepositories(remoteRepos);
        
        try {
            CollectResult collectResult = repositorySystem.collectDependencies(session, collectRequest);
            DependencyNode root = collectResult.getRoot();
            
            DependencyRequest dependencyRequest = new DependencyRequest();
            dependencyRequest.setRoot(root);
            
            repositorySystem.resolveDependencies(session, dependencyRequest);
            
            PreorderNodeListGenerator nlg = new PreorderNodeListGenerator();
            root.accept(nlg);
            
            List<File> files = new ArrayList<>();
            for (Artifact a : nlg.getArtifacts(false)) {
                File file = a.getFile();
                
                // 如果指定了目标目录，复制文件
                if (StringUtils.isNotBlank(targetDir)) {
                    File targetDirFile = new File(targetDir);
                    if (!targetDirFile.exists()) {
                        targetDirFile.mkdirs();
                    }
                    
                    File targetFile = new File(targetDirFile, file.getName());
                    FileUtils.copyFile(file, targetFile);
                    files.add(targetFile);
                } else {
                    files.add(file);
                }
            }
            
            return files;
        } catch (DependencyCollectionException | DependencyResolutionException | IOException e) {
            log.error("下载构件及其依赖[{}:{}:{}]失败: {}", groupId, artifactId, version, e.getMessage());
            return Collections.emptyList();
        }
    }

    /**
     * 查找项目中的pom.xml文件
     *
     * @param projectDir 项目目录
     * @return pom.xml文件路径，如果未找到则返回null
     */
    public static String findPomFile(String projectDir) {
        File projectDirFile = new File(projectDir);
        if (!projectDirFile.exists() || !projectDirFile.isDirectory()) {
            return null;
        }
        
        // 首先检查项目根目录
        File pomFile = new File(projectDirFile, "pom.xml");
        if (pomFile.exists() && pomFile.isFile()) {
            return pomFile.getAbsolutePath();
        }
        
        // 递归查找
        Optional<String> pomPath = findPomFileRecursively(projectDirFile);
        return pomPath.orElse(null);
    }
    
    /**
     * 递归查找pom.xml文件
     *
     * @param dir 目录
     * @return pom.xml文件路径
     */
    private static Optional<String> findPomFileRecursively(File dir) {
        File[] files = dir.listFiles();
        if (files == null) {
            return Optional.empty();
        }
        
        // 首先检查当前目录
        for (File file : files) {
            if (file.isFile() && "pom.xml".equals(file.getName())) {
                return Optional.of(file.getAbsolutePath());
            }
        }
        
        // 然后检查子目录
        for (File file : files) {
            if (file.isDirectory() && !file.getName().startsWith(".")) {
                Optional<String> pomPath = findPomFileRecursively(file);
                if (pomPath.isPresent()) {
                    return pomPath;
                }
            }
        }
        
        return Optional.empty();
    }
    
    /**
     * 查找目录中的JAR文件（不包括源码包）
     *
     * @param directory 目录
     * @return JAR文件路径列表
     */
    public static List<Path> findJarsNotSource(Path directory) {
        try {
            return Files.walk(directory)
                    .filter(Files::isRegularFile)
                    .filter(path -> {
                        String fileName = path.getFileName().toString().toLowerCase();
                        return fileName.endsWith(".jar") && !fileName.contains("-sources") && !fileName.contains("-javadoc");
                    })
                    .collect(Collectors.toList());
        } catch (IOException e) {
            log.error("查找JAR文件失败: {}", e.getMessage());
            return Collections.emptyList();
        }
    }
} 