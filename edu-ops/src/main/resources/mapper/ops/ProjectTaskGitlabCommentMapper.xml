<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nybc.edu.ops.dao.ProjectTaskGitlabCommentMapper">
    <resultMap id="BaseResultMap" type="com.nybc.edu.ops.entity.ProjectTaskGitlabComment">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="file_id" jdbcType="BIGINT" property="fileId"/>
        <result column="platform_project_id" jdbcType="BIGINT" property="platformProjectId"/>
        <result column="platform_task_id" jdbcType="BIGINT" property="platformTaskId"/>
        <result column="gitlab_project_id" jdbcType="BIGINT" property="gitlabProjectId"/>
        <result column="gitlab_file_path" jdbcType="VARCHAR" property="gitlabFilePath"/>
        <result column="comment_content" jdbcType="VARCHAR" property="commentContent"/>
        <result column="parent_comment_id" jdbcType="BIGINT" property="parentCommentId"/>
        <result column="comment_type" jdbcType="VARCHAR" property="commentType"/>
        <result column="line_number" jdbcType="INTEGER" property="lineNumber"/>
        <result column="commit_id" jdbcType="VARCHAR" property="commitId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user" jdbcType="BIGINT" property="createUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user" jdbcType="BIGINT" property="updateUser"/>
        <result column="deleted" jdbcType="INTEGER" property="deleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, file_id, platform_project_id, platform_task_id, gitlab_project_id, gitlab_file_path, comment_content, parent_comment_id, 
            comment_type, line_number, commit_id, create_time, create_user, update_time, update_user, deleted
    </sql>
    <!--查询单个-->
    <select id="getById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_project_task_gitlab_comment
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="findRepliesByParentCommentId" resultType="com.nybc.edu.ops.entity.ProjectTaskGitlabComment">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_project_task_gitlab_comment
        WHERE parent_comment_id = #{parentCommentId,jdbcType=BIGINT}
        AND deleted = 0
        ORDER BY create_time
    </select>
    <select id="countCommentsForFileWithoutCommit" resultType="java.lang.Long">
        SELECT COUNT(id)
        FROM t_project_task_gitlab_comment
        WHERE gitlab_project_id = #{gitlabProjectId,jdbcType=BIGINT}
          AND gitlab_file_path = #{gitlabFilePath,jdbcType=VARCHAR}
          AND deleted = 0
    </select>
    <select id="findAllCommentsForFile" resultType="com.nybc.edu.ops.entity.ProjectTaskGitlabComment">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_project_task_gitlab_comment
        WHERE gitlab_project_id = #{gitlabProjectId,jdbcType=BIGINT}
        AND gitlab_file_path = #{gitlabFilePath,jdbcType=VARCHAR}
        AND deleted = 0
        ORDER BY create_time
    </select>
    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from t_project_task_gitlab_comment
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="logicalDeleteBatch">
        update t_project_task_gitlab_comment
        set deleted = 1,
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        update_user = #{updateUser,jdbcType=BIGINT}
        where id in
        <foreach collection="commentIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and deleted = 0
    </delete>
    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true"
            parameterType="com.nybc.edu.ops.entity.ProjectTaskGitlabComment">
        insert into t_project_task_gitlab_comment(file_id, platform_project_id, platform_task_id, gitlab_project_id,
                                                  gitlab_file_path, comment_content, parent_comment_id, comment_type,
                                                  line_number, commit_id, create_time, create_user, update_time,
                                                  update_user, deleted,tenant_id)
        values (#{fileId,jdbcType=BIGINT}, #{platformProjectId,jdbcType=BIGINT}, #{platformTaskId,jdbcType=BIGINT},
                #{gitlabProjectId,jdbcType=BIGINT}, #{gitlabFilePath,jdbcType=VARCHAR},
                #{commentContent,jdbcType=VARCHAR}, #{parentCommentId,jdbcType=BIGINT}, #{commentType,jdbcType=VARCHAR},
                #{lineNumber,jdbcType=INTEGER}, #{commitId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
                #{createUser,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=BIGINT},
                #{deleted,jdbcType=INTEGER}, #{tenantId,jdbcType=BIGINT})
    </insert>
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true"
            parameterType="com.nybc.edu.ops.entity.ProjectTaskGitlabComment">
        insert into t_project_task_gitlab_comment(
        file_id, platform_project_id, platform_task_id, gitlab_project_id, gitlab_file_path, comment_content,
        parent_comment_id, comment_type, line_number, commit_id, create_time, create_user, update_time, update_user,
        deleted, tenant_id ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.fileId,jdbcType=BIGINT} , #{item.platformProjectId,jdbcType=BIGINT} ,
            #{item.platformTaskId,jdbcType=BIGINT} , #{item.gitlabProjectId,jdbcType=BIGINT} ,
            #{item.gitlabFilePath,jdbcType=VARCHAR} , #{item.commentContent,jdbcType=VARCHAR} ,
            #{item.parentCommentId,jdbcType=BIGINT} , #{item.commentType,jdbcType=VARCHAR} ,
            #{item.lineNumber,jdbcType=INTEGER} , #{item.commitId,jdbcType=VARCHAR} ,
            #{item.createTime,jdbcType=TIMESTAMP} , #{item.createUser,jdbcType=BIGINT} ,
            #{item.updateTime,jdbcType=TIMESTAMP} , #{item.updateUser,jdbcType=BIGINT} ,
            #{item.deleted,jdbcType=INTEGER}, #{item.tenantId,jdbcType=BIGINT} )
        </foreach>
    </insert>
    <!--通过主键修改数据-->
    <update id="updateById" parameterType="com.nybc.edu.ops.entity.ProjectTaskGitlabComment">
        update t_project_task_gitlab_comment
        <set>
            file_id = #{fileId,jdbcType=BIGINT},
            platform_project_id = #{platformProjectId,jdbcType=BIGINT},
            platform_task_id = #{platformTaskId,jdbcType=BIGINT},
            gitlab_project_id = #{gitlabProjectId,jdbcType=BIGINT},
            gitlab_file_path = #{gitlabFilePath,jdbcType=VARCHAR},
            comment_content = #{commentContent,jdbcType=VARCHAR},
            parent_comment_id = #{parentCommentId,jdbcType=BIGINT},
            comment_type = #{commentType,jdbcType=VARCHAR},
            line_number = #{lineNumber,jdbcType=INTEGER},
            commit_id = #{commitId,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            create_user = #{createUser,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            update_user = #{updateUser,jdbcType=BIGINT},
            deleted = #{deleted,jdbcType=INTEGER},
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateCommentContent" parameterType="com.nybc.edu.ops.entity.ProjectTaskGitlabComment">
        UPDATE t_project_task_gitlab_comment
        SET comment_content = #{commentContent,jdbcType=LONGVARCHAR},
            update_time     = #{updateTime,jdbcType=TIMESTAMP},
            update_user     = #{updateUser,jdbcType=BIGINT}
        WHERE id = #{id,jdbcType=BIGINT}
          AND deleted = 0
    </update>
    <update id="logicalDelete">
        UPDATE t_project_task_gitlab_comment
        SET deleted     = 1,
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            update_user = #{updateUser,jdbcType=BIGINT}
        WHERE id = #{commentId,jdbcType=BIGINT}
          AND deleted = 0
    </update>
    <select id="countCommentsByGitLabFileIdentifiersBatch" resultType="java.util.HashMap">
        SELECT
        t.gitlab_file_path AS filePath,
        t.gitlab_project_id AS gitlabProjectId,
        COUNT(t.id) AS commentCount
        FROM
        t_project_task_gitlab_comment t
        WHERE
        t.deleted = 0
        AND
        (
        <foreach collection="fileIdentifiers" item="item" index="idx" separator="OR">
            (
            t.platform_project_id = #{item.platformProjectId,jdbcType=BIGINT} AND
            t.platform_task_id = #{item.platformTaskId,jdbcType=BIGINT} AND
            t.gitlab_project_id = #{item.gitlabProjectId,jdbcType=BIGINT} AND
            t.gitlab_file_path = #{item.gitlabFilePath,jdbcType=VARCHAR}
            )
        </foreach>
        )
        GROUP BY
        t.gitlab_file_path, t.gitlab_project_id
    </select>
</mapper>