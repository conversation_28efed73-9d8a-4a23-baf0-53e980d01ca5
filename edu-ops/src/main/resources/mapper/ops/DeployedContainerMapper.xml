<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nybc.edu.ops.dao.DeployedContainerMapper">
    <resultMap id="BaseResultMap" type="com.nybc.edu.ops.entity.DeployedContainer">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="platform_project_id" jdbcType="BIGINT" property="platformProjectId"/>
        <result column="platform_task_id" jdbcType="BIGINT" property="platformTaskId"/>
        <result column="gitlab_project_id" jdbcType="BIGINT" property="gitlabProjectId"/>
        <result column="server_id" jdbcType="BIGINT" property="serverId"/>
        <result column="container_name" jdbcType="VARCHAR" property="containerName"/>
        <result column="image_name" jdbcType="VARCHAR" property="imageName"/>
        <result column="image_tag" jdbcType="VARCHAR" property="imageTag"/>
        <result column="allocated_port" jdbcType="INTEGER" property="allocatedPort"/>
        <result column="container_internal_port" jdbcType="INTEGER" property="containerInternalPort"/>
        <result column="deployment_status" jdbcType="VARCHAR" property="deploymentStatus"/>
        <result column="last_deployment_time" jdbcType="TIMESTAMP" property="lastDeploymentTime"/>
        <result column="deployment_message" jdbcType="LONGVARCHAR" property="deploymentMessage"/>
        <result column="docker_command" jdbcType="LONGVARCHAR" property="dockerCommand"/>
        <result column="commit_id" jdbcType="VARCHAR" property="commitId"/>
        <result column="operator_user_id" jdbcType="BIGINT" property="operatorUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user" jdbcType="BIGINT" property="createUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user" jdbcType="BIGINT" property="updateUser"/>
        <result column="deleted" jdbcType="INTEGER" property="deleted"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, platform_project_id, platform_task_id, gitlab_project_id, server_id,
        container_name, image_name, image_tag, allocated_port, container_internal_port,
        deployment_status, last_deployment_time, deployment_message, docker_command, commit_id, operator_user_id,
        create_time, create_user, update_time, update_user, deleted, tenant_id
    </sql>

    <!-- 通过ID查询单个已部署容器数据 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_ops_deployed_container
        WHERE id = #{id,jdbcType=BIGINT}
        AND deleted = 0
    </select>

    <!-- 根据平台项目ID和GitLab项目ID查询当前活跃的部署容器记录 -->
    <select id="findActiveDeployment" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_ops_deployed_container
        WHERE platform_project_id = #{platformProjectId,jdbcType=BIGINT}
        AND gitlab_project_id = #{gitlabProjectId,jdbcType=BIGINT}
        AND server_id = #{serverId,jdbcType=BIGINT}
        AND deployment_status = #{deploymentStatus,jdbcType=VARCHAR}
        AND deleted = 0
    </select>

    <!-- 根据平台项目ID查询所有部署历史记录 -->
    <select id="findByPlatformProjectId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_ops_deployed_container
        WHERE platform_project_id = #{platformProjectId,jdbcType=BIGINT}
        AND deleted = 0
        ORDER BY last_deployment_time DESC
    </select>

    <!-- 根据服务器ID查询该服务器上所有已部署的容器记录 -->
    <select id="findByServerId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_ops_deployed_container
        WHERE server_id = #{serverId,jdbcType=BIGINT}
        AND deleted = 0
        ORDER BY last_deployment_time DESC
    </select>

    <!-- 新增已部署容器数据 -->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true"
            parameterType="com.nybc.edu.ops.entity.DeployedContainer">
        INSERT INTO t_ops_deployed_container (platform_project_id, platform_task_id, gitlab_project_id, server_id,
                                              container_name, image_name, image_tag, allocated_port,
                                              container_internal_port,
                                              deployment_status, last_deployment_time, deployment_message,
                                              docker_command, commit_id, operator_user_id,
                                              create_time, create_user, update_time, update_user, deleted, tenant_id)
        VALUES (#{platformProjectId,jdbcType=BIGINT}, #{platformTaskId,jdbcType=BIGINT},
                #{gitlabProjectId,jdbcType=BIGINT}, #{serverId,jdbcType=BIGINT},
                #{containerName,jdbcType=VARCHAR}, #{imageName,jdbcType=VARCHAR}, #{imageTag,jdbcType=VARCHAR},
                #{allocatedPort,jdbcType=INTEGER}, #{containerInternalPort,jdbcType=INTEGER},
                #{deploymentStatus,jdbcType=VARCHAR}, #{lastDeploymentTime,jdbcType=TIMESTAMP},
                #{deploymentMessage,jdbcType=LONGVARCHAR}, #{dockerCommand,jdbcType=LONGVARCHAR},
                #{commitId,jdbcType=VARCHAR}, #{operatorUserId,jdbcType=BIGINT},
                #{createTime,jdbcType=TIMESTAMP}, #{createUser,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP},
                #{updateUser,jdbcType=BIGINT}, #{deleted,jdbcType=INTEGER}, #{tenantId,jdbcType=BIGINT})
    </insert>

    <!-- 更新已部署容器数据 -->
    <update id="updateById" parameterType="com.nybc.edu.ops.entity.DeployedContainer">
        UPDATE t_ops_deployed_container
        <set>
            platform_project_id = #{platformProjectId,jdbcType=BIGINT},
            platform_task_id = #{platformTaskId,jdbcType=BIGINT},
            gitlab_project_id = #{gitlabProjectId,jdbcType=BIGINT},
            server_id = #{serverId,jdbcType=BIGINT},
            container_name = #{containerName,jdbcType=VARCHAR},
            image_name = #{imageName,jdbcType=VARCHAR},
            image_tag = #{imageTag,jdbcType=VARCHAR},
            allocated_port = #{allocatedPort,jdbcType=INTEGER},
            container_internal_port = #{containerInternalPort,jdbcType=INTEGER},
            deployment_status = #{deploymentStatus,jdbcType=VARCHAR},
            last_deployment_time = #{lastDeploymentTime,jdbcType=TIMESTAMP},
            deployment_message = #{deploymentMessage,jdbcType=LONGVARCHAR},
            docker_command = #{dockerCommand,jdbcType=LONGVARCHAR},
            commit_id = #{commitId,jdbcType=VARCHAR},
            operator_user_id = #{operatorUserId,jdbcType=BIGINT},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            create_user = #{createUser,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            update_user = #{updateUser,jdbcType=BIGINT},
            deleted = #{deleted,jdbcType=INTEGER},
            tenant_id = #{tenantId,jdbcType=BIGINT}
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
        AND deleted = 0
    </update>

    <!-- 更新已部署容器的部署状态和消息 -->
    <update id="updateDeploymentStatusAndMessage">
        UPDATE t_ops_deployed_container
        SET deployment_status  = #{deploymentStatus,jdbcType=VARCHAR},
            deployment_message = #{deploymentMessage,jdbcType=LONGVARCHAR},
            update_time        = #{updateTime,jdbcType=TIMESTAMP},
            update_user        = #{updateUser,jdbcType=BIGINT}
        WHERE id = #{id,jdbcType=BIGINT}
          AND deleted = 0
    </update>

    <!-- 逻辑删除指定ID的已部署容器记录 -->
    <update id="logicalDelete">
        UPDATE t_ops_deployed_container
        SET deleted     = 1,
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            update_user = #{updateUser,jdbcType=BIGINT}
        WHERE id = #{id,jdbcType=BIGINT}
          AND deleted = 0
    </update>

    <!-- 批量逻辑删除指定平台项目ID下的所有容器记录 -->
    <update id="logicalDeleteByPlatformProjectId">
        UPDATE t_ops_deployed_container
        SET deleted     = 1,
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            update_user = #{updateUser,jdbcType=BIGINT}
        WHERE platform_project_id = #{platformProjectId,jdbcType=BIGINT}
          AND deleted = 0
    </update>

    <!-- 查询指定平台项目和GitLab项目ID下的最新部署记录，按部署时间倒序排列 -->
    <select id="findLatestDeployments" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_ops_deployed_container
        WHERE platform_project_id = #{platformProjectId,jdbcType=BIGINT}
        <if test="gitlabProjectId != null">
            AND gitlab_project_id = #{gitlabProjectId,jdbcType=BIGINT}
        </if>
        <if test="deleted != null">
            AND deleted = #{deleted,jdbcType=INTEGER}
        </if>
        <if test="deleted == null">
            AND deleted = 0
        </if>
        ORDER BY last_deployment_time DESC
        LIMIT #{limit,jdbcType=INTEGER}
    </select>

    <!-- 查询指定服务器上特定容器名称的最新活跃部署记录 -->
    <select id="findActiveContainerByName" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_ops_deployed_container
        WHERE server_id = #{serverId,jdbcType=BIGINT}
        AND container_name = #{containerName,jdbcType=VARCHAR}
        AND deployment_status = #{deploymentStatus,jdbcType=VARCHAR}
        AND deleted = 0
        ORDER BY last_deployment_time DESC
        LIMIT 1
    </select>
    <select id="findByGitlabJobId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_ops_deployed_container
        WHERE gitlab_job_id = #{gitlabJobId}
        AND deleted = 0
        ORDER BY create_time DESC
        LIMIT 1
    </select>
    <select id="findByPlatformTaskId" resultType="com.nybc.edu.ops.entity.DeployedContainer">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_ops_deployed_container
        WHERE platform_task_id = #{platformTaskId}
        AND deleted = 0
        ORDER BY create_time DESC
        LIMIT 1
    </select>
    <update id="updateStatusForSpecificDeployment">
        UPDATE t_ops_deployed_container
        SET deployment_status = #{newStatus,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        update_user = #{updateUser,jdbcType=BIGINT}
        WHERE platform_project_id = #{platformProjectId,jdbcType=BIGINT}
        AND gitlab_project_id = #{gitlabProjectId,jdbcType=BIGINT}
        AND server_id = #{serverId,jdbcType=BIGINT}
        AND deleted = 0
        AND deployment_status IN
        <foreach collection="oldStatuses" item="status" open="(" separator="," close=")">
            #{status, jdbcType=VARCHAR}
        </foreach>
    </update>

    <!-- 新增：根据平台项目ID、GitLab项目ID和服务器ID查询处于特定活跃状态的部署记录 -->
    <select id="findActiveDeploymentRecords" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_ops_deployed_container
        WHERE platform_project_id = #{platformProjectId,jdbcType=BIGINT}
        AND gitlab_project_id = #{gitlabProjectId,jdbcType=BIGINT}
        AND server_id = #{serverId,jdbcType=BIGINT}
        AND deleted = 0
        AND deployment_status IN
        <foreach collection="activeStatuses" item="status" open="(" separator="," close=")">
            #{status, jdbcType=VARCHAR}
        </foreach>
        <if test="excludeId != null">
            AND id != #{excludeId,jdbcType=BIGINT}
        </if>
    </select>
</mapper>