<template>
  <div class="home">
    <el-container>
      <el-header>
        <div class="header-content">
          <h1>NYBC Vue3 项目模板</h1>
          <el-space>
            <el-button type="primary" @click="$router.push('/about')">
              <el-icon><InfoFilled /></el-icon>
              关于
            </el-button>
            <el-button @click="toggleTheme">
              <el-icon><Sunny v-if="isDark" /><Moon v-else /></el-icon>
              {{ isDark ? '浅色' : '深色' }}
            </el-button>
          </el-space>
        </div>
      </el-header>
      
      <el-main>
        <div class="main-content">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-card shadow="hover">
                <template #header>
                  <div class="card-header">
                    <el-icon><Vue /></el-icon>
                    <span>Vue 3</span>
                  </div>
                </template>
                <p>基于Vue 3 Composition API构建的现代化前端框架</p>
              </el-card>
            </el-col>
            
            <el-col :span="8">
              <el-card shadow="hover">
                <template #header>
                  <div class="card-header">
                    <el-icon><Lightning /></el-icon>
                    <span>Vite</span>
                  </div>
                </template>
                <p>极速的构建工具，提供快速的开发体验</p>
              </el-card>
            </el-col>
            
            <el-col :span="8">
              <el-card shadow="hover">
                <template #header>
                  <div class="card-header">
                    <el-icon><Grid /></el-icon>
                    <span>Element Plus</span>
                  </div>
                </template>
                <p>基于Vue 3的组件库，提供丰富的UI组件</p>
              </el-card>
            </el-col>
          </el-row>
          
          <el-divider />
          
          <div class="demo-section">
            <h2>功能演示</h2>
            <el-space wrap>
              <el-button type="primary" @click="showMessage">显示消息</el-button>
              <el-button type="success" @click="showNotification">显示通知</el-button>
              <el-button type="warning" @click="openDialog">打开对话框</el-button>
              <el-button type="info" @click="loadData" :loading="loading">
                {{ loading ? '加载中...' : '加载数据' }}
              </el-button>
            </el-space>
          </div>
        </div>
      </el-main>
    </el-container>
    
    <!-- 示例对话框 -->
    <el-dialog v-model="dialogVisible" title="示例对话框" width="30%">
      <p>这是一个示例对话框，展示Element Plus的对话框组件。</p>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="dialogVisible = false">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import { useDark, useToggle } from '@vueuse/core'

const isDark = useDark()
const toggleTheme = useToggle(isDark)

const loading = ref(false)
const dialogVisible = ref(false)

const showMessage = () => {
  ElMessage.success('这是一条成功消息！')
}

const showNotification = () => {
  ElNotification({
    title: '通知',
    message: '这是一条通知消息！',
    type: 'success'
  })
}

const openDialog = () => {
  dialogVisible.value = true
}

const loadData = async () => {
  loading.value = true
  // 模拟API调用
  await new Promise(resolve => setTimeout(resolve, 2000))
  loading.value = false
  ElMessage.success('数据加载完成！')
}
</script>

<style scoped>
.home {
  min-height: 100vh;
  background: var(--el-bg-color-page);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.demo-section {
  text-align: center;
  margin-top: 40px;
}

.demo-section h2 {
  margin-bottom: 20px;
  color: var(--el-text-color-primary);
}
</style>
