package com.nybc.edu.common.enums;

import lombok.Getter;

/**
 * 标签状态枚举
 * <p>
 * 用于管理标签的生命周期状态
 *
 * <AUTHOR>
 * @version 1.0
 */
@Getter
public enum TagStatusEnum {

    /**
     * 正常活跃状态
     * 标签正常使用，可以被搜索和关联
     */
    ACTIVE("ACTIVE", "正常"),

    /**
     * 已废弃状态
     * 标签不再推荐使用，但保留历史关联，不影响现有业务
     */
    DEPRECATED("DEPRECATED", "已废弃"),

    /**
     * 已禁用状态
     * 标签被管理员禁用，不能被使用和搜索
     */
    DISABLED("DISABLED", "已禁用"),

    /**
     * 审核中状态
     * 新创建的标签等待管理员审核
     */
    PENDING("PENDING", "审核中");

    private final String code;
    private final String description;

    TagStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 状态代码
     * @return 对应的枚举值，如果找不到则返回ACTIVE
     */
    public static TagStatusEnum fromCode(String code) {
        if (code == null) {
            return ACTIVE;
        }
        for (TagStatusEnum status : values()) {
            if (status.getCode().equalsIgnoreCase(code)) {
                return status;
            }
        }
        return ACTIVE;
    }

    /**
     * 判断是否为可用状态
     *
     * @return true表示可用，false表示不可用
     */
    public boolean isUsable() {
        return this == ACTIVE || this == DEPRECATED;
    }

} 