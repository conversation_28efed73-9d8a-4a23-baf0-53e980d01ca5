package com.nybc.edu.common.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 类描述：
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Accessors(chain = true)
@Schema(description = "删除文件存储")
public class FileDeleteDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 8560101587645468397L;

    private List<Long> fileIds;

    private Long projectId;

    private Long courseId;

    private Long taskId;

    private Long userId;

}
