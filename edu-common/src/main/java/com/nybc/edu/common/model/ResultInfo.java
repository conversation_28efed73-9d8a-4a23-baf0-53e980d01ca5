package com.nybc.edu.common.model;

import com.nybc.edu.common.enums.ResultCodeEnum;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Objects;

/**
 * 类描述：统一返回
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
public class ResultInfo<T> implements Serializable {
    @Serial
    private static final long serialVersionUID = -7797078966552049205L;

    private boolean success;
    /**
     * 状态码
     */
    private int code;
    /**
     * 提示信息
     */
    private String msg;

    /**
     * 返回的具体内容
     */
    private T data;

    public static <T> ResultInfo<T> result(Integer code) {
        return result(code, null);
    }

    public static <T> ResultInfo<T> resultMsg(Integer code, String msg) {
        ResultInfo<T> resultInfo = new ResultInfo<>();
        resultInfo.setSuccess(Objects.equals(code, ResultCodeEnum.SUCCESS.getCode()));
        resultInfo.setMsg(msg);
        resultInfo.setCode(code);
        return resultInfo;
    }

    public static <T> ResultInfo<T> result(Boolean success) {
        ResultInfo<T> resultInfo = new ResultInfo<>();
        resultInfo.setSuccess(success);
        resultInfo.setCode(success ? ResultCodeEnum.SUCCESS.getCode() : ResultCodeEnum.ERROR.getCode());
        return resultInfo;
    }

    public static <T> ResultInfo<T> result(Integer code, T obj) {
        ResultInfo<T> resultInfo = new ResultInfo<>();
        resultInfo.setSuccess(code.equals(ResultCodeEnum.SUCCESS.getCode()));
        resultInfo.setMsg(ResultCodeEnum.getSign(code));
        resultInfo.setCode(code);
        resultInfo.setData(obj);
        return resultInfo;
    }

    public static <T> ResultInfo<T> success(T data) {
        ResultInfo<T> resultInfo = new ResultInfo<>();
        resultInfo.setSuccess(true);
        resultInfo.setCode(ResultCodeEnum.SUCCESS.getCode());
        resultInfo.setMsg("请求成功");
        resultInfo.setData(data);
        return resultInfo;
    }

    public static <T> ResultInfo<T> success() {
        return success(null);
    }

    public static <T> ResultInfo<T> error() {
        return error(ResultCodeEnum.ERROR.getCode(), "系统异常，请稍后再试");
    }

    public static <T> ResultInfo<T> error(Integer code, String msg) {
        ResultInfo<T> resultInfo = new ResultInfo<>();
        resultInfo.setSuccess(false);
        resultInfo.setCode(code);
        resultInfo.setMsg(msg);
        return resultInfo;
    }

    public static <T> ResultInfo<T> error(Integer code) {
        return error(code, "系统异常，请稍后再试");
    }

    public static <T> ResultInfo<T> error(String msg) {
        return error(99999, msg);
    }

    public static <T> ResultInfo<T> error(int code, String msg) {
        ResultInfo<T> resultInfo = new ResultInfo<>();
        resultInfo.setSuccess(false);
        resultInfo.setCode(code);
        resultInfo.setMsg(msg);
        return resultInfo;
    }

    public static <T> ResultInfo<T> error(Integer code, String msg, T data) {
        ResultInfo<T> resultInfo = new ResultInfo<>();
        resultInfo.setSuccess(false);
        resultInfo.setCode(code);
        resultInfo.setMsg(msg);
        resultInfo.setData(data);
        return resultInfo;
    }

    @SuppressWarnings("unchecked")
    public static <T> ResultInfo<T> emptyList() {
        ResultInfo<T> resultInfo = new ResultInfo<>();
        resultInfo.setSuccess(true);
        resultInfo.setCode(200);
        resultInfo.setData((T) new ArrayList<T>());
        return resultInfo;
    }

}
