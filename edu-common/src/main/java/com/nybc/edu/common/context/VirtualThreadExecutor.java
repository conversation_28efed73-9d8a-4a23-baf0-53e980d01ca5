package com.nybc.common.tool.thread;

import com.alibaba.ttl.threadpool.TtlExecutors;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.*;

/**
 * 类描述：
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
public class VirtualThreadExecutor {
    // 优雅关闭的超时时间（秒）
    private static final long SHUTDOWN_TIMEOUT_SECONDS = 10;

    // 核心执行器：为每个提交的任务创建一个新的虚拟线程。
    // 标记为 final 并在静态代码块中初始化，以保证初始化时的线程安全。
    private static final ExecutorService VIRTUAL_THREAD_EXECUTOR;

    static {
        log.info("正在初始化 VirtualThreadExecutor...");
        // 创建执行器
        ExecutorService virtualThreadPerTaskExecutor = Executors.newVirtualThreadPerTaskExecutor();
        VIRTUAL_THREAD_EXECUTOR = TtlExecutors.getTtlExecutorService(virtualThreadPerTaskExecutor);
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            log.info("VirtualThreadExecutor 的关闭钩子被触发。开始执行关闭...");
            shutdown();
        }, "VirtualThreadExecutorShutdownHook"));
        log.info("已为虚拟线程执行器添加关闭钩子。");
    }

    /**
     * 私有构造函数，防止该工具类被实例化。
     */
    private VirtualThreadExecutor() {
        throw new UnsupportedOperationException("这是一个工具类，不能被实例化");
    }

    /**
     * 在未来的某个时间使用虚拟线程执行给定的命令（Runnable）。
     * 这适用于“发射后不管”（fire and forget）类型的任务，即你不需要跟踪任务的完成状态或结果。
     *
     * @param command 要执行的可运行任务
     * @throws RejectedExecutionException 如果任务无法被接受执行（例如，执行器已关闭）
     * @throws NullPointerException       如果 command 为 null
     */
    public static void execute(Runnable command) {
        try {
            VIRTUAL_THREAD_EXECUTOR.execute(command);
        } catch (RejectedExecutionException e) {
            log.error("任务被虚拟线程执行器拒绝。执行器可能正在关闭。", e);
            // 根据需要，可以重新抛出异常或进行其他处理
            throw e;
        } catch (NullPointerException e) {
            log.error("不能执行 null 命令。", e);
            throw e;
        }
    }

    /**
     * 提交一个返回值的任务（Callable）以在虚拟线程上执行，并返回一个代表任务待定结果的 Future。
     *
     * @param <T>  任务结果的类型
     * @param task 要提交的任务
     * @return 代表任务待定完成状态的 Future
     * @throws RejectedExecutionException 如果任务无法被接受执行（例如，执行器已关闭）
     * @throws NullPointerException       如果 task 为 null
     */
    public static <T> Future<T> submit(Callable<T> task) {
        try {
            return VIRTUAL_THREAD_EXECUTOR.submit(task);
        } catch (RejectedExecutionException e) {
            log.error("Callable 任务被虚拟线程执行器拒绝。执行器可能正在关闭。", e);
            throw e;
        } catch (NullPointerException e) {
            log.error("不能提交 null 的 Callable 任务。", e);
            throw e;
        }
    }

    /**
     * 提交一个 Runnable 任务以在虚拟线程上执行，并返回一个代表该任务的 Future。
     * 成功完成后，该 Future 的 {@code get} 方法将返回 {@code null}。
     *
     * @param task 要提交的任务
     * @return 代表任务待定完成状态的 Future
     * @throws RejectedExecutionException 如果任务无法被接受执行（例如，执行器已关闭）
     * @throws NullPointerException       如果 task 为 null
     */
    public static Future<?> submit(Runnable task) {
        try {
            return VIRTUAL_THREAD_EXECUTOR.submit(task);
        } catch (RejectedExecutionException e) {
            log.error("Runnable 任务被虚拟线程执行器拒绝。执行器可能正在关闭。", e);
            throw e;
        } catch (NullPointerException e) {
            log.error("不能提交 null 的 Runnable 任务。", e);
            throw e;
        }
    }

    /**
     * 提交一个 Runnable 任务以在虚拟线程上执行，并返回一个代表该任务的 Future。
     * 成功完成后，该 Future 的 {@code get} 方法将返回给定的结果。
     *
     * @param <T>    结果的类型
     * @param task   要提交的任务
     * @param result 要返回的结果
     * @return 代表任务待定完成状态的 Future
     * @throws RejectedExecutionException 如果任务无法被接受执行（例如，执行器已关闭）
     * @throws NullPointerException       如果 task 为 null
     */
    public static <T> Future<T> submit(Runnable task, T result) {
        try {
            return VIRTUAL_THREAD_EXECUTOR.submit(task, result);
        } catch (RejectedExecutionException e) {
            log.error("带结果的 Runnable 任务被虚拟线程执行器拒绝。执行器可能正在关闭。", e);
            throw e;
        } catch (NullPointerException e) {
            log.error("不能提交 null 的 Runnable 任务（带结果）。", e);
            throw e;
        }
    }

    /**
     * 执行给定的任务集合，并在所有任务完成时返回一个包含它们状态和结果的 Future 列表。
     * 使用虚拟线程执行这些任务。
     *
     * @param <T>   结果的类型
     * @param tasks 任务集合
     * @return 一个 Future 列表，代表这些任务，其顺序与给定任务集合的迭代器产生的顺序相同。
     * @throws InterruptedException 如果在等待时被中断，此时未完成的任务将被取消
     * @throws NullPointerException 如果 tasks 或其任何元素为 {@code null}
     * @throws RejectedExecutionException 如果有任何任务无法被调度执行
     */
    public static <T> List<Future<T>> invokeAll(Collection<? extends Callable<T>> tasks) throws InterruptedException {
        try {
            // 注意：invokeAll 会阻塞，直到所有任务完成或超时
            return VIRTUAL_THREAD_EXECUTOR.invokeAll(tasks);
        } catch (RejectedExecutionException e) {
            log.error("invokeAll 期间一个或多个任务被虚拟线程执行器拒绝。执行器可能正在关闭。", e);
            throw e;
        } catch (NullPointerException e) {
            log.error("不能使用 null 任务集合或 null 任务调用 invokeAll。", e);
            throw e;
        }
    }

    // --- 关闭逻辑 ---

    /**
     * 发起一个有序的关闭过程，在此过程中，先前提交的任务会被执行，但不会接受任何新任务。
     * 如果执行器已经关闭，则再次调用此方法无效。
     * <p>
     * 此方法不会等待先前提交的任务完成执行。请使用 {@link } 来等待。
     * </p>
     * <p>
     * 该方法会通过关闭钩子自动调用，但如果需要提前关闭，也可以手动调用。
     * </p>
     */
    public static void shutdown() {
        // 检查执行器是否为 null 或已关闭
        if (VIRTUAL_THREAD_EXECUTOR == null || VIRTUAL_THREAD_EXECUTOR.isShutdown()) {
            log.warn("虚拟线程执行器为 null 或已经关闭。");
            return;
        }

        log.info("正在关闭虚拟线程执行器。将不再接受新任务。");
        // 阻止提交新任务
        VIRTUAL_THREAD_EXECUTOR.shutdown();

        try {
            // 等待一段时间让现有任务终止
            log.info("等待现有任务终止（超时时间：{} 秒）...", SHUTDOWN_TIMEOUT_SECONDS);
            if (!VIRTUAL_THREAD_EXECUTOR.awaitTermination(SHUTDOWN_TIMEOUT_SECONDS, TimeUnit.SECONDS)) {
                // 如果超时后仍未终止
                log.warn("执行器在 {} 秒内未能终止。将强制关闭...", SHUTDOWN_TIMEOUT_SECONDS);
                // 尝试取消正在执行的任务，并返回等待队列中的任务列表
                List<Runnable> droppedTasks = VIRTUAL_THREAD_EXECUTOR.shutdownNow();
                log.warn("强制关闭导致 {} 个等待执行的任务被取消。", droppedTasks.size());
                // 在强制关闭后再次等待终止
                if (!VIRTUAL_THREAD_EXECUTOR.awaitTermination(SHUTDOWN_TIMEOUT_SECONDS, TimeUnit.SECONDS)) {
                    log.error("执行器即使在强制关闭后也未能终止。");
                } else {
                    log.info("执行器在强制关闭后成功终止。");
                }
            } else {
                // 如果在超时时间内成功终止
                log.info("执行器已优雅地终止。");
            }
        } catch (InterruptedException ie) {
            // 如果等待终止的过程被中断
            log.error("等待终止时被中断。将强制关闭。", ie);
            // 再次尝试取消任务
            VIRTUAL_THREAD_EXECUTOR.shutdownNow();
            // 保持中断状态
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 返回底层的 ExecutorService 实例。
     * 请谨慎使用，优先使用本工具类提供的静态方法。
     * 主要用于高级用例或与期望 ExecutorService 的库集成。
     *
     * @return ExecutorService 实例
     */
    public static ExecutorService getExecutorService() {
        return VIRTUAL_THREAD_EXECUTOR;
    }

    // --- 示例用法 (可选 - 可以移除或放在单独的测试类中) ---
    public static void main(String[] args) throws InterruptedException, ExecutionException {
        log.info("开始 VirtualThreadExecutor 示例...");

        // 示例 1: 发射后不管
        VirtualThreadExecutor.execute(() -> {
            log.info("任务 1 (execute) 运行在线程: {}", Thread.currentThread());
            try { TimeUnit.MILLISECONDS.sleep(50); } catch (InterruptedException ignored) {}
        });

        // 示例 2: 提交 Runnable 并获取 Future
        Future<?> future2 = VirtualThreadExecutor.submit(() -> {
            log.info("任务 2 (submit Runnable) 运行在线程: {}", Thread.currentThread());
            try { TimeUnit.MILLISECONDS.sleep(100); } catch (InterruptedException ignored) {}
        });

        // 示例 3: 提交 Callable 并获取结果
        Future<String> future3 = VirtualThreadExecutor.submit(() -> {
            log.info("任务 3 (submit Callable) 运行在线程: {}", Thread.currentThread());
            TimeUnit.MILLISECONDS.sleep(150);
            return "任务 3 的结果";
        });

        // 等待结果 (可选)
        future2.get();
        log.info("任务 2 已完成。");

        String result3 = future3.get();
        log.info("任务 3 已完成，结果为: {}", result3);

        // 示例 4: invokeAll
        List<Callable<Integer>> tasks = List.of(
                () -> { log.info("InvokeAll 任务 A 运行在线程 {}", Thread.currentThread()); TimeUnit.MILLISECONDS.sleep(50); return 1; },
                () -> { log.info("InvokeAll 任务 B 运行在线程 {}", Thread.currentThread()); TimeUnit.MILLISECONDS.sleep(80); return 2; }
                                               );
        log.info("通过 invokeAll 提交任务...");
        List<Future<Integer>> results = VirtualThreadExecutor.invokeAll(tasks);
        log.info("invokeAll 已完成。");
        for (Future<Integer> f : results) {
            log.info("InvokeAll 结果: {}", f.get());
        }

        log.info("示例运行结束。等待关闭钩子触发或手动关闭...");
    }
    /**
     * 并发执行一个 Runnable 任务集合中的所有任务，并阻塞当前线程直到所有任务执行完成。
     * 这个方法适用于执行多个没有返回值的后台操作，并需要等待它们全部结束后再继续主流程的场景。
     *
     * @param tasks 要执行的 Runnable 任务集合。如果为 null 或空集合，则方法立即返回。
     * @throws InterruptedException 如果当前线程在等待任务完成时被中断。
     */
    public static void executeAllAndWait(Collection<? extends Runnable> tasks) throws InterruptedException {
        if (tasks == null || tasks.isEmpty()) {
            log.debug("任务集合为 null 或空，无需执行或等待。");
            return;
        }
        if (VIRTUAL_THREAD_EXECUTOR.isShutdown()){
            log.warn("执行器已关闭，无法提交新任务。");
            // 或者可以抛出 RejectedExecutionException
            throw new RejectedExecutionException("Executor is shut down, cannot accept new tasks.");
        }

        int taskCount = tasks.size();
        log.debug("准备执行并等待 {} 个 Runnable 任务完成...", taskCount);
        List<Future<?>> futures = new ArrayList<>(taskCount);

        // 1. 提交所有任务
        try {
            for (Runnable task : tasks) {
                if (task == null) {
                    log.warn("任务集合中包含 null 任务，已跳过。");
                    continue; // 跳过 null 任务
                }
                // 使用 submit(Runnable) 获取 Future
                futures.add(VIRTUAL_THREAD_EXECUTOR.submit(task));
            }
        } catch (RejectedExecutionException e) {
            log.error("提交任务时被拒绝，执行器可能正在关闭。", e);
            // 如果在提交过程中就失败，可能需要取消已提交的（虽然比较难）
            // 这里我们先抛出异常，让调用者知道提交未完全成功
            throw e;
        }

        int successfullySubmittedCount = futures.size();
        if (successfullySubmittedCount < taskCount) {
            log.warn("实际成功提交的任务数 ({}) 少于集合中的任务数 ({})，可能因为包含 null 任务。", successfullySubmittedCount, taskCount);
        }
        if (successfullySubmittedCount == 0) {
            log.debug("没有成功提交任何任务，方法返回。");
            return; // 如果一个都没提交成功（比如全是null），直接返回
        }

        log.debug("已成功提交 {} 个任务，开始等待完成...", successfullySubmittedCount);
        // 2. 等待所有已提交的任务完成
        // 注意：我们将 InterruptedException 抛出给调用者处理
        for (Future<?> future : futures) {
            try {
                // future.get() 会阻塞直到任务完成
                future.get();
            } catch (ExecutionException e) {
                e.printStackTrace();
                // 任务本身执行时抛出了异常
                log.error("等待的任务在执行期间抛出异常: {}", e.getCause() != null ? e.getCause().getMessage() : e.getMessage());
                // 继续等待其他任务完成
            }

        }

        log.debug("所有 {} 个已提交的任务均已完成（或记录了异常）。", successfullySubmittedCount);
    }
}
