package com.nybc.edu.common.enums;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 类描述：考核类型枚举
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Getter
@AllArgsConstructor
@Schema(description = "考核类型枚举")
public enum AssessmentTypeEnum {

    QUIZ("QUIZ", "随堂测验"),
    HOMEWORK("HOMEWORK", "作业"),
    MIDTERM_EXAM("MIDTERM_EXAM", "期中考试"),
    FINAL_EXAM("FINAL_EXAM", "期末考试"),
    PROJECT("PROJECT", "课程项目"),
    PRESENTATION("PRESENTATION", "演示/答辩"),
    PARTICIPATION("PARTICIPATION", "课堂参与"),
    LAB_REPORT("LAB_REPORT", "实验报告"),
    RETAKE_EXAM("RETAKE_EXAM", "补考"),
    OTHER("OTHER", "其他");

    @JSONField
    private final String code;
    private final String description;

    public static AssessmentTypeEnum fromCode(String code) {
        return Arrays.stream(AssessmentTypeEnum.values())
                .filter(e -> e.getCode().equalsIgnoreCase(code))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("无效的考核类型编码: " + code));
    }
}
